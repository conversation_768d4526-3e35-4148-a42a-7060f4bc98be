using UnityEngine;
using Unity.Netcode;
using System.Collections.Generic;
using System.Linq;

public class SpawnManager : NetworkBehaviour
{
    public static SpawnManager Instance { get; private set; }

    [Header("Başlangıç Noktaları")]
    [Tooltip("Oyuncu HİÇ checkpoint almadan düşerse bu noktalara döner.")]
    [SerializeField] private Transform[] initialSpawnPoints;

    [Header("Checkpoint'ler")]
    [Toolt<PERSON>("Sahnedeki TÜM checkpoint objelerini buraya sürükleyin.")]
    [SerializeField] private Checkpoint[] checkpoints;

    // nextInitialSpawnIndex ve GetInitialSpawnPoint metotları kaldırıldı.

    private void Awake()
    {
        // ⚠️ KRİTİK DÜZELTME: NetworkObject'leri client'ta asla destroy etme! ⚠️
        if (Instance != null && Instance != this)
        {
            if (NetworkManager.Singleton != null && NetworkManager.Singleton.IsListening)
            {
                // Network aktifse, sadece server destroy edebilir
                if (IsServer)
                {
                    Debug.Log($"🗑️ SpawnManager: Server'da duplicate instance destroy ediliyor");
                    Destroy(gameObject);
                }
                else
                {
                    // Client'ta ise sadece Instance'ı güncelle, destroy etme
                    Debug.LogWarning($"⚠️ SpawnManager: Client'ta duplicate instance bulundu, Instance güncelleniyor");
                    Instance = this;
                }
            }
            else
            {
                // Network aktif değilse (offline mod) normal destroy
                Debug.Log($"🗑️ SpawnManager: Offline modda duplicate instance destroy ediliyor");
                Destroy(gameObject);
            }
        }
        else
        {
            Instance = this;
        }
    }

    public void UpdatePlayerCheckpoint(ulong clientId, int checkpointId)
    {
        if (!IsServer) return;

        NetworkObject oyuncuNetworkObject = NetworkManager.Singleton.ConnectedClients[clientId].PlayerObject;
        if (oyuncuNetworkObject != null)
        {
            OyuncuKontrol oyuncu = oyuncuNetworkObject.GetComponent<OyuncuKontrol>();
            if (oyuncu.lastCheckpointID.Value < checkpointId)
            {
                oyuncu.lastCheckpointID.Value = checkpointId;
            }
        }
    }
    
    public void RespawnPlayer(OyuncuKontrol playerController)
    {
        if (!IsServer) return;
        Transform spawnPoint = GetSpawnPointForPlayer(playerController);
        playerController.Teleport(spawnPoint.position, spawnPoint.rotation);
    }
    
    private Transform GetSpawnPointForPlayer(OyuncuKontrol playerController)
    {
        int checkpointId = playerController.lastCheckpointID.Value;

        if (checkpointId != -1)
        {
            Checkpoint checkpoint = checkpoints.FirstOrDefault(c => c.checkpointID == checkpointId);
            if (checkpoint != null) return checkpoint.transform;
        }
        
        if (initialSpawnPoints.Length > 0)
        {
            return initialSpawnPoints[Random.Range(0, initialSpawnPoints.Length)];
        }

        Debug.LogError("Sahnede HİÇBİR spawn noktası veya checkpoint tanımlanmamış!");
        return transform;
    }
}