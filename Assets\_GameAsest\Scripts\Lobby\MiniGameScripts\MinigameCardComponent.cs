using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// MinigameCard prefab'ına eklenen component
/// Prefab'daki tüm UI referanslarını tutar
/// </summary>
public class MinigameCardComponent : MonoBehaviour
{
    [<PERSON><PERSON>("UI Referansları")]
    [Toolt<PERSON>("Arka plan image")]
    public Image backgroundImage;
    
    [<PERSON>lt<PERSON>("Önizleme resmi")]
    public Image previewImage;
    
    [Tooltip("Seçili durumu göstergesi")]
    public GameObject seciliMi;
    
    [Tooltip("Minigame adı text'i")]
    public TextMeshProUGUI minigameNameText;
    
    [Tooltip("Ana button component")]
    public Button mainButton;

    [<PERSON><PERSON>("Görsel Ayarlar")]
    [Tooltip("Normal durum rengi")]
    public Color normalColor = Color.white;
    
    [Tooltip("Seçili durum rengi")]
    public Color selectedColor = Color.green;

    /// <summary>
    /// Kartın seçili durumunu ayarla
    /// </summary>
    public void SetSelected(bool selected)
    {
        if (seciliMi != null)
        {
            seciliMi.SetActive(selected);
        }
        
        if (backgroundImage != null)
        {
            backgroundImage.color = selected ? selectedColor : normalColor;
        }
    }
    
    /// <summary>
    /// Preview image'i ayarla
    /// </summary>
    public void SetPreviewSprite(Sprite sprite)
    {
        if (previewImage != null)
        {
            if (sprite != null)
            {
                previewImage.sprite = sprite;
                previewImage.color = Color.white; // Normal renk
                previewImage.gameObject.SetActive(true);
            }
            else
            {
                previewImage.sprite = null;
                previewImage.color = Color.clear; // Görünmez yap
                previewImage.gameObject.SetActive(false);
            }
        }
    }
    
    /// <summary>
    /// Minigame adını ayarla
    /// </summary>
    public void SetMinigameName(string name)
    {
        if (minigameNameText != null)
        {
            minigameNameText.text = name;
        }
    }
    
    /// <summary>
    /// Button click event'ini ayarla
    /// </summary>
    public void SetButtonClickAction(System.Action action)
    {
        if (mainButton != null)
        {
            mainButton.onClick.RemoveAllListeners();
            mainButton.onClick.AddListener(() => action?.Invoke());
        }
    }

    /// <summary>
    /// Tüm referansları otomatik bul (opsiyonel)
    /// </summary>
    [ContextMenu("Referansları Otomatik Bul")]
    public void FindReferences()
    {
        // Ana button
        if (mainButton == null)
            mainButton = GetComponent<Button>();
        
        // Background image
        if (backgroundImage == null)
            backgroundImage = GetComponent<Image>();
        
        // Child'larda ara
        Transform[] children = GetComponentsInChildren<Transform>();
        
        foreach (Transform child in children)
        {
            string childName = child.name.ToLower();
            
            // PreviewImage
            if (previewImage == null && childName.Contains("preview"))
            {
                previewImage = child.GetComponent<Image>();
            }
            
            // SeciliMi
            if (seciliMi == null && (childName.Contains("secili") || childName.Contains("selected")))
            {
                seciliMi = child.gameObject;
            }
            
            // MinigameNameText
            if (minigameNameText == null && (childName.Contains("name") || childName.Contains("text")))
            {
                minigameNameText = child.GetComponent<TextMeshProUGUI>();
            }
        }
        
        Debug.Log("✅ MinigameCardComponent: Referanslar otomatik bulundu!");
    }

    /// <summary>
    /// Referansları kontrol et
    /// </summary>
    public bool ValidateReferences()
    {
        bool isValid = true;
        
        if (mainButton == null)
        {
            Debug.LogWarning("⚠️ MainButton referansı eksik!");
            isValid = false;
        }
        
        if (backgroundImage == null)
        {
            Debug.LogWarning("⚠️ BackgroundImage referansı eksik!");
            isValid = false;
        }
        
        if (minigameNameText == null)
        {
            Debug.LogWarning("⚠️ MinigameNameText referansı eksik!");
            isValid = false;
        }
        
        // Opsiyonel olanlar için sadece bilgi
        if (previewImage == null)
        {
            Debug.Log("ℹ️ PreviewImage referansı yok (opsiyonel)");
        }
        
        if (seciliMi == null)
        {
            Debug.Log("ℹ️ SeciliMi referansı yok (opsiyonel)");
        }
        
        return isValid;
    }

#if UNITY_EDITOR
    /// <summary>
    /// Inspector'da referansları kontrol et
    /// </summary>
    private void OnValidate()
    {
        // Editor'da değişiklik olduğunda otomatik kontrol
        if (Application.isPlaying) return;
        
        UnityEditor.EditorApplication.delayCall += () =>
        {
            if (this != null)
            {
                ValidateReferences();
            }
        };
    }
#endif
}
