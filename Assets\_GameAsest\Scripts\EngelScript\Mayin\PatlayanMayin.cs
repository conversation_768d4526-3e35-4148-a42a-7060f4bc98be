using UnityEngine;
using Unity.Netcode;

public class MayinController : NetworkBehaviour
{
    [Head<PERSON>("Collider Referansları")]
    public BoxCollider armingCollider;
    public SphereCollider detonationCollider;

    [Header("<PERSON><PERSON><PERSON>")]
    private bool isArmed = false;
    public GameObject explosionEffect;
    
    // --- DEĞİŞİKLİK: <PERSON><PERSON>a ayarları daha belirgin hale getirildi ve ragdoll süresi eklendi ---
    [Head<PERSON>("Patlama Ayarları")]
    [Tooltip("Patlamanın karaktere uygulayacağı itme kuvveti.")]
    public float explosionForce = 1500f;
    [Tooltip("Patlamanın etki edeceği alanın yarıçapı.")]
    public float explosionRadius = 7f;
    [Tooltip("Ragdoll'un aktif kalacağı minimum süre.")]
    public float ragdollDuration = 2f; // YENİ: RagdollManager'a gönderilecek süre

    [Header("Görsel Geri Bildirim")]
    public Material unarmedMaterial;
    public Material armedMaterial;
    private MeshRenderer meshRenderer;

    void Start()
    {
        isArmed = false;
        meshRenderer = GetComponent<MeshRenderer>();
        if (meshRenderer != null && unarmedMaterial != null)
        {
            meshRenderer.material = unarmedMaterial;
        }

        if (armingCollider == null || detonationCollider == null)
        {
            Debug.LogError("Mayın objesindeki Collider referansları atanmamış!");
            return;
        }
        armingCollider.isTrigger = true;
        detonationCollider.isTrigger = true;
    }

    private void OnTriggerEnter(Collider other)
    {
        if (!other.CompareTag("Player"))
        {
            return;
        }

        if (armingCollider.bounds.Intersects(other.bounds))
        {
            if (!isArmed)
            {
                ArmMine();
            }
        }

        if (detonationCollider.bounds.Intersects(other.bounds))
        {
            if (isArmed)
            {
                // --- DEĞİŞİKLİK: Explode fonksiyonuna patlamayı tetikleyen karakterin bilgisini gönderiyoruz ---
                Explode(other); 
            }
        }
    }

    private void ArmMine()
    {
        Debug.Log("Mayın Aktif Hale Geldi!");
        isArmed = true;

        if (meshRenderer != null && armedMaterial != null)
        {
            meshRenderer.material = armedMaterial;
        }
    }

    // --- DEĞİŞİKLİK: Explode fonksiyonu artık hangi karakteri patlattığını biliyor ---
    private void Explode(Collider playerCollider)
    {
        Debug.Log("BOOM! Mayın Patladı!");

        // 1. Patlama efektini oluştur
        if (explosionEffect != null)
        {
            Instantiate(explosionEffect, transform.position, Quaternion.identity);
        }

        // 2. Patlayan karakterin RagdollManager script'ini bul
        //    GetComponentInParent kullanıyoruz çünkü collider karakterin bir alt objesi olabilir.
        RagdollManager ragdollManager = playerCollider.GetComponentInParent<RagdollManager>();

        // 3. Script'i bulduysak, ragdoll'u aktif etme fonksiyonunu çağır
        if (ragdollManager != null)
        {
            Debug.Log(playerCollider.name + " üzerinde RagdollManager bulundu ve tetikleniyor.");
            // Gönderdiğiniz script'teki doğru fonksiyonu çağırıyoruz: PatlamaEtkisiyleRagdollAktifEt
            ragdollManager.PatlamaEtkisiyleRagdollAktifEt(transform.position, explosionForce, ragdollDuration);
        }
        else
        {
            Debug.LogWarning(playerCollider.name + " üzerinde RagdollManager script'i bulunamadı!");
        }

        // 4. Patlamadan sonra mayın objesini yok et
        // ÇÖZÜM 1 & 2: NetworkObject için doğru yok etme yöntemi
        if (IsServer)
        {
            // Server'daysa direkt despawn et
            NetworkObject networkObject = GetComponent<NetworkObject>();
            if (networkObject != null && networkObject.IsSpawned)
            {
                Debug.Log($"🗑️ PatlayanMayin server'da despawn ediliyor: {name}");
                networkObject.Despawn(true);
            }
            else
            {
                // NetworkObject değilse normal destroy
                Debug.Log($"🗑️ PatlayanMayin normal destroy: {name}");
                Destroy(gameObject);
            }
        }
        else
        {
            // Client'taysa server'a haber ver
            Debug.Log($"📡 PatlayanMayin client'tan server'a yok etme isteği: {name}");
            YokEtBeniServerRpc();
        }
    }

    /// <summary>
    /// ÇÖZÜM 2: Client'tan server'a "beni yok et" isteği
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    private void YokEtBeniServerRpc()
    {
        if (!IsServer) return;

        Debug.Log($"📡 PatlayanMayin: Client'tan yok etme emri alındı: {name}");

        NetworkObject networkObject = GetComponent<NetworkObject>();
        if (networkObject != null && networkObject.IsSpawned)
        {
            networkObject.Despawn(true);
        }
        else
        {
            Destroy(gameObject);
        }
    }
}