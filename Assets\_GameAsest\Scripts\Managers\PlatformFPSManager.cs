using UnityEngine;

/// <summary>
/// Platform bazlı FPS yöneticisi.
/// Mobil cihazlarda 90 FPS'e sabitler, PC'de sınırsız FPS kullanır.
/// Fizik timestep'i de hedef FPS'e göre ayarlar.
/// </summary>
public class PlatformFPSManager : MonoBehaviour
{
    // Singleton instance
    private static PlatformFPSManager instance;
    
    [Header("FPS Ayarları")]
    [Tooltip("Mobil cihazlar için hedef FPS")]
    public int mobileFPS = 60;
    
    [Tooltip("PC için hedef FPS (-1 = sınırsız)")]
    public int pcFPS = -1;
    
    [Header("Fizik Ayarları")]
    [Tooltip("Fizik timestep'i otomatik ayarlansın mı?")]
    public bool adjustPhysicsTimestep = true;
    
    // Uygulanan FPS değerini tutacak değişken
    private int appliedFPS;
    
    // Singleton pattern
    public static PlatformFPSManager Instance
    {
        get { return instance; }
    }
    
    private void Awake()
    {
        // Singleton pattern: Sahnede zaten bir instance varsa yenisini yok et
        if (instance != null && instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        instance = this;
        DontDestroyOnLoad(gameObject);
        
        // Platform bazlı FPS ayarlarını uygula
        ApplyPlatformSpecificSettings();
    }
    
    /// <summary>
    /// Platform bazlı FPS ve fizik ayarlarını uygular
    /// </summary>
    private void ApplyPlatformSpecificSettings()
    {
        // Mobil platform kontrolü (runtime + derleme zamanı)
        bool isMobilePlatform =
#if UNITY_ANDROID || UNITY_IOS
            true ||
#endif
            Application.isMobilePlatform ||
            SystemInfo.deviceType == DeviceType.Handheld;
        
        // Hedef FPS'i belirle
        appliedFPS = isMobilePlatform ? mobileFPS : pcFPS;
        
        // VSync'i kapat (FPS limitini manuel kontrol etmek için)
        QualitySettings.vSyncCount = 0;
        
        // Hedef FPS'i ayarla
        Application.targetFrameRate = appliedFPS;
        
        // Fizik timestep'i ayarla (eğer etkinse)
        if (adjustPhysicsTimestep && appliedFPS > 0)
        {
            // Fizik timestep'i FPS'e göre hesapla (1/FPS)
            Time.fixedDeltaTime = 1.0f / appliedFPS;
            
            // Fizik timestep'i çok küçük olmasın (performans için)
            Time.fixedDeltaTime = Mathf.Max(Time.fixedDeltaTime, 0.01f);
            
            Debug.Log($"[PlatformFPSManager] Fizik timestep: {Time.fixedDeltaTime}");
        }
        
        // Uygulanan ayarları logla
        Debug.Log($"[PlatformFPSManager] Platform: {(isMobilePlatform ? "Mobil" : "PC")}, " +
                  $"Hedef FPS: {(appliedFPS == -1 ? "Sınırsız" : appliedFPS.ToString())}, " +
                  $"VSync: Kapalı");
    }
    
    /// <summary>
    /// Hedef FPS'i manuel olarak değiştirmek için kullanılabilir
    /// </summary>
    public void SetTargetFPS(int newFPS)
    {
        appliedFPS = newFPS;
        Application.targetFrameRate = appliedFPS;
        
        // Fizik timestep'i de güncelle (eğer etkinse)
        if (adjustPhysicsTimestep && appliedFPS > 0)
        {
            Time.fixedDeltaTime = 1.0f / appliedFPS;
            Time.fixedDeltaTime = Mathf.Max(Time.fixedDeltaTime, 0.01f);
        }
        
        Debug.Log($"[PlatformFPSManager] Hedef FPS manuel olarak değiştirildi: " +
                  $"{(appliedFPS == -1 ? "Sınırsız" : appliedFPS.ToString())}");
    }
    
    /// <summary>
    /// Uygulanan FPS değerini döndürür
    /// </summary>
    public int GetCurrentFPS()
    {
        return appliedFPS;
    }
    
    /// <summary>
    /// Mevcut gerçek FPS değerini döndürür (ortalama)
    /// </summary>
    public float GetRealFPS()
    {
        return 1.0f / Time.deltaTime;
    }
}
