using UnityEngine;
using System.Collections.Generic;
using Unity.Netcode;
using UnityEngine.InputSystem;
using System.Linq;

// NetworkBehaviour'dan kalıtım alması sağlandı.
public class ZeminOlusturucu : NetworkBehaviour
{
    [Header("<PERSON><PERSON><PERSON>ları")]
    public int genislik = 12;
    public int uzunluk = 25;
    public float karoArasiMesafe = 2f;
    public GameObject zeminKarosuPrefab;

    [Header("Rastgele Yol Ayarları")]
    [Tooltip("Her oyunda rastgele yol parametreleri kullanılsın mı?")]
    public bool rastgeleYolParametreleri = true;

    [Header("Gelişmiş Yol Ayarları (Rastgele Kapalıysa)")]
    [Tooltip("Pist kaç adımda bir yön değiştirmeye çalışsın? Değer arttıkça yol düzleşir.")]
    public int donusSikligi = 3;
    [Tooltip("<PERSON> yolun geniş<PERSON>ği (Sabit 1 - Daha tahmin edilemez)")]
    [Range(1, 1)]
    public int anaYolGenisligi = 1;
    [Tooltip("Yolun ne kadar zigzag yapacağı (0-100, yüksek değer daha çok zigzag)")]
    [Range(0, 100)]
    public int zigzagSiddeti = 50;
    [Tooltip("Alternatif yol dallarının oluşturulma şansı (0-100)")]
    [Range(0, 100)]
    public int alternatifYolSansi = 30;

    [Header("Sahte Yol Ayarları (Rastgele Kapalıysa)")]
    [Tooltip("Oluşturulacak maksimum sahte çıkmaz yol sayısı.")]
    [Range(0, 15)]
    public int sahteYolSayisi = 5;
    [Tooltip("Sahte yolların minimum uzunluğu.")]
    public int minSahteYolUzunlugu = 2;
    [Tooltip("Sahte yolların maksimum uzunluğu.")]
    public int maxSahteYolUzunlugu = 6;
    [Tooltip("Sahte yolların dallanma şansı (0-100)")]
    [Range(0, 100)]
    public int sahteYolDallanmaSansi = 40;

    [Header("Sahte Başlangıç Ayarları (Rastgele Kapalıysa)")]
    [Tooltip("Başlangıçta kaç tane sahte 'kapı' (yol başlangıcı) olacak?")]
    [Range(0, 8)]
    public int sahteBaslangicSayisi = 3;
    [Tooltip("Sahte başlangıç yollarının minimum ileriye doğru uzunluğu (başlangıç karosu hariç).")]
    public int minSahteBaslangicUzunlugu = 2;
    [Tooltip("Sahte başlangıç yollarınun maksimum ileriye doğru uzunluğu (başlangıç karosu hariç).")]
    public int maxSahteBaslangicUzunlugu = 8;

    [Header("Görsel Tema Ayarları")]
    [Tooltip("Burada farklı görsel temalar oluşturabilirsiniz.")]
    public List<ZeminTemasi> kullanilabilirTemalar;
    [Tooltip("Kullanılacak temanın listedeki sırası (0'dan başlar).")]
    public int seciliTemaIndex = 0;

    // --- Private Değişkenler ---
    private HashSet<Vector2Int> guvenliYolKoordinatlari;
    // Karo listesi artık sadece sunucuda tutuluyor.
    private readonly List<ZeminKarosu> tumKaroScriptler = new List<ZeminKarosu>();
    private ZeminTemasi aktifTema;
    private InputAction debugAction;

    // --- YENİ NETWORK DEĞİŞKENİ ---
    // Debug modunun aktif olup olmadığını tüm istemcilere senkronize eder.
    public readonly NetworkVariable<bool> isDebugModeActive = new NetworkVariable<bool>(false);
    
    // Diğer scriptlerin bu objeye kolayca erişmesi için Singleton yapısı
    public static ZeminOlusturucu Instance { get; private set; }

    private void Awake()
    {
        // Singleton deseni
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
        }
        else
        {
            Instance = this;
        }
    }
    
    // Start yerine OnNetworkSpawn kullanıldı. Obje ağa dahil olduğunda çalışır.
    public override void OnNetworkSpawn()
    {
        // Bu bloğun içindeki kodlar sadece SUNUCUDA çalışır.
        // İstemciler zemin oluşturma mantığını çalıştırmaz, sadece sunucunun oluşturduğu karoları görür.
        if (!IsServer)
        {
            // Debug modu için istemcilerin de tuşa basabilmesi gerekir.
            DebugModunuAyarla();

            // ⚠️ SENİN PRENSİBİN: Client'ta zamanlayıcı başlatma! ⚠️
            // StartCoroutine(ClientKaroKontrolCoroutine()); // KALDIRILDI!

            // Bunun yerine sadece bir kez kontrol et
            Invoke(nameof(ClientKaroTekKontrol), 1f);
            return;
        }

        if (!TemaGecerliMi())
        {
            Debug.LogError("❌ ZeminOlusturucu: Tema geçerli değil!");
            return;
        }
        if (zeminKarosuPrefab == null)
        {
            Debug.LogError("❌ ZeminOlusturucu: Zemin Karosu Prefab'ı atanmamış!");
            return;
        }

        // Server'da tüm oyuncuların sahne yüklenmesini bekle
        StartCoroutine(TumOyuncularinSahneYuklenmesinibekle());
    }

    bool TemaGecerliMi()
    {
        if (kullanilabilirTemalar.Count > 0 && seciliTemaIndex < kullanilabilirTemalar.Count)
        {
            aktifTema = kullanilabilirTemalar[seciliTemaIndex];
            
            // --- DEĞİŞİKLİK BURADA ---
            // Artık 3 ayrı materyali değil, SADECE ana materyalin atanıp atanmadığını kontrol ediyoruz.
            if (aktifTema.anaMateryal == null) 
            {
                Debug.LogError($"'{aktifTema.temaAdi}' temasında Ana Materyal (anaMateryal) alanı boş bırakılmış!");
                return false;
            }
            return true;
        }
        else
        {
            Debug.LogError("Geçerli bir tema seçilmedi veya tema listesi boş!");
            return false;
        }
    }

    void GuvenliYolOlustur()
    {
        guvenliYolKoordinatlari = new HashSet<Vector2Int>();

        // Rastgele parametreler kullanılacaksa ayarla
        if (rastgeleYolParametreleri)
        {
            RastgeleParametreleriAyarla();
        }

        // Yeni gelişmiş yol oluşturma sistemi
        List<Vector2Int> anaYollar = GelismisAnaYolOlustur();
        guvenliYolKoordinatlari.UnionWith(anaYollar);

        // Alternatif yol dalları ekle
        AlternatifYollarEkle(anaYollar);

        // Gelişmiş sahte yollar ekle
        GelismisSahteYollarEkle(anaYollar);

        // Gelişmiş sahte başlangıçlar ekle
        GelismisSahteBaslangiclarEkle(anaYollar);
    }

    private void RastgeleParametreleriAyarla()
    {
        // Ana yol parametreleri - Zigzag garantili
        donusSikligi = Random.Range(1, 3); // 1-2 arası (daha sık dönüş)
        anaYolGenisligi = 1; // HEP 1 - Tek şerit
        zigzagSiddeti = Random.Range(80, 101); // 80-100 arası (çok yüksek zigzag şansı)
        alternatifYolSansi = Random.Range(40, 81); // 40-80 arası (daha çok alternatif)

        // Sahte yol parametreleri - Daha fazla ve uzun
        sahteYolSayisi = Random.Range(5, 11); // 5-10 arası (daha fazla sahte yol)
        minSahteYolUzunlugu = Random.Range(3, 6); // 3-5 arası
        maxSahteYolUzunlugu = Random.Range(7, 13); // 7-12 arası
        sahteYolDallanmaSansi = Random.Range(40, 81); // 40-80 arası (daha çok dallanma)

        // Sahte başlangıç parametreleri - Daha fazla
        sahteBaslangicSayisi = Random.Range(2, 6); // 2-5 arası (daha fazla sahte başlangıç)
        minSahteBaslangicUzunlugu = Random.Range(3, 6); // 3-5 arası
        maxSahteBaslangicUzunlugu = Random.Range(7, 13); // 7-12 arası
    }

    private List<Vector2Int> GelismisAnaYolOlustur()
    {
        if (donusSikligi <= 0) donusSikligi = 1;
        var anaYollar = new List<Vector2Int>();
        int minX = 1;
        int maxX = genislik - 2;

        // Rastgele başlangıç pozisyonu
        int mevcutX = Random.Range(minX + 1, maxX);
        int sonHareket = 0; // Son hareketi takip et (düz gitmeyi önlemek için)
        int ayniYondeSayac = 0; // Aynı yönde kaç adım gidildi

        for (int z = uzunluk - 1; z >= 0; z--)
        {
            // Her adımda hareket etme şansı - düz gitmeyi önle
            bool hareketEt = false;

            if (z % donusSikligi == 0) // Belirli aralıklarla kesin hareket
            {
                hareketEt = true;
            }
            else if (ayniYondeSayac >= 3) // 3 adımdan fazla düz gitmeyi önle
            {
                hareketEt = true;
            }
            else if (Random.Range(0, 100) < zigzagSiddeti) // Rastgele hareket şansı
            {
                hareketEt = true;
            }

            if (hareketEt)
            {
                // Yön seçimi - son hareketin tersini tercih et (zigzag için)
                int yonDegisimi;
                if (ayniYondeSayac >= 2 && sonHareket != 0)
                {
                    // Aynı yönde çok gidildiyse ters yöne git
                    yonDegisimi = -sonHareket;
                }
                else
                {
                    // Normal rastgele yön seçimi (0 hariç - düz gitmeyi önle)
                    yonDegisimi = Random.value < 0.5f ? -1 : 1;
                }

                int yeniX = mevcutX + yonDegisimi;

                // Sınırları kontrol et
                if (yeniX >= minX && yeniX <= maxX)
                {
                    mevcutX = yeniX;

                    // Hareket takibi
                    if (sonHareket == yonDegisimi)
                    {
                        ayniYondeSayac++;
                    }
                    else
                    {
                        ayniYondeSayac = 1;
                        sonHareket = yonDegisimi;
                    }
                }
                else
                {
                    // Sınıra çarptıysa ters yöne git
                    ayniYondeSayac = 0;
                    sonHareket = 0;
                }
            }
            else
            {
                // Hareket etmiyorsa düz git sayacını artır
                ayniYondeSayac++;
            }

            // Tek karo genişliğinde ana yol
            anaYollar.Add(new Vector2Int(mevcutX, z));
        }

        Debug.Log($"🛤️ Ana yol oluşturuldu: {anaYollar.Count} karo, Zigzag şiddeti: {zigzagSiddeti}%");
        return anaYollar;
    }

    private void AlternatifYollarEkle(List<Vector2Int> anaYollar)
    {
        // Daha fazla ve daha kaotik alternatif yollar
        int alternatifSayisi = Random.Range(3, 8); // 3-7 alternatif yol

        for (int i = 0; i < alternatifSayisi; i++)
        {
            // Ana yolun ortalarından alternatif dal oluştur
            int rastgeleZ = Random.Range(uzunluk / 4, uzunluk * 3 / 4);
            var oZdekiAnaKaro = anaYollar.FirstOrDefault(p => p.y == rastgeleZ);

            if (oZdekiAnaKaro != default(Vector2Int))
            {
                int alternatifSansi = Random.Range(0, 100);
                if (alternatifSansi < alternatifYolSansi)
                {
                    AlternatifDalOlustur(oZdekiAnaKaro);
                }
            }
        }
    }

    private void AlternatifDalOlustur(Vector2Int baslangicNoktasi)
    {
        int dalUzunlugu = Random.Range(4, 10); // 4-9 arası, daha uzun dallar
        int yon = Random.value < 0.5f ? -1 : 1;
        int mevcutX = baslangicNoktasi.x;

        for (int i = 1; i <= dalUzunlugu; i++)
        {
            // Sadece yana git, ileriye gitme
            mevcutX += yon;
            Vector2Int yeniKaro = new Vector2Int(mevcutX, baslangicNoktasi.y + Random.Range(-2, 3)); // -2 ile +2 arası z kayması

            if (mevcutX <= 0 || mevcutX >= genislik - 1) break;
            if (!guvenliYolKoordinatlari.Contains(yeniKaro))
            {
                guvenliYolKoordinatlari.Add(yeniKaro);
            }
        }
    }

    private void GelismisSahteYollarEkle(List<Vector2Int> anaYollar)
    {
        int olusacakSahteYolAdedi = Random.Range(sahteYolSayisi / 2, sahteYolSayisi + 1);

        for (int i = 0; i < olusacakSahteYolAdedi; i++)
        {
            // Rastgele bir pozisyondan sahte yol başlat
            int rastgeleZ = Random.Range(uzunluk / 4, uzunluk * 3 / 4);
            int rastgeleX = Random.Range(1, genislik - 1);

            Vector2Int sahteBaslangic = new Vector2Int(rastgeleX, rastgeleZ);

            // Ana yola çok yakın değilse sahte yol oluştur
            bool anaYolaCokYakin = anaYollar.Any(p => Vector2Int.Distance(p, sahteBaslangic) < 2);
            if (!anaYolaCokYakin && !guvenliYolKoordinatlari.Contains(sahteBaslangic))
            {
                SahteYolOlustur(sahteBaslangic);
            }
        }
    }

    private void SahteYolOlustur(Vector2Int baslangic)
    {
        guvenliYolKoordinatlari.Add(baslangic);

        int sahteUzunluk = Random.Range(minSahteYolUzunlugu, maxSahteYolUzunlugu + 1);
        int mevcutX = baslangic.x;
        int mevcutZ = baslangic.y;

        for (int i = 1; i <= sahteUzunluk; i++)
        {
            // Rastgele yön seç (8 yön)
            int[] xYonleri = {-1, -1, -1, 0, 0, 1, 1, 1};
            int[] zYonleri = {-1, 0, 1, -1, 1, -1, 0, 1};
            int rastgeleYon = Random.Range(0, 8);

            int yeniX = mevcutX + xYonleri[rastgeleYon] + Random.Range(-1, 2); // Ekstra rastgelelik
            int yeniZ = mevcutZ + zYonleri[rastgeleYon] + Random.Range(-1, 2);

            Vector2Int yeniKaro = new Vector2Int(yeniX, yeniZ);

            // Sınırları kontrol et
            if (yeniX <= 0 || yeniX >= genislik - 1 || yeniZ <= 0 || yeniZ >= uzunluk - 1) break;
            if (guvenliYolKoordinatlari.Contains(yeniKaro)) break;

            guvenliYolKoordinatlari.Add(yeniKaro);
            mevcutX = yeniX;
            mevcutZ = yeniZ;

            // Dallanma şansı
            if (i > 2 && Random.Range(0, 100) < sahteYolDallanmaSansi)
            {
                SahteYolDallanmasiOlustur(yeniKaro, sahteUzunluk - i);
            }
        }
    }

    private void SahteYolDallanmasiOlustur(Vector2Int dallanmaNoktasi, int kalanUzunluk)
    {
        int dalUzunlugu = Mathf.Min(kalanUzunluk / 2, Random.Range(1, 4));
        int yon = Random.value < 0.5f ? -1 : 1;

        for (int i = 1; i <= dalUzunlugu; i++)
        {
            int yeniX = dallanmaNoktasi.x + (i * yon);
            int yeniZ = dallanmaNoktasi.y + Random.Range(-1, 2); // Hafif zigzag

            Vector2Int yeniKaro = new Vector2Int(yeniX, yeniZ);

            if (yeniX <= 0 || yeniX >= genislik - 1 || yeniZ <= 0 || yeniZ >= uzunluk - 1) break;
            if (!guvenliYolKoordinatlari.Contains(yeniKaro))
            {
                guvenliYolKoordinatlari.Add(yeniKaro);
            }
        }
    }

    private void GelismisSahteBaslangiclarEkle(List<Vector2Int> anaYollar)
    {
        // Ana yolun başlangıç pozisyonlarını bul
        var baslangicKarolari = anaYollar.Where(p => p.y == uzunluk - 1).ToList();
        List<int> kullanilanXler = baslangicKarolari.Select(p => p.x).ToList();

        int minX = 1;
        int maxX = genislik - 2;
        List<int> uygunXler = new List<int>();

        for (int x = minX; x <= maxX; x++)
        {
            // Ana yol başlangıcından en az 2 karo uzakta olsun
            bool cokYakin = kullanilanXler.Any(anaX => Mathf.Abs(anaX - x) < 2);
            if (!cokYakin)
            {
                uygunXler.Add(x);
            }
        }

        int olusacakSahteBaslangicAdedi = Mathf.Min(sahteBaslangicSayisi, uygunXler.Count);

        for (int i = 0; i < olusacakSahteBaslangicAdedi; i++)
        {
            if (uygunXler.Count == 0) break;

            int randomIndex = Random.Range(0, uygunXler.Count);
            int sahteX = uygunXler[randomIndex];
            uygunXler.RemoveAt(randomIndex);

            // Sahte başlangıç oluştur
            SahteBaslangicOlustur(sahteX);
        }
    }

    private void SahteBaslangicOlustur(int sahteX)
    {
        // Başlangıç karosu
        guvenliYolKoordinatlari.Add(new Vector2Int(sahteX, uzunluk - 1));

        int sahteYolUzantisi = Random.Range(minSahteBaslangicUzunlugu, maxSahteBaslangicUzunlugu + 1);
        int mevcutX = sahteX;

        for (int z = uzunluk - 2; z >= uzunluk - 1 - sahteYolUzantisi && z >= 0; z--)
        {
            // Sahte yol da zigzag yapabilir
            if (Random.value < 0.3f) // %30 şansla yana kay
            {
                int kayma = Random.Range(-1, 2); // -1, 0, 1
                mevcutX = Mathf.Clamp(mevcutX + kayma, 1, genislik - 2);
            }

            Vector2Int sahteKaro = new Vector2Int(mevcutX, z);

            // Ana yola çok yaklaşırsa dur
            bool anaYolaCokYakin = guvenliYolKoordinatlari.Any(p => Vector2Int.Distance(p, sahteKaro) < 1.5f);
            if (anaYolaCokYakin) break;

            guvenliYolKoordinatlari.Add(sahteKaro);

            // Bazen sahte başlangıçlar da dallanabilir
            if (Random.Range(0, 100) < 25) // %25 şansla dallan
            {
                SahteBaslangicDallanmasiOlustur(sahteKaro);
            }
        }
    }

    private void SahteBaslangicDallanmasiOlustur(Vector2Int dallanmaNoktasi)
    {
        int dalUzunlugu = Random.Range(1, 4);
        int yon = Random.value < 0.5f ? -1 : 1;

        for (int i = 1; i <= dalUzunlugu; i++)
        {
            int yeniX = dallanmaNoktasi.x + (i * yon);
            int yeniZ = dallanmaNoktasi.y - Random.Range(0, 2); // Hafif geriye git

            Vector2Int yeniKaro = new Vector2Int(yeniX, yeniZ);

            if (yeniX <= 0 || yeniX >= genislik - 1 || yeniZ < 0) break;
            if (!guvenliYolKoordinatlari.Contains(yeniKaro))
            {
                guvenliYolKoordinatlari.Add(yeniKaro);
            }
        }
    }

    void ZeminiOlustur()
    {
        // ⚠️ KRİTİK: NetworkObject'leri doğru şekilde temizle ⚠️
        if (!IsServer)
        {
            return;
        }

        // Mevcut NetworkObject'leri güvenli şekilde despawn et
        foreach (Transform child in transform)
        {
            NetworkObject netObj = child.GetComponent<NetworkObject>();
            if (netObj != null && netObj.IsSpawned)
            {
                netObj.Despawn(true);
            }
            else
            {
                // NetworkObject değilse normal destroy
                Destroy(child.gameObject);
            }
        }
        tumKaroScriptler.Clear();

        GuvenliYolOlustur();

        for (int x = 0; x < genislik; x++)
        {
            for (int z = 0; z < uzunluk; z++)
            {
                Vector3 localPozisyon = new Vector3(x * karoArasiMesafe, 0, z * karoArasiMesafe);
                Vector3 pozisyon = transform.TransformPoint(localPozisyon);

                // --- BURASI DEĞİŞTİ ---
                // 1. Karo objesini HİÇBİR ŞEYİN ALTINDA OLMADAN oluştur.
                GameObject yeniKaroObj = Instantiate(zeminKarosuPrefab, pozisyon, transform.rotation);

                ZeminKarosu zeminKarosuScript = yeniKaroObj.GetComponent<ZeminKarosu>();
                Vector2Int mevcutKoordinat = new Vector2Int(x, z);

                if (zeminKarosuScript != null)
                {
                    // 2. ÖNCE karo objesini ağ üzerinde spawn et.
                    NetworkObject netObj = yeniKaroObj.GetComponent<NetworkObject>();
                    if (netObj != null)
                    {
                        netObj.Spawn(true);
                    }
                    else
                    {
                        Debug.LogError($"❌ ZeminKaro prefab'ında NetworkObject component'i yok! {yeniKaroObj.name}");
                        Destroy(yeniKaroObj); // Hatalı objeyi yok et
                        continue; // Döngünün bir sonraki adımına geç
                    }

                    // 3. ŞİMDİ GÜVENLE PARENT YAPABİLİRSİN.
                    yeniKaroObj.transform.SetParent(this.transform);

                    // 4. Ve NetworkVariable'ları ayarlayabilirsin.
                    ZeminKarosu.ZeminTipi tip = guvenliYolKoordinatlari.Contains(mevcutKoordinat)
                        ? ZeminKarosu.ZeminTipi.Saglam
                        : ZeminKarosu.ZeminTipi.Yikilan;

                    zeminKarosuScript.Kurulum(tip, seciliTemaIndex);

                    tumKaroScriptler.Add(zeminKarosuScript);
                }
            }
        }

        // Client'lara karo sayısını bildir
        BildirKaroSayisiClientRpc(tumKaroScriptler.Count);
    }

    /// <summary>
    /// Client'lara karo sayısını bildir
    /// </summary>
    [ClientRpc]
    private void BildirKaroSayisiClientRpc(int karoSayisi)
    {
        if (IsServer) return; // Server'da çalışmasın
    }

    /// <summary>
    /// SENİN PRENSİBİN: Client'ta tek seferlik kontrol, sürekli zamanlayıcı yok!
    /// </summary>
    private void ClientKaroTekKontrol()
    {
        if (IsServer) return; // Sadece client'ta çalışsın

        // Bu objenin child'larını kontrol et
        int childCount = transform.childCount;

        // NetworkObject'leri de kontrol et
        ZeminKarosu[] karolar = FindObjectsByType<ZeminKarosu>(FindObjectsSortMode.None);
    }

    void DebugModunuAyarla()
    {
        debugAction = new InputAction(type: InputActionType.Button, binding: "<Keyboard>/o");
        // Tuşa basıldığında sunucuya RPC gönder
        debugAction.performed += ctx => {
            // Sadece sunucuda çalışan bir fonksiyonu çağırmak için RPC gönderiyoruz.
            // Bu isteği herhangi bir istemci yapabilir.
            if (IsOwner)
                ToggleDebugServerRpc();
        };
        debugAction.Enable();
    }
    
    // --- OPTİMİZE EDİLMİŞ RPC ---
    // Artık her karo için tek tek RPC göndermeye gerek yok.
    [ServerRpc(RequireOwnership = false)]
    private void ToggleDebugServerRpc()
    {
        // Sadece bu tek değişkeni değiştirmemiz yeterli.
        // Bu değişikliği dinleyen tüm ZeminKarosu objeleri kendilerini güncelleyecek.
        isDebugModeActive.Value = !isDebugModeActive.Value;
    }

    [ContextMenu("Debug: ZeminKoro Network Durumu")]
    public void DebugZeminKoroNetworkDurumu()
    {
        Debug.Log("🔍 === ZEMİNKORO NETWORK DURUM RAPORU ===");
        Debug.Log($"IsServer: {IsServer}");
        Debug.Log($"IsClient: {IsClient}");

        if (IsServer)
        {
            Debug.Log($"Server'da toplam karo sayısı: {tumKaroScriptler.Count}");
        }
    }

    [ContextMenu("Test: Basit Karo Oluştur")]
    public void TestBasitKaroOlustur()
    {
        if (!IsServer)
        {
            return;
        }

        Vector3 testPozisyon = transform.position + Vector3.forward * 5f;
        GameObject testKaro = Instantiate(zeminKarosuPrefab, testPozisyon, Quaternion.identity);

        NetworkObject netObj = testKaro.GetComponent<NetworkObject>();
        if (netObj != null)
        {
            netObj.Spawn(true);
        }
        else
        {
            Destroy(testKaro);
        }
    }

    /// <summary>
    /// Tüm oyuncuların sahne yüklenmesini bekle
    /// </summary>
    private System.Collections.IEnumerator TumOyuncularinSahneYuklenmesinibekle()
    {
        if (!IsServer) yield break;

        // NetworkManager'ın SceneManager'ını kullanarak sahne yüklenme durumunu kontrol et
        float beklemeSuresi = 0f;
        float maksimumBekleme = 30f; // 30 saniye maksimum bekleme

        while (beklemeSuresi < maksimumBekleme)
        {
            // Tüm bağlı oyuncuların sayısını al
            int toplamOyuncuSayisi = NetworkManager.Singleton.ConnectedClientsIds.Count;

            // Eğer oyuncu yoksa veya sadece server varsa direkt başlat
            if (toplamOyuncuSayisi <= 1)
            {
                break;
            }

            // Kısa bir bekleme
            yield return new WaitForSeconds(0.5f);
            beklemeSuresi += 0.5f;

            // Basit kontrol: Belirli bir süre bekle (NetworkManager'ın kendi senkronizasyonuna güven)
            if (beklemeSuresi >= 3f) // Minimum 3 saniye bekle
            {
                break;
            }
        }

        // Zemin oluşturmayı başlat
        ZeminiOlustur();
        DebugModunuAyarla();
    }

    /// <summary>
    /// Gelişmiş sahne yüklenme kontrolü (NetworkManager SceneManager ile)
    /// </summary>
    private System.Collections.IEnumerator GelismisOyuncuBekleme()
    {
        if (!IsServer) yield break;

        float beklemeSuresi = 0f;
        float maksimumBekleme = 30f;
        int sonOyuncuSayisi = 0;
        float sabitSayiSuresi = 0f;

        while (beklemeSuresi < maksimumBekleme)
        {
            int mevcutOyuncuSayisi = NetworkManager.Singleton.ConnectedClientsIds.Count;

            // Oyuncu sayısı değişti mi?
            if (mevcutOyuncuSayisi != sonOyuncuSayisi)
            {
                sonOyuncuSayisi = mevcutOyuncuSayisi;
                sabitSayiSuresi = 0f;
            }
            else
            {
                sabitSayiSuresi += 0.5f;
            }

            // Oyuncu sayısı 2 saniye boyunca sabit kaldıysa başlat
            if (sabitSayiSuresi >= 2f && mevcutOyuncuSayisi > 1)
            {
                break;
            }

            // Tek oyuncuysa direkt başlat
            if (mevcutOyuncuSayisi <= 1)
            {
                break;
            }

            yield return new WaitForSeconds(0.5f);
            beklemeSuresi += 0.5f;
        }

        // Zemin oluşturmayı başlat
        ZeminiOlustur();
        DebugModunuAyarla();
    }

    private new void OnDestroy()
    {
        if (debugAction != null) debugAction.Disable();
    }
}
