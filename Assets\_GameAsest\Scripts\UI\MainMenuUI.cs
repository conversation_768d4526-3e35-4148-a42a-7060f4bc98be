using UnityEngine;
using UnityEngine.UI;
using Unity.Netcode;
using TMPro;

public class MainMenuUI : MonoBehaviour
{
    [Header("UI Referansları")]
    public TMP_InputField nameInputField;
    public Button hostButton;
    public Button clientButton;
    public Button quitButton;

    [Header("Para Gösterimi")]
    public TextMeshProUGUI toplamParaText;
    public Button paraSifirlaButton; // Test için

    [Header("Oyuncu Bilgileri")]
    public TextMeshProUGUI oyuncuBilgileriText; // İsim ve para birlikte

    [Header("Sahn<PERSON> Ayarları")]
    public string lobbySceneName = "Lobby";

    [Header("Lobi Sistemi")]
    public bool useLobbySystem = true;

    void Start()
    {
        // Butonlara event listener'ları ekle
        if (hostButton != null)
            hostButton.onClick.AddListener(StartHost);

        if (clientButton != null)
            clientButton.onClick.AddListener(StartClient);

        if (quitButton != null)
            quitButton.onClick.AddListener(QuitGame);

        if (paraSifirlaButton != null)
            paraSifirlaButton.onClick.AddListener(ParaSifirla);

        // Tüm oyuncu verilerini yükle
        PlayerData.TumVerileriYukle();

        // Input field'a kaydedilmiş ismi yükle
        if (nameInputField != null)
        {
            nameInputField.text = PlayerData.PlayerName;

            // Input field değiştiğinde otomatik kaydet
            nameInputField.onEndEdit.AddListener(OnNameChanged);
        }

        // UI'ı güncelle
        OyuncuBilgileriGuncelle();

        // Application event'lerine abone ol (LeanTween temizliği için)
        Application.focusChanged += OnApplicationFocusChanged;
        Application.quitting += OnApplicationQuitting;
    }

    private void StartHost()
    {
        // Input alanındaki yazıyı al ve kaydet
        if (nameInputField != null)
        {
            PlayerData.IsimDegistir(nameInputField.text);
        }

        Debug.Log($"Lobi Kuruluyor... Oyuncu Adı: {PlayerData.PlayerName}");

        if (useLobbySystem)
        {
            // YENİ LOBİ SİSTEMİ - Doğrudan Lobby3D sahnesine git
            Debug.Log("🎮 Host başlatılıyor - Lobby3D sahnesine gidiliyor...");
            
            NetworkManager.Singleton.StartHost();
            NetworkManager.Singleton.SceneManager.LoadScene("3DLobby", UnityEngine.SceneManagement.LoadSceneMode.Single);
        }
        else
        {
            // Eski sistem - direkt oyuna geç
            NetworkManager.Singleton.StartHost();
            NetworkManager.Singleton.SceneManager.LoadScene("fakeblock_Scene", UnityEngine.SceneManagement.LoadSceneMode.Single);
        }
    }

    private void StartClient()
    {
        // Input alanındaki yazıyı al ve kaydet
        if (nameInputField != null)
        {
            PlayerData.IsimDegistir(nameInputField.text);
        }

        Debug.Log($"🔌 CLIENT BAŞLATILIYOR... Oyuncu Adı: {PlayerData.PlayerName}");

        // NetworkManager kontrolü
        if (NetworkManager.Singleton == null)
        {
            Debug.LogError("❌ NetworkManager bulunamadı!");
            return;
        }

        // Bağlantı bilgilerini ayarla ve kontrol et
        var transport = NetworkManager.Singleton.NetworkConfig.NetworkTransport;
        if (transport is Unity.Netcode.Transports.UTP.UnityTransport unityTransport)
        {
            // Localhost'a bağlan
            unityTransport.SetConnectionData("127.0.0.1", 7777);
            Debug.Log($"🌐 Hedef sunucu ayarlandı - IP: {unityTransport.ConnectionData.Address}, Port: {unityTransport.ConnectionData.Port}");
            Debug.Log("ℹ️ Localhost bağlantısı - Aynı bilgisayarda host çalışıyor olmalı!");
        }

        if (useLobbySystem)
        {
            // YENİ LOBİ SİSTEMİ - Doğrudan host'a katıl
            Debug.Log("🔗 Client başlatılıyor - Host'a katılınıyor...");
            // Oyuncu adını bağlantı payload'ına ekle
            string playerName = PlayerData.PlayerName;

            // Payload size kontrolü (max 1024 bytes)
            if (string.IsNullOrEmpty(playerName))
            {
                playerName = "Oyuncu";
                Debug.LogWarning("⚠️ PlayerName boş, varsayılan kullanılıyor");
            }

            byte[] nameBytes = System.Text.Encoding.UTF8.GetBytes(playerName);
            if (nameBytes.Length > 1024)
            {
                Debug.LogError($"❌ PlayerName çok uzun! {nameBytes.Length} bytes > 1024");
                playerName = playerName.Substring(0, 100); // Kısalt
                nameBytes = System.Text.Encoding.UTF8.GetBytes(playerName);
            }

            NetworkManager.Singleton.NetworkConfig.ConnectionData = nameBytes;
            Debug.Log($"🔗 Connection data ayarlandı - PlayerName: '{playerName}', Bytes: {nameBytes.Length}");

            // Connection callback'lerini dinle
            NetworkManager.Singleton.OnClientConnectedCallback += OnClientConnected;
            NetworkManager.Singleton.OnClientDisconnectCallback += OnClientDisconnected;

            bool result = NetworkManager.Singleton.StartClient();
            Debug.Log($"🔗 StartClient sonucu: {result}");
        }
        else
        {
            Debug.Log("🎮 Eski sistem - direkt host'a katılınıyor...");
            // Eski sistem - direkt host'a katıl
            NetworkManager.Singleton.StartClient();
        }
    }

    private void QuitGame()
    {
        Debug.Log("Oyundan çıkılıyor...");

        #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
        #else
            Application.Quit();
        #endif
    }

    /// <summary>
    /// Para gösterimini güncelle
    /// </summary>
    public void ParaGosteriminiGuncelle()
    {
        if (toplamParaText != null)
        {
            toplamParaText.text = $"💰 Toplam Para: {PlayerData.ToplamPara:N0}";
        }
    }

    /// <summary>
    /// Oyuncu bilgilerini güncelle (isim + para)
    /// </summary>
    public void OyuncuBilgileriGuncelle()
    {
        // Ayrı para text'i varsa güncelle
        ParaGosteriminiGuncelle();

        // Birleşik oyuncu bilgileri text'i varsa güncelle
        if (oyuncuBilgileriText != null)
        {
            oyuncuBilgileriText.text = $"👤 {PlayerData.PlayerName}\n💰 Para: {PlayerData.ToplamPara:N0}";
        }
    }

    /// <summary>
    /// İsim değiştiğinde çağrılır
    /// </summary>
    private void OnNameChanged(string yeniIsim)
    {
        if (!string.IsNullOrEmpty(yeniIsim))
        {
            PlayerData.IsimDegistir(yeniIsim);
            OyuncuBilgileriGuncelle();
        }
    }

    /// <summary>
    /// Test için para sıfırlama
    /// </summary>
    private void ParaSifirla()
    {
        PlayerData.ParaSifirla();
        OyuncuBilgileriGuncelle();
        Debug.Log("💰 MainMenuUI: Para sıfırlandı!");
    }

    /// <summary>
    /// Oyun bittiğinde çağrılacak - kazanılan parayı ekle
    /// </summary>
    public void OyunBittiParaEkle(int kazanilanPara)
    {
        PlayerData.ParaEkle(kazanilanPara);
        OyuncuBilgileriGuncelle();
        Debug.Log($"💰 MainMenuUI: Oyun bitti, para eklendi: +{kazanilanPara}");
    }

    /// <summary>
    /// Uygulama odak kaybettiğinde animasyon temizliği
    /// </summary>
    private void OnApplicationFocusChanged(bool hasFocus)
    {
        if (!hasFocus && SafeAnimationSystem.TryCancelAllAnimations())
        {
            Debug.Log("✅ MainMenuUI: Application focus kaybı - Animasyonlar temizlendi");
        }
    }

    /// <summary>
    /// Uygulama kapanırken animasyon temizliği
    /// </summary>
    private void OnApplicationQuitting()
    {
        if (SafeAnimationSystem.TryCancelAllAnimations())
        {
            Debug.Log("✅ MainMenuUI: Application quitting - Animasyonlar temizlendi");
        }
    }

    void OnDestroy()
    {
        // Güvenli animasyon sistemi temizliği
        if (SafeAnimationSystem.TryCancelAllAnimations())
        {
            Debug.Log("✅ MainMenuUI: Animasyon temizlik tamamlandı");
        }

        // Event listener'ları temizle
        if (hostButton != null)
            hostButton.onClick.RemoveListener(StartHost);

        if (clientButton != null)
            clientButton.onClick.RemoveListener(StartClient);

        if (quitButton != null)
            quitButton.onClick.RemoveListener(QuitGame);

        if (paraSifirlaButton != null)
            paraSifirlaButton.onClick.RemoveListener(ParaSifirla);

        if (nameInputField != null)
            nameInputField.onEndEdit.RemoveListener(OnNameChanged);

        // Application event'lerini temizle
        Application.focusChanged -= OnApplicationFocusChanged;
        Application.quitting -= OnApplicationQuitting;
    }

    /// <summary>
    /// Client bağlandığında çağrılır
    /// </summary>
    private void OnClientConnected(ulong clientId)
    {
        Debug.Log($"✅ Client bağlandı! ClientId: {clientId}");

        // Callback'leri temizle
        if (NetworkManager.Singleton != null)
        {
            NetworkManager.Singleton.OnClientConnectedCallback -= OnClientConnected;
            NetworkManager.Singleton.OnClientDisconnectCallback -= OnClientDisconnected;
        }
    }

    /// <summary>
    /// Client bağlantısı kesildiğinde çağrılır
    /// </summary>
    private void OnClientDisconnected(ulong clientId)
    {
        Debug.Log($"❌ Client bağlantısı kesildi! ClientId: {clientId}");

        // Callback'leri temizle
        if (NetworkManager.Singleton != null)
        {
            NetworkManager.Singleton.OnClientConnectedCallback -= OnClientConnected;
            NetworkManager.Singleton.OnClientDisconnectCallback -= OnClientDisconnected;
        }
    }
}