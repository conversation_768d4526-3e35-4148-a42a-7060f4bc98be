using UnityEngine;
using Unity.Netcode;

[RequireComponent(typeof(Collider))]
public class Checkpoint : MonoBehaviour
{
    // --- HATA BU SATIRDAN KAYNAKLANIYOR ---
    // De<PERSON><PERSON><PERSON><PERSON>in "public" olduğundan ve adının "checkpointID"
    // (c <PERSON><PERSON><PERSON><PERSON><PERSON>, ID büyük) şeklinde yazıldığından emin ol.
    [Tooltip("Bu checkpoint'in benzersiz ID'si. Sahneye sırayla yerleştirin (0, 1, 2...).")]
    public int checkpointID = 0;
    // ------------------------------------

    private void Awake()
    {
        // Collider'ın trigger olduğundan emin olalım.
        GetComponent<Collider>().isTrigger = true;
    }

    private void OnTriggerEnter(Collider other)
    {
        // Sadece sunucu bu mantığı işlemeli.
        if (!NetworkManager.Singleton.IsServer) return;

        // Checkpoint'e giren objenin bir oyuncu olup olmadığını kontrol et.
        if (other.TryGetComponent<OyuncuKontrol>(out OyuncuKontrol oyuncu))
        {
            // SpawnManager'a bu oyuncunun checkpoint'ini güncellemesi için haber ver.
            SpawnManager.Instance.UpdatePlayerCheckpoint(oyuncu.OwnerClientId, checkpointID);
        }
    }
}