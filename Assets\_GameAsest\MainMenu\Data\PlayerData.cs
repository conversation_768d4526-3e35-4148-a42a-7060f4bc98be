using UnityEngine;

public static class PlayerData
{
    // 'static' sayesinde bu de<PERSON><PERSON><PERSON><PERSON><PERSON> projenin her yerinden
    // PlayerData.PlayerName yazarak ulaşabiliriz.
    // Bir nevi herkesin ulaşabildiği bir not defteri gibi.
    public static string PlayerName = "Oyuncu"; // Başlangıçta boş olmasın diye varsayılan bir isim.

    // Toplam kazanılan para
    public static int ToplamPara = 0;

    /// <summary>
    /// Para ekle ve kaydet
    /// </summary>
    public static void ParaEkle(int miktar)
    {
        ToplamPara += miktar;
        ParaKaydet();
        Debug.Log($"💰 PlayerData: Para eklendi +{miktar}, Toplam: {ToplamPara}");
    }

    /// <summary>
    /// Parayı PlayerPrefs'e kaydet
    /// </summary>
    public static void ParaKaydet()
    {
        PlayerPrefs.SetInt("ToplamPara", ToplamPara);
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Parayı PlayerPrefs'ten yükle
    /// </summary>
    public static void ParaYukle()
    {
        ToplamPara = PlayerPrefs.GetInt("ToplamPara", 0);
        Debug.Log($"💰 PlayerData: Para yüklendi: {ToplamPara}");
    }

    /// <summary>
    /// Parayı sıfırla (test için)
    /// </summary>
    public static void ParaSifirla()
    {
        ToplamPara = 0;
        ParaKaydet();
        Debug.Log("💰 PlayerData: Para sıfırlandı");
    }

    /// <summary>
    /// İsmi kaydet
    /// </summary>
    public static void IsimKaydet()
    {
        PlayerPrefs.SetString("PlayerName", PlayerName);
        PlayerPrefs.Save();
        Debug.Log($"👤 PlayerData: İsim kaydedildi: {PlayerName}");
    }

    /// <summary>
    /// İsmi yükle
    /// </summary>
    public static void IsimYukle()
    {
        PlayerName = PlayerPrefs.GetString("PlayerName", "Oyuncu");
        Debug.Log($"👤 PlayerData: İsim yüklendi: {PlayerName}");
    }

    /// <summary>
    /// İsmi değiştir ve kaydet
    /// </summary>
    public static void IsimDegistir(string yeniIsim)
    {
        if (!string.IsNullOrEmpty(yeniIsim))
        {
            PlayerName = yeniIsim;
            IsimKaydet();
            Debug.Log($"👤 PlayerData: İsim değiştirildi: {PlayerName}");
        }
    }

    /// <summary>
    /// Tüm verileri yükle
    /// </summary>
    public static void TumVerileriYukle()
    {
        IsimYukle();
        ParaYukle();
        Debug.Log($"📁 PlayerData: Tüm veriler yüklendi - İsim: {PlayerName}, Para: {ToplamPara}");
    }

    /// <summary>
    /// Tüm verileri kaydet
    /// </summary>
    public static void TumVerileriKaydet()
    {
        IsimKaydet();
        ParaKaydet();
        Debug.Log($"💾 PlayerData: Tüm veriler kaydedildi - İsim: {PlayerName}, Para: {ToplamPara}");
    }
}