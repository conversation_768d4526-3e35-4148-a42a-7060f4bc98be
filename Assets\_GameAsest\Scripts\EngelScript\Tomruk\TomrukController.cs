using UnityEngine;
using Unity.Netcode;

public class TomrukController : NetworkBehaviour
{
    [Header("Hareket Ayarları")]
    [Tooltip("<PERSON><PERSON><PERSON><PERSON> ne kadar hızlı yuvarlana<PERSON>ğını belirler.")]
    [SerializeField] private float yuvarlanmaKuvveti = 10f;

    [Tooltip("Tomruğun hangi yöne gideceğini buradan ayarlayabilirsin. Örneğin Z ekseninde ileri için (0, 0, 1) yaz.")]
    [SerializeField] private Vector3 hareketYonu = new Vector3(0, 0, 1);

    [Header("Tag Ayarları")]
    [Tooltip("Tomruğu yok edecek objelerin tag'i")]
    [SerializeField] private string yokEdiciTag = "YokEdici";

    [Header("Oyuncu Etkileşim Ayarları")]
    [Tooltip("Oyuncuya çarptığında fırlatma kuvveti")]
    [SerializeField] private float firlatmaKuvveti = 15f;

    [Tooltip("Oyuncuya çarptığında yukarı doğru fırlatma kuvveti")]
    [SerializeField] private float yukariFirelatmaKuvveti = 8f;

    [Tooltip("Oyuncu çarpışma efekti")]
    [SerializeField] private GameObject carpismEfekti;

    private Rigidbody rb;
    private Vector3 normalizedDirection; // Performans için cache'liyoruz
    private NetworkObject networkObject;

    public override void OnNetworkSpawn()
    {
        // NetworkObject referansını al
        networkObject = GetComponent<NetworkObject>();

        // Rigidbody kontrolü
        rb = GetComponent<Rigidbody>();
        if (rb == null)
        {
            Debug.LogError("TomrukController: Rigidbody komponenti bulunamadı!");
            enabled = false;
            return;
        }

        // Hareket yönünü normalize edip cache'liyoruz
        normalizedDirection = hareketYonu.normalized;
    }

    void FixedUpdate()
    {
        // Sadece server hareket hesaplamalarını yapar
        if (!IsServer) return;

        // Cache'lenmiş normalized direction kullanıyoruz
        rb.AddForce(normalizedDirection * yuvarlanmaKuvveti, ForceMode.Acceleration);
    }

    /// <summary>
    /// Çarpışma anında çalışan fonksiyon (Fiziksel çarpışma için)
    /// </summary>
    /// <param name="collision">Çarpışma bilgisi</param>
    private void OnCollisionEnter(Collision collision)
    {
        // Çarptığımız objenin etiketini kontrol ediyoruz
        if (collision.gameObject.CompareTag(yokEdiciTag))
        {
            // Server'da ise direkt yok et, client'da ise RPC gönder
            if (IsServer)
            {
                YokEt();
            }
            else
            {
                YokEtRpc();
            }
        }
        // Oyuncuya çarpışma kontrolü - HEM CLIENT HEM SERVER'DA ALGILANIR
        else if (collision.gameObject.CompareTag("Player"))
        {
            // Oyuncunun NetworkObject'ini al
            NetworkObject oyuncuNetworkObject = collision.gameObject.GetComponent<NetworkObject>();
            if (oyuncuNetworkObject != null)
            {
                // Server'da ise direkt işle, client'da ise RPC gönder
                if (IsServer)
                {
                    OyuncuyaCarpisma(oyuncuNetworkObject.OwnerClientId, collision.gameObject);
                }
                else
                {
                    // Client'tan server'a çarpışma bildir
                    OyuncuCarpismaBildirRpc(oyuncuNetworkObject.OwnerClientId);
                }
            }
        }
    }

    /// <summary>
    /// Trigger çarpışması için (Eğer YokEdici obje Trigger ise)
    /// </summary>
    /// <param name="other">Trigger olan collider</param>
    private void OnTriggerEnter(Collider other)
    {
        // Trigger olan objenin etiketini kontrol ediyoruz
        if (other.gameObject.CompareTag(yokEdiciTag))
        {
            // Server'da ise direkt yok et, client'da ise RPC gönder
            if (IsServer)
            {
                YokEt();
            }
            else
            {
                YokEtRpc();
            }
        }
        // Oyuncuya trigger çarpışması kontrolü - HEM CLIENT HEM SERVER'DA ALGILANIR
        else if (other.gameObject.CompareTag("Player"))
        {
            // Oyuncunun NetworkObject'ini al
            NetworkObject oyuncuNetworkObject = other.gameObject.GetComponent<NetworkObject>();
            if (oyuncuNetworkObject != null)
            {
                // Server'da ise direkt işle, client'da ise RPC gönder
                if (IsServer)
                {
                    OyuncuyaCarpisma(oyuncuNetworkObject.OwnerClientId, other.gameObject);
                }
                else
                {
                    // Client'tan server'a çarpışma bildir
                    OyuncuCarpismaBildirRpc(oyuncuNetworkObject.OwnerClientId);
                }
            }
        }
    }

    /// <summary>
    /// Tomruğu network üzerinden yok eder (Client'dan Server'a)
    /// </summary>
    [Rpc(SendTo.Server)]
    private void YokEtRpc()
    {
        YokEt();
    }

    /// <summary>
    /// Tomruğu yok eder (Sadece Server'da çalışır)
    /// </summary>
    private void YokEt()
    {
        if (!IsServer) return;

        if (networkObject != null)
        {
            Debug.Log($"Tomruk yok ediliyor: {gameObject.name}");
            // NetworkObject'i despawn et
            networkObject.Despawn(true);
        }
    }

    /// <summary>
    /// Client'tan server'a oyuncu çarpışması bildirir
    /// </summary>
    [Rpc(SendTo.Server)]
    private void OyuncuCarpismaBildirRpc(ulong oyuncuClientId)
    {
        // Server'da oyuncuyu bul ve çarpışma işlemini yap
        if (NetworkManager.Singleton.ConnectedClients.TryGetValue(oyuncuClientId, out var clientData))
        {
            if (clientData.PlayerObject != null)
            {
                GameObject oyuncuGameObject = clientData.PlayerObject.gameObject;
                OyuncuyaCarpisma(oyuncuClientId, oyuncuGameObject);
            }
        }
    }

    /// <summary>
    /// Oyuncuya çarpışma işlemi (Sadece Server'da çalışır)
    /// </summary>
    /// <param name="oyuncuClientId">Çarpışan oyuncunun client ID'si</param>
    /// <param name="oyuncuGameObject">Çarpışan oyuncu GameObject'i</param>
    private void OyuncuyaCarpisma(ulong oyuncuClientId, GameObject oyuncuGameObject)
    {
        if (!IsServer) return;

        Debug.Log($"Tomruk oyuncuya çarptı: {oyuncuGameObject.name} (Client ID: {oyuncuClientId})");

        // Çarpışma yönünü hesapla
        Vector3 carpismYonu = (oyuncuGameObject.transform.position - transform.position).normalized;
        carpismYonu.y = 0; // Y eksenini sıfırla, sadece yatay fırlatma

        // Fırlatma vektörünü hesapla
        Vector3 firlatmaVektoru = carpismYonu * firlatmaKuvveti;
        firlatmaVektoru.y = yukariFirelatmaKuvveti; // Yukarı doğru fırlatma ekle

        // SADECE FIRLATMA SİSTEMİ - Ragdoll KaktusVurusu tarafından hallediliyor
        IEtkilesimeGirebilir etkilesenObje = oyuncuGameObject.GetComponent<IEtkilesimeGirebilir>();
        if (etkilesenObje != null)
        {
            // Oyuncuyu fırlat
            etkilesenObje.HariciHareketiAyarla(firlatmaVektoru);

            // Kısa bir süre sonra harici hareketi sıfırla
            StartCoroutine(FirlatmaEfektiSifirla(etkilesenObje));
        }

        // Sunucu hesaplamasının ardından client tarafına da fırlatma bilgisini ilet
        OyuncuFirlatmaRpc(oyuncuClientId, firlatmaVektoru);

        // GÖRSEL EFEKT
        Vector3 efektPozisyonu = oyuncuGameObject.transform.position;
        efektPozisyonu.y += 1f; // Biraz yukarıda göster

        // Server'da efekt oluştur
        if (carpismEfekti != null)
        {
            Instantiate(carpismEfekti, efektPozisyonu, Quaternion.identity);
        }

        // Client'lara çarpışma efektini bildir
        CarpismEfektiRpc(efektPozisyonu);
    }

    /// <summary>
    /// Fırlatma efektini belirli bir süre sonra sıfırlar
    /// </summary>
    private System.Collections.IEnumerator FirlatmaEfektiSifirla(IEtkilesimeGirebilir etkilesenObje)
    {
        yield return new WaitForSeconds(0.5f); // 0.5 saniye bekle

        if (etkilesenObje != null)
        {
            etkilesenObje.HariciHareketiSifirla();
        }
    }

    /// <summary>
    /// Client'lara çarpışma efektini bildirir
    /// </summary>
    [Rpc(SendTo.ClientsAndHost)]
    private void CarpismEfektiRpc(Vector3 efektPozisyonu)
    {
        // Sadece client'larda efekt oluştur (server'da zaten oluşturuldu)
        if (!IsServer && carpismEfekti != null)
        {
            Instantiate(carpismEfekti, efektPozisyonu, Quaternion.identity);
        }
    }

    /// <summary>
    /// Sunucudan client'lara oyuncunun fırlatılma bilgisini iletir
    /// </summary>
    [Rpc(SendTo.ClientsAndHost)]
    private void OyuncuFirlatmaRpc(ulong hedefClientId, Vector3 firlatmaVektoru)
    {
        // Sunucu tarafı zaten işlemi gerçekleştirdi
        if (IsServer) return;

        // Hedef oyuncuyu bul
        GameObject oyuncuGO = null;
        if (NetworkManager.Singleton.ConnectedClients.TryGetValue(hedefClientId, out var clientData))
        {
            if (clientData.PlayerObject != null)
            {
                oyuncuGO = clientData.PlayerObject.gameObject;
            }
        }
        if (oyuncuGO != null)
        {
            IEtkilesimeGirebilir etkilesen = oyuncuGO.GetComponent<IEtkilesimeGirebilir>();
            if (etkilesen != null)
            {
                // Harici hareket uygula
                etkilesen.HariciHareketiAyarla(firlatmaVektoru);

                // Kısa bir süre sonra harici hareketi sıfırla
                StartCoroutine(FirlatmaEfektiSifirla(etkilesen));
            }
        }
    }

    /// <summary>
    /// Spawner tarafından ayarlandığında çağrılır
    /// </summary>
    public void SpawnerTarafindanAyarlandi()
    {
        // Hareket yönünü yeniden hesapla
        if (Application.isPlaying)
        {
            normalizedDirection = hareketYonu.normalized;
        }
    }

    /// <summary>
    /// Inspector'da hareket yönü değiştirildiğinde çalışır
    /// </summary>
    private void OnValidate()
    {
        if (Application.isPlaying)
        {
            normalizedDirection = hareketYonu.normalized;
        }
    }
}