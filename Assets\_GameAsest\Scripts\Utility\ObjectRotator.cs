using Unity.Netcode;
using UnityEngine;

public class ObjectRotator : NetworkBehaviour
{
    [<PERSON><PERSON>("Dönme Ayarları")]
    [Tooltip("<PERSON><PERSON><PERSON><PERSON> hızı (derece/saniye).")]
    [SerializeField] private float rotationSpeed = 50f;

    [Tooltip("Dönme ekseni. Genellikle Vector3.up (Y ekseni) kullanılır.")]
    [SerializeField] private Vector3 rotationAxis = Vector3.up;

    [Header("Yörünge Ayarları")]
    [Tooltip("Aktif edilirse, obje bir merkez etrafında yörünge hareketi yapar.")]
    [SerializeField] private bool enableOrbit = false;
    [Tooltip("Yörüngenin merkez noktası. Bu obje, bu noktanın etrafında dönecektir.")]
    [SerializeField] private Transform orbitCenter;
    [Tooltip("Yörünge hızı (derece/saniye).")]
    [SerializeField] private float orbitSpeed = 30f;
    [Tooltip("Yörünge ekseni. Genellikle Vector3.up (Y ekseni) kullanılır.")]
    [SerializeField] private Vector3 orbitAxis = Vector3.up;

    [Header("Senkronizasyon Ayarları")]
    [Tooltip("NetworkTransform kullanmak yerine manuel senkronizasyon kullan.")]
    [SerializeField] private bool useManualSync = false;

    // Network senkronizasyonu için değişkenler
    private NetworkVariable<float> networkRotationAngle = new NetworkVariable<float>(0f, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);
    private NetworkVariable<Vector3> networkPosition = new NetworkVariable<Vector3>(Vector3.zero, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);
    private NetworkVariable<Quaternion> networkRotation = new NetworkVariable<Quaternion>(Quaternion.identity, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);

    // Başlangıç pozisyon ve rotasyonu
    private Vector3 initialPosition;
    private Quaternion initialRotation;
    private float currentRotationAngle = 0f;

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();
        
        // Başlangıç değerlerini kaydet
        initialPosition = transform.position;
        initialRotation = transform.rotation;

        if (IsServer)
        {
            // Server başlangıç değerlerini network variable'lara ata
            networkPosition.Value = initialPosition;
            networkRotation.Value = initialRotation;
            networkRotationAngle.Value = 0f;
        }

        // Client'larda network variable değişikliklerini dinle
        if (!IsServer && useManualSync)
        {
            networkPosition.OnValueChanged += OnNetworkPositionChanged;
            networkRotation.OnValueChanged += OnNetworkRotationChanged;
        }
    }

    public override void OnNetworkDespawn()
    {
        // Event listener'ları temizle
        if (!IsServer && useManualSync)
        {
            networkPosition.OnValueChanged -= OnNetworkPositionChanged;
            networkRotation.OnValueChanged -= OnNetworkRotationChanged;
        }
        
        base.OnNetworkDespawn();
    }

    private void FixedUpdate()
    {
        if (IsServer)
        {
            // Server'da rotasyon hesaplamalarını yap
            currentRotationAngle += rotationSpeed * Time.fixedDeltaTime;
            if (currentRotationAngle >= 360f) currentRotationAngle -= 360f;

            // Platformu kendi ekseninde döndür
            transform.Rotate(rotationAxis, rotationSpeed * Time.fixedDeltaTime);

            // Yörünge hareketini uygula (eğer aktifse)
            if (enableOrbit && orbitCenter != null)
            {
                transform.RotateAround(orbitCenter.position, orbitAxis, orbitSpeed * Time.fixedDeltaTime);
            }

            // Manuel senkronizasyon kullanılıyorsa network variable'ları güncelle
            if (useManualSync)
            {
                networkRotationAngle.Value = currentRotationAngle;
                networkPosition.Value = transform.position;
                networkRotation.Value = transform.rotation;
            }
        }
        else if (useManualSync)
        {
            // Client'larda manuel senkronizasyon kullanılıyorsa network variable'lardan güncelle
            ApplyNetworkTransform();
        }
        else
        {
            // NetworkTransform kullanılıyorsa client'larda da lokal rotasyonu uygula
            // Bu, NetworkTransform'un pozisyon senkronizasyonunu bozmadan rotasyonu sağlar
            if (IsSpawned)
            {
                transform.Rotate(rotationAxis, rotationSpeed * Time.fixedDeltaTime);
                
                if (enableOrbit && orbitCenter != null)
                {
                    transform.RotateAround(orbitCenter.position, orbitAxis, orbitSpeed * Time.fixedDeltaTime);
                }
            }
        }
    }

    private void ApplyNetworkTransform()
    {
        // Manuel senkronizasyon için network variable'lardan transform'u güncelle
        if (networkPosition.Value != Vector3.zero)
        {
            transform.position = Vector3.Lerp(transform.position, networkPosition.Value, Time.fixedDeltaTime * 10f);
        }
        
        if (networkRotation.Value != Quaternion.identity)
        {
            transform.rotation = Quaternion.Lerp(transform.rotation, networkRotation.Value, Time.fixedDeltaTime * 10f);
        }
    }

    private void OnNetworkPositionChanged(Vector3 previousValue, Vector3 newValue)
    {
        if (!IsServer)
        {
            transform.position = newValue;
        }
    }

    private void OnNetworkRotationChanged(Quaternion previousValue, Quaternion newValue)
    {
        if (!IsServer)
        {
            transform.rotation = newValue;
        }
    }

    [ServerRpc(RequireOwnership = false)]
    private void RequestParentingServerRpc(ulong playerId, bool shouldParent)
    {
        if (!IsServer) return;

        var playerObject = NetworkManager.Singleton.SpawnManager.SpawnedObjects[playerId];
        if (playerObject != null)
        {
            if (shouldParent)
            {
                playerObject.transform.SetParent(transform, true);
            }
            else
            {
                playerObject.transform.SetParent(null, true);
            }
            
            // Tüm client'lara parenting değişikliğini bildir
            UpdateParentingClientRpc(playerId, shouldParent);
        }
    }

    [ClientRpc]
    private void UpdateParentingClientRpc(ulong playerId, bool shouldParent)
    {
        if (IsServer) return; // Server zaten işlemi yaptı

        var playerObject = NetworkManager.Singleton.SpawnManager.SpawnedObjects[playerId];
        if (playerObject != null)
        {
            if (shouldParent)
            {
                playerObject.transform.SetParent(transform, true);
            }
            else
            {
                playerObject.transform.SetParent(null, true);
            }
        }
    }

    private void OnTriggerEnter(Collider other)
    {
        if (!IsServer) return;

        // Oyuncu platforma girdiğinde, oyuncuyu platformun altına al (parenting)
        if (other.CompareTag("Player"))
        {
            var networkObject = other.GetComponent<NetworkObject>();
            if (networkObject != null)
            {
                other.transform.SetParent(transform, true);
                // Client'lara da bildir
                UpdateParentingClientRpc(networkObject.NetworkObjectId, true);
            }
        }
    }

    private void OnTriggerExit(Collider other)
    {
        if (!IsServer) return;

        // Oyuncu platformdan ayrıldığında, parent ilişkisini kopar
        if (other.CompareTag("Player"))
        {
            var networkObject = other.GetComponent<NetworkObject>();
            if (networkObject != null)
            {
                other.transform.SetParent(null, true);
                // Client'lara da bildir
                UpdateParentingClientRpc(networkObject.NetworkObjectId, false);
            }
        }
    }

    // Debug için - Inspector'da network durumunu göster
    private void OnValidate()
    {
        if (Application.isPlaying && IsSpawned)
        {
            Debug.Log($"ObjectRotator - IsServer: {IsServer}, IsClient: {IsClient}, IsHost: {IsHost}");
        }
    }
}