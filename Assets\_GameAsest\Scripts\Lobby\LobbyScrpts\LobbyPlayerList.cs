using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using Unity.Netcode;

/// <summary>
/// Lobideki oyuncu listesini yöneten UI
/// </summary>
public class LobbyPlayerList : MonoBehaviour
{
    public static LobbyPlayerList Instance { get; private set; }

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            // If there is an instance, and it's not me, destroy myself.
            Destroy(gameObject);
            return;
        }
        Instance = this;
    }

    [Header("UI Components")]
    [Tooltip("Oyuncuları liste halinde düzenlemek için Grid veya Vertical Layout kullanın.")]
    public Transform playerListContainer;
    
    [Tooltip("Oyuncu profil prefab'ı.")]
    public GameObject playerListItemPrefab;
    
    private Dictionary<ulong, PlayerListItem> playerListItems = new Dictionary<ulong, PlayerListItem>();

    void Start()
    {
        // Lobby3DManager'ın hazır olma<PERSON>ını beklemek için kısa bir gecikme.
        // Bu, sahne yükleme zamanlamalarına karşı daha sağlam bir yapı sağlar.
        Invoke(nameof(SubscribeToEvents), 0.2f);
    }

    private void SubscribeToEvents()
    {
        if (Lobby3DManager.Instance != null)
        {
            Lobby3DManager.Instance.GetPlayerList().OnListChanged += OnNetworkListChanged;
            UpdatePlayerListUI(); // Başlangıçta listeyi doldur
        }
        else
        {
            // Eğer Lobby3DManager hala bulunamazsa, tekrar dene.
            Debug.LogWarning("[LobbyPlayerList] Lobby3DManager bulunamadı. 1 saniye sonra tekrar denenecek.");
            Invoke(nameof(SubscribeToEvents), 1f);
        }
    }

    // YENİ: NetworkList'teki değişiklikleri dinleyen olay işleyici.
    private void OnNetworkListChanged(NetworkListEvent<PlayerData3D> changeEvent)
    {
        UpdatePlayerListUI();
    }

    /// <summary>
    /// Oyuncu listesi değiştiğinde UI'ı günceller.
    /// </summary>
    public void UpdatePlayerListUI()
    {
        if (playerListContainer == null)
        {
            Debug.LogError("[LobbyPlayerList] playerListContainer null!");
            return;
        }

        // Prefab'ın yanlış yapılandırılıp yapılandırılmadığını kontrol et
        if (playerListItemPrefab != null && playerListItemPrefab.GetComponent<LobbyPlayerList>() != null)
        {
            Debug.LogError("[LobbyPlayerList] 'playerListItemPrefab' yanlış yapılandırılmış. Bu prefab, bir 'LobbyPlayerList' bileşeni içeremez. Lütfen Unity editöründe doğru prefab'ı atadığınızdan emin olun.");
            return;
        }

        // Clear previous list to prevent duplicates
        foreach (Transform child in playerListContainer)
        {
            Destroy(child.gameObject);
        }
        playerListItems.Clear();

        if (Lobby3DManager.Instance == null)
        {
            Debug.LogError("[LobbyPlayerList] Lobby3DManager.Instance null!");
            return;
        }

        NetworkList<PlayerData3D> playerList = Lobby3DManager.Instance.GetPlayerList();
        ulong hostId = Lobby3DManager.Instance.GetHostClientId();

        // Recreate the list
        foreach (var playerData in playerList)
        {
            GameObject newListItem = Instantiate(playerListItemPrefab, playerListContainer);
            PlayerListItem listItemComponent = newListItem.GetComponent<PlayerListItem>();

            if (listItemComponent != null)
            {
                bool isHost = hostId == playerData.ClientId;
                listItemComponent.UpdatePlayerInfo(playerData.ClientId, playerData.PlayerName.ToString(), playerData.IsReady, isHost);
                playerListItems[playerData.ClientId] = listItemComponent;
            }
            else
            {
                Debug.LogError($"[LobbyPlayerList] PlayerListItem component not found on prefab for player {playerData.ClientId}");
            }
        }
    }

    private void OnDestroy()
    {
        if (Instance == this)
        {
            Instance = null;
        }

        if (Lobby3DManager.Instance != null && Lobby3DManager.Instance.GetPlayerList() != null)
        {
            Lobby3DManager.Instance.GetPlayerList().OnListChanged -= OnNetworkListChanged;
        }
    }
}
