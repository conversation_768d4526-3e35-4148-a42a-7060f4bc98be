using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Material Batch Sorunu Çözücü
/// 
/// Bu script, farklı renk materyallerini kullanan tüm renderer'ları bulup
/// tek bir materyal ve MaterialPropertyBlock kullanarak SetPass call sayısını azaltır.
/// </summary>
public class MaterialBatchFix : MonoBehaviour
{
    // Singleton pattern
    public static MaterialBatchFix Instance { get; private set; }

    [Header("Materyal Ayarları")]
    [Tooltip("Tüm renkli objeler için kullanılacak temel materyal")]
    public Material temelMateryal;

    [Tooltip("Otomatik temel materyal oluştur")]
    public bool otomatikMateryalOlustur = true;

    [Header("Optimizasyon Ayarları")]
    [Tooltip("Değiştirilecek materyallerin bulunduğu klasör yolu")]
    public string renkMateryalleriKlasoru = "color";

    [Tooltip("Değişiklikler kalıcı olsun mu? (Dikkat: Prefab'ları değiştirir!)")]
    public bool kaliciDegisiklik = false;

    // Orijinal materyalleri saklamak için
    private Dictionary<Renderer, Material[]> orijinalMateryaller = new Dictionary<Renderer, Material[]>();

    // Renk materyallerini ve renklerini saklamak için
    private Dictionary<string, Color> materyalRenkleri = new Dictionary<string, Color>();

    // Değiştirilen renderer sayısı
    private int degistirilenRendererSayisi = 0;
    private int toplamMateryalSayisi = 0;
    private int azaltilanMateryalSayisi = 0;

    private void Awake()
    {
        // Singleton pattern
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(gameObject);

        // Temel materyal yoksa oluştur
        if (temelMateryal == null && otomatikMateryalOlustur)
        {
            OlusturTemelMateryal();
        }

        // Hemen optimizasyonu başlat
        MateryalleriOptimizeEt();
    }

    /// <summary>
    /// Temel materyal oluşturur
    /// </summary>
    private void OlusturTemelMateryal()
    {
        // URP shader kullanarak basit bir materyal oluştur
        Shader shader = Shader.Find("Universal Render Pipeline/Lit");
        if (shader == null)
        {
            shader = Shader.Find("Standard"); // Fallback
        }

        temelMateryal = new Material(shader);
        temelMateryal.name = "OptimizeEdilmisRenkMateryal";
        temelMateryal.color = Color.white;
        temelMateryal.enableInstancing = true;

        Debug.Log("[MaterialBatchFix] Temel materyal otomatik oluşturuldu: " + temelMateryal.name);
    }

    /// <summary>
    /// Tüm materyalleri optimize eder
    /// </summary>
    public void MateryalleriOptimizeEt()
    {
        if (temelMateryal == null)
        {
            Debug.LogError("[MaterialBatchFix] Temel materyal ayarlanmamış! Optimizasyon yapılamıyor.");
            return;
        }

        Debug.Log("[MaterialBatchFix] Materyal optimizasyonu başlatılıyor...");

        // Önce renk materyallerini bul ve renklerini kaydet
        BulVeKaydetRenkMateryalleri();

        // Tüm renderer'ları bul
        Renderer[] tumRendererlar = FindObjectsByType<Renderer>(FindObjectsSortMode.None);
        degistirilenRendererSayisi = 0;
        toplamMateryalSayisi = 0;
        azaltilanMateryalSayisi = 0;

        // Her renderer için kontrol et
        foreach (Renderer renderer in tumRendererlar)
        {
            if (renderer == null) continue;

            // Orijinal materyalleri kaydet
            Material[] orijinalMats = renderer.sharedMaterials;
            if (orijinalMats == null || orijinalMats.Length == 0) continue;

            toplamMateryalSayisi += orijinalMats.Length;

            // Bu renderer'ın renk materyali kullanıp kullanmadığını kontrol et
            bool renkMateryaliKullaniyor = false;
            foreach (Material mat in orijinalMats)
            {
                if (mat != null && mat.name.Contains(renkMateryalleriKlasoru))
                {
                    renkMateryaliKullaniyor = true;
                    break;
                }
            }

            // Eğer renk materyali kullanıyorsa değiştir
            if (renkMateryaliKullaniyor)
            {
                // Orijinal materyalleri sakla
                if (!orijinalMateryaller.ContainsKey(renderer))
                {
                    orijinalMateryaller[renderer] = orijinalMats;
                }

                // Yeni materyal dizisi oluştur
                Material[] yeniMateryaller = new Material[orijinalMats.Length];
                MaterialPropertyBlock propertyBlock = new MaterialPropertyBlock();

                // Her materyal için kontrol et
                for (int i = 0; i < orijinalMats.Length; i++)
                {
                    Material orijinalMat = orijinalMats[i];
                    
                    // Eğer bu bir renk materyali ise değiştir
                    if (orijinalMat != null && orijinalMat.name.Contains(renkMateryalleriKlasoru))
                    {
                        // Rengi bul
                        Color renk = Color.white;
                        foreach (var entry in materyalRenkleri)
                        {
                            if (orijinalMat.name.Contains(entry.Key))
                            {
                                renk = entry.Value;
                                break;
                            }
                        }

                        // Temel materyali kullan
                        yeniMateryaller[i] = temelMateryal;

                        // PropertyBlock ile rengi ayarla
                        renderer.GetPropertyBlock(propertyBlock);
                        propertyBlock.SetColor("_Color", renk);
                        propertyBlock.SetColor("_BaseColor", renk); // URP için
                        renderer.SetPropertyBlock(propertyBlock, i);

                        azaltilanMateryalSayisi++;
                    }
                    else
                    {
                        // Renk materyali değilse aynı materyali kullan
                        yeniMateryaller[i] = orijinalMat;
                    }
                }

                // Yeni materyalleri uygula
                if (kaliciDegisiklik)
                {
                    renderer.sharedMaterials = yeniMateryaller;
                }
                else
                {
                    renderer.materials = yeniMateryaller;
                }

                degistirilenRendererSayisi++;
            }
        }

        Debug.Log($"[MaterialBatchFix] Optimizasyon tamamlandı! " +
                  $"Değiştirilen renderer: {degistirilenRendererSayisi}, " +
                  $"Toplam materyal: {toplamMateryalSayisi}, " +
                  $"Azaltılan materyal: {azaltilanMateryalSayisi}");
    }

    /// <summary>
    /// Renk materyallerini bulur ve renklerini kaydeder
    /// </summary>
    private void BulVeKaydetRenkMateryalleri()
    {
        materyalRenkleri.Clear();

        // Tüm materyalleri bul
        Material[] tumMateryaller = Resources.FindObjectsOfTypeAll<Material>();
        
        foreach (Material mat in tumMateryaller)
        {
            // Sadece renk klasöründeki materyalleri kontrol et
            if (mat != null && mat.name.Contains(renkMateryalleriKlasoru))
            {
                // Materyal adından renk adını çıkar
                string renkAdi = mat.name.ToLower();
                
                // Rengi kaydet
                if (mat.HasProperty("_Color"))
                {
                    materyalRenkleri[renkAdi] = mat.GetColor("_Color");
                }
                else if (mat.HasProperty("_BaseColor")) // URP için
                {
                    materyalRenkleri[renkAdi] = mat.GetColor("_BaseColor");
                }
                else
                {
                    // Renk bulunamadıysa, materyal adına göre varsayılan renk ata
                    if (renkAdi.Contains("kirmizi")) materyalRenkleri[renkAdi] = Color.red;
                    else if (renkAdi.Contains("mavi")) materyalRenkleri[renkAdi] = Color.blue;
                    else if (renkAdi.Contains("yesil")) materyalRenkleri[renkAdi] = Color.green;
                    else if (renkAdi.Contains("sari")) materyalRenkleri[renkAdi] = Color.yellow;
                    else if (renkAdi.Contains("mor")) materyalRenkleri[renkAdi] = new Color(0.5f, 0f, 1f);
                    else if (renkAdi.Contains("turuncu")) materyalRenkleri[renkAdi] = new Color(1f, 0.5f, 0f);
                    else if (renkAdi.Contains("pembe") || renkAdi.Contains("pink")) materyalRenkleri[renkAdi] = Color.magenta;
                    else if (renkAdi.Contains("siyah") || renkAdi.Contains("black")) materyalRenkleri[renkAdi] = Color.black;
                    else if (renkAdi.Contains("gri") || renkAdi.Contains("gray")) materyalRenkleri[renkAdi] = Color.gray;
                    else materyalRenkleri[renkAdi] = Color.white;
                }
                
                Debug.Log($"[MaterialBatchFix] Renk materyali bulundu: {mat.name}, Renk: {materyalRenkleri[renkAdi]}");
            }
        }
        
        Debug.Log($"[MaterialBatchFix] Toplam {materyalRenkleri.Count} renk materyali bulundu.");
    }

    /// <summary>
    /// Orijinal materyalleri geri yükler
    /// </summary>
    public void OrijinalMateryalleriGeriYukle()
    {
        int geriYuklenenSayisi = 0;

        foreach (var pair in orijinalMateryaller)
        {
            if (pair.Key != null)
            {
                pair.Key.sharedMaterials = pair.Value;
                geriYuklenenSayisi++;
            }
        }

        Debug.Log($"[MaterialBatchFix] {geriYuklenenSayisi} renderer'ın orijinal materyalleri geri yüklendi.");
    }

    private void OnDestroy()
    {
        // Singleton instance'ı temizle
        if (Instance == this)
        {
            Instance = null;
        }

        // Kalıcı değişiklik yapılmadıysa orijinal materyalleri geri yükle
        if (!kaliciDegisiklik)
        {
            OrijinalMateryalleriGeriYukle();
        }
    }
}
