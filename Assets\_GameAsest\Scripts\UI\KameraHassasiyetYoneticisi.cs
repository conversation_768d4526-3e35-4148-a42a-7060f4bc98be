using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Kamera hassasiyeti ayarlarını yöneten UI kontrolcüsü
/// Slider ile hassasiyet değerini ayarlar ve kaydeder
/// </summary>
public class KameraHassasiyetYoneticisi : MonoBehaviour
{
    [Header("UI Referansları")]
    [Tooltip("Hassasiyet ayarı için kullanılacak slider")]
    public Slider hassasiyetSlider;
    
    [Tooltip("Hassasiyet değerini gösteren metin (opsiyonel)")]
    public TextMeshProUGUI hassasiyetDegeriMetni;
    
    [Header("Hassasiyet Ayarları")]
    [Tooltip("Varsayılan hassasiyet değeri")]
    public float varsayilanHassasiyet = 50f;
    
    [Tooltip("Minimum hassasiyet değeri")]
    public float minHassasiyet = 10f;
    
    [<PERSON>lt<PERSON>("Maksimum hassasiyet değeri")]
    public float maxHassasiyet = 150f;
    
    [Header("Görsel Ayarlar")]
    [Tooltip("Hassasiyet değeri metninde gösterilecek ondalık basamak sayısı")]
    public int ondalikBasamakSayisi = 0;

    private InstantHybridCameraController kameraKontrolcusu;

    void Start()
    {
        // Slider'ı ayarla
        SliderAyarla();

        // Mevcut hassasiyet değerini PlayerPrefs'ten yükle
        KaydedilmisHassasiyetiYukle();
    }

    /// <summary>
    /// Slider'ın min/max değerlerini ayarlar ve event listener ekler
    /// </summary>
    private void SliderAyarla()
    {
        if (hassasiyetSlider == null)
        {
            Debug.LogError("KameraHassasiyetYoneticisi: Hassasiyet slider'ı atanmamış!");
            return;
        }

        // Slider değer aralığını ayarla
        hassasiyetSlider.minValue = minHassasiyet;
        hassasiyetSlider.maxValue = maxHassasiyet;
        
        // Slider değeri değiştiğinde çağrılacak metodu bağla
        hassasiyetSlider.onValueChanged.AddListener(HassasiyetDegisti);
    }

    /// <summary>
    /// Kaydedilmiş hassasiyet değerini PlayerPrefs'ten yükler ve UI'ya yansıtır
    /// </summary>
    private void KaydedilmisHassasiyetiYukle()
    {
        if (hassasiyetSlider == null) return;

        // PlayerPrefs'ten hassasiyet değerini al
        float kaydedilmisHassasiyet = PlayerPrefs.GetFloat("KameraHassasiyeti", varsayilanHassasiyet);

        // Slider'ın değerini güncelle (bu otomatik olarak HassasiyetDegisti metodunu çağıracak)
        hassasiyetSlider.value = kaydedilmisHassasiyet;
    }

    /// <summary>
    /// Slider değeri değiştiğinde çağrılan metod
    /// </summary>
    /// <param name="yeniDeger">Slider'ın yeni değeri</param>
    public void HassasiyetDegisti(float yeniDeger)
    {
        // Hassasiyet değerini PlayerPrefs'e kaydet
        PlayerPrefs.SetFloat("KameraHassasiyeti", yeniDeger);
        PlayerPrefs.Save();

        // Hassasiyet değeri metnini güncelle
        HassasiyetMetniniGuncelle(yeniDeger);

        // Eğer oyun sahnesindeyse ve kamera kontrolcüsü varsa, anlık güncelle
        if (InstantHybridCameraController.instance != null)
        {
            InstantHybridCameraController.instance.HassasiyetAyarla(yeniDeger);
        }
    }

    /// <summary>
    /// Hassasiyet değeri metnini günceller
    /// </summary>
    /// <param name="deger">Gösterilecek hassasiyet değeri</param>
    private void HassasiyetMetniniGuncelle(float deger)
    {
        if (hassasiyetDegeriMetni == null) return;

        // Ondalık basamak sayısına göre metni formatla
        string formatlanmisDeger = deger.ToString($"F{ondalikBasamakSayisi}");
        hassasiyetDegeriMetni.text = $"Hassasiyet: {formatlanmisDeger}";
    }

    /// <summary>
    /// Hassasiyeti varsayılan değere sıfırlar
    /// </summary>
    public void HassasiyetSifirla()
    {
        if (hassasiyetSlider == null) return;
        
        hassasiyetSlider.value = varsayilanHassasiyet;
    }

    /// <summary>
    /// Component destroy edildiğinde event listener'ları temizle
    /// </summary>
    private void OnDestroy()
    {
        if (hassasiyetSlider != null)
        {
            hassasiyetSlider.onValueChanged.RemoveListener(HassasiyetDegisti);
        }
    }

    /// <summary>
    /// Inspector'da değerler değiştiğinde slider'ı güncelle
    /// </summary>
    private void OnValidate()
    {
        // Editor'da çalışırken slider değerlerini güncelle
        if (hassasiyetSlider != null && Application.isPlaying)
        {
            hassasiyetSlider.minValue = minHassasiyet;
            hassasiyetSlider.maxValue = maxHassasiyet;
        }
    }
}
