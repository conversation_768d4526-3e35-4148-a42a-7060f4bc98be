using UnityEngine;
using Unity.Netcode;

public class SarkacHareketi : NetworkBehaviour
{
    public enum SalinimEkseni { X, Y, Z }

    [Header("<PERSON>rkaç Ayarları")]
    [Tooltip("Salınımın hangi eksende gerçekleşeceğini belirler.")]
    public SalinimEkseni salinimEkseni = SalinimEkseni.Z;
    public NetworkVariable<float> salinimHizi = new NetworkVariable<float>(1.0f);
    public NetworkVariable<float> salinimAcisi = new NetworkVariable<float>(90.0f);

    private NetworkVariable<double> serverStartTime = new NetworkVariable<double>(0.0d);
    private bool isServerTimeSet = false;

    void Update()
    {
        // YENİ GÜVENLİK ÖNLEMİ: NetworkManager'ın hazır olup olmadığını kontrol et.
        // Eğer sahnede NetworkManager yoksa veya henüz aktif değilse hata vermeden bekle.
        if (NetworkManager.Singleton == null)
        {
            return;
        }

        // Sadece sunucu üzerinde ve sadece bir kez çalışacak olan zamanı ayarlama mantığı.
        if (IsServer && !isServerTimeSet)
        {
            if (NetworkManager.Singleton.ServerTime.Time > 0)
            {
                serverStartTime.Value = NetworkManager.Singleton.ServerTime.Time;
                isServerTimeSet = true;
            }
        }

        // Zaman henüz sunucu tarafından ayarlanmadıysa bekle.
        if (serverStartTime.Value == 0.0d)
        {
            return;
        }

        // Hesaplama mantığı (DEĞİŞİKLİK YOK)
        double gecenSure = NetworkManager.Singleton.ServerTime.Time - serverStartTime.Value;
        float aci = salinimAcisi.Value * Mathf.Sin((float)gecenSure * salinimHizi.Value);

        Vector3 eulerAci = Vector3.zero;
        switch (salinimEkseni)
        {
            case SalinimEkseni.X:
                eulerAci.x = aci;
                break;
            case SalinimEkseni.Y:
                eulerAci.y = aci;
                break;
            case SalinimEkseni.Z:
                eulerAci.z = aci;
                break;
        }

        transform.localRotation = Quaternion.Euler(eulerAci);
    }
}