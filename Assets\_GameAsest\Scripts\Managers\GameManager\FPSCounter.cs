using UnityEngine;
using TMPro; // TextMeshPro kullanmak için bu kütüphaneyi ekliyoruz.

public class FPSCounter : MonoBehaviour
{
    // FPS metnini göstereceğimiz UI elemanı
    public TextMeshProUGUI fpsText;

    // FPS'i ne kadar sürede bir güncelleyeceğimizi belirler (örn: saniyede 1 kez)
    public float pollingTime = 1f;

    private float time;
    private int frameCount;

    void Update()
    {
        // Geçen zamanı ve kare sayısını topla
        time += Time.unscaledDeltaTime;
        frameCount++;

        // Belirlenen süre (pollingTime) dolduğunda...
        if (time >= pollingTime)
        {
            // FPS'i hesapla (kare sayısı / geçen süre)
            int frameRate = Mathf.RoundToInt(frameCount / time);

            // Metin alanını güncelle
            fpsText.text = "FPS: " + frameRate.ToString();

            // Sayaçları bir sonraki ölçüm için sıfırla
            time = 0f;
            frameCount = 0;
        }
    }
}