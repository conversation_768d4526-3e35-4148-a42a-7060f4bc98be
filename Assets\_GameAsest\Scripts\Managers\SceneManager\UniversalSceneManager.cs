using UnityEngine;
using Unity.Netcode;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.SceneManagement;

/// <summary>
/// Tüm sahnelerde kullanılabilecek genel sahne yönetici scripti.
/// GameManager olmayan sahnelerde sahne geçişlerini yönetir.
/// </summary>
public class UniversalSceneManager : NetworkBehaviour
{
    public static UniversalSceneManager Instance { get; private set; }

    [Header("Sahne Ayarları")]
    [Tooltip("Oynanacak mini oyun sahnelerinin listesi")]
    public string[] miniOyunSahneleri = {
        "RenkOyunu",
        "fakeblock_Scene"
    };

    [Header("Ana Menü Ayarları")]
    [Tooltip("Tüm mini oyunlar bittiğinde dönülecek ana menü sahnesinin adı")]
    public string anaMenuSahnesi = "MainMenu";

    [Header("Geçiş Ayarları")]
    [Tooltip("Sahne geçişi öncesi bekleme süresi")]
    public float sahneGecisiBeklemeSuresi = 3f;

    // Oynanan sahneleri takip etmek için (static - sahneler arası korunur)
    private static HashSet<string> oynananSahneler = new HashSet<string>();

    private void Awake()
    {
        // Singleton pattern with DontDestroyOnLoad
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(gameObject);
    }

    public override void OnNetworkSpawn()
    {
        if (IsServer)
        {
        }
    }

    /// <summary>
    /// Oyun bittiğinde çağrılacak metod (sadece server'da)
    /// </summary>
    public void OyunBitti()
    {
        if (!IsServer)
        {
            return;
        }

        StartCoroutine(SonrakiSahneGecis());
    }

    /// <summary>
    /// Client'lardan server'a oyun bitişi bildirimi
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    public void OyunBittiServerRpc()
    {
        OyunBitti();
    }

    private IEnumerator SonrakiSahneGecis()
    {
        if (!IsServer) yield break;

        // Kısa bir bekleme
        yield return new WaitForSeconds(sahneGecisiBeklemeSuresi);

        // Sonraki sahneyi seç
        string sonrakiSahne = GetSonrakiSahne();

        if (!string.IsNullOrEmpty(sonrakiSahne))
        {
            // Sahne değişmeden önce animasyon temizliği
        if (SafeAnimationSystem.Instance != null)
        {
            SafeAnimationSystem.Instance.CancelAllAnimations();
        }

        // Sahne değiştir
            if (NetworkManager.Singleton != null && NetworkManager.Singleton.SceneManager != null)
            {
                NetworkManager.Singleton.SceneManager.LoadScene(sonrakiSahne, LoadSceneMode.Single);
            }
        }
    }

    private string GetSonrakiSahne()
    {
        // Sahne listesini kontrol et
        if (miniOyunSahneleri == null || miniOyunSahneleri.Length == 0)
        {
            return anaMenuSahnesi;
        }

        // Mevcut sahneyi oynanan listesine ekle
        string mevcutSahne = SceneManager.GetActiveScene().name;
        if (miniOyunSahneleri.Contains(mevcutSahne))
        {
            oynananSahneler.Add(mevcutSahne);
        }

        // Henüz oynanmamış sahneleri bul
        var oynanmamisSahneler = miniOyunSahneleri.Where(sahne => 
            !string.IsNullOrEmpty(sahne) && 
            !oynananSahneler.Contains(sahne) && 
            sahne != mevcutSahne
        ).ToArray();

        // Eğer oynanmamış sahne varsa birini seç
        if (oynanmamisSahneler.Length > 0)
        {
            int rastgeleIndex = Random.Range(0, oynanmamisSahneler.Length);
            string secilenSahne = oynanmamisSahneler[rastgeleIndex];
            return secilenSahne;
        }

        // Tüm sahneler oynandıysa ana menüye dön
        // Oyun bittiğinde kazanılan parayı kaydet
        KazanilanParayiKaydet();

        // Oynanan sahneler listesini sıfırla (yeni oyun için)
        oynananSahneler.Clear();

        return anaMenuSahnesi;
    }

    /// <summary>
    /// Manuel sahne değiştirme (sadece server'da)
    /// </summary>
    public void ManuelSahneGecisi(string hedefSahne)
    {
        if (!IsServer)
        {
            return;
        }

        if (string.IsNullOrEmpty(hedefSahne))
        {
            return;
        }

        StartCoroutine(ManuelSahneGecisCoroutine(hedefSahne));
    }

    private IEnumerator ManuelSahneGecisCoroutine(string hedefSahne)
    {
        yield return new WaitForSeconds(1f);

        if (NetworkManager.Singleton != null && NetworkManager.Singleton.SceneManager != null)
        {
            NetworkManager.Singleton.SceneManager.LoadScene(hedefSahne, LoadSceneMode.Single);
        }
    }

    /// <summary>
    /// Debug: Sahne durumunu göster
    /// </summary>
    [ContextMenu("Debug: Sahne Durumunu Göster")]
    public void DebugSahneDurumu()
    {
        Debug.Log("=== UNIVERSAL SCENE MANAGER DURUMU ===");
        Debug.Log($"Mevcut sahne: {SceneManager.GetActiveScene().name}");
        Debug.Log($"Oynanan sahne sayısı: {oynananSahneler.Count}/{miniOyunSahneleri.Length}");
    }

    /// <summary>
    /// Debug: Oynanan sahneleri sıfırla
    /// </summary>
    [ContextMenu("Debug: Oynanan Sahneleri Sıfırla")]
    public void DebugOynananSahneleriSifirla()
    {
        oynananSahneler.Clear();
        Debug.Log("🔄 UniversalSceneManager: Oynanan sahneler listesi sıfırlandı!");
    }

    /// <summary>
    /// Oyun bittiğinde kazanılan parayı PlayerData'ya kaydet
    /// </summary>
    private void KazanilanParayiKaydet()
    {
        // Sadece local player'ın parasını kaydet
        if (NetworkManager.Singleton != null && NetworkManager.Singleton.LocalClient != null)
        {
            var localPlayerObject = NetworkManager.Singleton.LocalClient.PlayerObject;
            if (localPlayerObject != null)
            {
                var oyuncuKontrol = localPlayerObject.GetComponent<OyuncuKontrol>();
                if (oyuncuKontrol != null)
                {
                    int kazanilanPara = oyuncuKontrol.puan.Value;
                    if (kazanilanPara > 0)
                    {
                        PlayerData.ParaEkle(kazanilanPara);
                    }
                }
            }
        }
        else
        {
        }
    }



    public override void OnDestroy()
    {
        // Güvenli animasyon sistemi temizliği
        if (SafeAnimationSystem.TryCancelAllAnimations())
        {
            Debug.Log("✅ UniversalSceneManager: Animasyon temizlik tamamlandı");
        }

        if (Instance == this)
        {
            Instance = null;
        }
    }
}
