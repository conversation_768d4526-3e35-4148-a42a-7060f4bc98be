using UnityEngine;

public class YuruyenBant : MonoBehaviour
{
    [<PERSON><PERSON>("Bant Ayarları")]
    [Toolt<PERSON>("Bandın oyuncuyu iteceği yön (objenin kendi local yönüne göre).")]
    public Vector3 hareketYonu = Vector3.forward; // forward = objenin mavi oku (Z ekseni) yönü

    [Tooltip("Bandın itme hızı.")]
    public float bantHizi = 3f;

    // Oyuncu trigger alanının İÇİNDEYKEN HER KAREDE bu fonksiyon çalışır.
    private void OnTriggerStay(Collider other)
    {
        if (other.CompareTag("Player"))
        {
            // DEĞİŞİKLİK: "OyuncuKontrol" yerine "IEtkilesimeGirebilir" arıyoruz.
            IEtkilesimeGirebilir etkilesenObje = other.GetComponentInParent<IEtkilesimeGirebilir>();

            if (etkilesenObje != null)
            {
                // Hareket yönünü objenin local yönünden dünya yönüne çevirip hızla çarpıyoruz.
                Vector3 dunyaYonuKuvveti = transform.TransformDirection(hareketYonu.normalized) * bantHizi;
                
                // Komutu arayüz üzerinden veriyoruz.
                etkilesenObje.HariciHareketiAyarla(dunyaYonuKuvveti);
            }
        }
    }

    // Oyuncu trigger alanından ÇIKTIĞI ANDA bu fonksiyon çalışır.
    private void OnTriggerExit(Collider other)
    {
        if (other.CompareTag("Player"))
        {
            // DEĞİŞİKLİK: Burada da "OyuncuKontrol" yerine "IEtkilesimeGirebilir" arıyoruz.
            IEtkilesimeGirebilir etkilesenObje = other.GetComponentInParent<IEtkilesimeGirebilir>();

            if (etkilesenObje != null)
            {
                // Hızı sıfırlama komutunu da arayüz üzerinden veriyoruz.
                etkilesenObje.HariciHareketiSifirla();
            }
        }
    }

    // Sahnede bandın yönünü gösteren bir ok çizmek için (isteğe bağlı, yardımcı)
    private void OnDrawGizmos()
    {
        Gizmos.color = Color.green;
        Vector3 dunyaYonu = transform.TransformDirection(hareketYonu.normalized);
        Gizmos.DrawRay(transform.position + Vector3.up * 0.5f, dunyaYonu * bantHizi);
    }
}