using UnityEngine;

public class KameraTakip : MonoBehaviour
{
    [Head<PERSON>("Ayarlar")]
    public Transform target; // Takip edilecek hedef (Bizim <PERSON> ka<PERSON>)
    public float mesafe = 5.0f; // Kameranın hedefe olan uzaklığı
    public float yukseklik = 2.0f; // Kameranın hedeften ne kadar yukarıda olacağı
    public float fareHassasiyeti = 2.0f; // Farenin dönüş hızı
    public float yumusaklik = 0.15f; // Kameranın takip yumuşaklığı

    private float yaw = 0.0f; // Ya<PERSON><PERSON> d<PERSON> (Y ekseni etrafında)
    private float pitch = 20.0f; // Dikey dön<PERSON> (X ekseni etrafında)
    
    private Vector3 mevcutRotasyon;
    private Vector3 rotasyonHizi;

    void LateUpdate()
    {
        if (target == null)
        {
            Debug.LogError("Kameranın hedefi (Target) atanmamış!");
            return;
        }

        // <PERSON>e hareketi<PERSON> al
        yaw += Input.GetAxis("Mouse X") * fareHassasiyeti;
        pitch -= Input.GetAxis("Mouse Y") * fareHassasiyeti;

        // Dikey açıyı kilitle ki kamera ters dönmesin
        pitch = Mathf.Clamp(pitch, 5f, 60f);

        // Hedef rotasyonu yumuşak bir şekilde hesapla
        Vector3 hedefRotasyon = new Vector3(pitch, yaw);
        mevcutRotasyon = Vector3.SmoothDamp(mevcutRotasyon, hedefRotasyon, ref rotasyonHizi, yumusaklik);

        // Kameranın rotasyonunu ayarla
        transform.eulerAngles = mevcutRotasyon;

        // Kameranın pozisyonunu hedefin arkasına, mesafeye ve yüksekliğe göre ayarla
        // Önce hedefin pozisyonuna git, sonra geriye ve yukarıya doğru hareket et
        transform.position = target.position - transform.forward * mesafe + Vector3.up * yukseklik;
    }
}