# Geliştirilmiş Kapı Engeli Planı: "Dikey Kapı Geçidi"

Hari<PERSON>, anladım! İstediğiniz mekanik çok daha dinamik ve zorlayıcı. `Fall Guys`'daki "Gate Crash" b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aşa<PERSON><PERSON> yukarı hareket eden kapılar yapacağız. Her kapının kendi ritmi olacak. İşte bu yeni konsepte göre güncellenmiş planımız:

## 🚪 Yeni Konsept: Dikey Kapı Geçidi

Oyuncular, art arda sıralanmış duvarlarla karşılaşacak. Her duvarda birden fazla dikey hareket eden kapı (giyot_n gibi) bulunacak. Ka<PERSON><PERSON><PERSON> farklı zamanlamalarla, farklı hızlarda ve farklı bekleme süreleriyle aşağı yukarı hareket edecek. Amaç, doğru ritmi yakalayıp kapıların altından en hızlı şekilde geçmek.

### Temel Özellikler:
-   **Dikey Hareket:** <PERSON><PERSON><PERSON><PERSON> sağ<PERSON> so<PERSON> değ<PERSON>, yukarı aşağı hareket edecek.
-   **Asenkron Zamanlama:** Her kapının açılış hızı, kapanış hızı ve açık kalma süresi birbirinden bağımsız ve rastgele olacak.
-   **Kırılma Yok:** Kapılar kırılmaz. Oyuncular kapalı bir kapıya çarparsa durdurulur veya geri sektirilir.
-   **Dinamik Zorluk:** Oyun ilerledikçe kapıların hareket hızı artabilir ve açık kalma süreleri kısalabilir.

## 🏗️ Güncellenmiş Mimari ve İşleyiş

Bu sistem, her kapının kendi zamanlamasını yönetebilmesi için daha dağıtık bir yapıya sahip olacak.

```mermaid
graph TD
    subgraph Server-Side Logic
        A[GateCrashManager] -- 1. Turu Başlat --> B(Tüm Kapılara Başlangıç Sinyali);
        B -- 2. Her Kapıya Rastgele Parametreler Ata --> C[Hız, Bekleme Süresi vb.];
        C -- 3. NetworkVariable ile parametreleri senkronize et --> D[Tüm Client'lara Kapı Ayarlarını Gönder];
    end

    subgraph Client-Side Logic
        E[Her VerticalDoorController] -- 4. Sunucudan parametreleri al --> F{Kendi Zaman Döngüsünü Başlat};
        F -- Döngü içinde --> G[Yukarı Hareket Et (Açıl)];
        G -- Açık kalma süresi kadar bekle --> H[Aşağı Hareket Et (Kapan)];
        H -- Kapalı kalma süresi kadar bekle --> F;
    end

    subgraph Oyuncu Etkileşimi
        I[Oyuncu] -- Doğru zamanda koşar --> J[Açık Kapıdan Geçer];
        I -- Yanlış zamanda koşar --> K[Kapanan Kapıya Çarpar];
        K -- Çarpma --> L[Oyuncu Durur / Geri Seker];
    end

    A --> D;
    D --> E;
```

### Güncellenmiş İşleyiş Adımları:

1.  **Başlangıç (Server):** `GateCrashManager` sahnedeki tüm `VerticalDoorController`'ları bulur.
2.  **Parametre Atama (Server):** Her bir `VerticalDoorController` için rastgele değerler üretir:
    *   `movementSpeed`: Kapının yukarı/aşağı hareket hızı.
    *   `openTime`: Kapının yukarıda bekleme süresi.
    *   `closedTime`: Kapının aşağıda bekleme süresi.
    *   `initialDelay`: Kapının harekete başlamadan önceki ilk gecikmesi.
3.  **Senkronizasyon (Server -> Client):** Bu rastgele parametreleri her kapının kendi `NetworkVariable`'ları aracılığıyla client'lara senkronize eder. Bu sayede her oyuncu, her kapıyı tamamen aynı ritimde hareket ederken görür.
4.  **Bağımsız Hareket (Client):** Her `VerticalDoorController` script'i, sunucudan aldığı bu parametrelerle kendi hareket döngüsünü **bağımsız olarak** yönetir. Bu, sunucu üzerinde sürekli bir yük oluşturmaz ve daha akıcı bir görüntü sağlar.
5.  **Etkileşim (Client & Server):** Oyuncu kapalı bir kapıya çarptığında, `Collider`'ı sayesinde fiziksel olarak engellenir.

### 🗂️ Güncellenmiş Dosya Listesi

-   **Scriptler:**
    -   `Scripts/EngelScript/GateCrash/GateCrashManager.cs` (Genel ayarlar ve tur yönetimi için)
    -   `Scripts/EngelScript/GateCrash/VerticalDoorController.cs` (Tek bir dikey kapının tüm mantığını içerecek)
-   **Prefab:**
    -   `Prefabs/Engel/GateCrash/VerticalDoor.prefab` (Dikey hareket edecek kapı modeli ve script'ini içerir)
-   **Sahne:**
    -   `_Scenes/Minigame/GateCrash_Scene.unity`

Bu güncellenmiş plan, istediğiniz dinamik ve zorlayıcı engel mekaniğini tam olarak karşılıyor. Onaylıyor musunuz?