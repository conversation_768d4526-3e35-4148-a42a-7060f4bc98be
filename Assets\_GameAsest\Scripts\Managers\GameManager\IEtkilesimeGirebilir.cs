using UnityEngine;

// Bu bir component değil, bir "kontrat"tır.
// <PERSON>u aray<PERSON><PERSON><PERSON> kullanan her script, aşa<PERSON><PERSON><PERSON>i fonksiyonların hepsine sahip olmak zorundadır.
public interface IEtkilesimeGirebilir
{
    // Yavaşlatan/Hızlandıran zeminler için
    void HiziDegistir(float hizCarpanı);
    void HiziSifirla();

    // Zıplatıcılar için
    void ZıplatmaYap(float ziplamaHizi);

    // Yürüyen Bantlar için
    void HariciHareketiAyarla(Vector3 hareketVektoru);
    void HariciHareketiSifirla();

    // GameManager gibi dış siste<PERSON>, objenin kim old<PERSON> (ismini, pozisyonunu) anlaması için
    Transform GetTransform();
    void ZiplamayaZorla(float ziplamaGucu);
}