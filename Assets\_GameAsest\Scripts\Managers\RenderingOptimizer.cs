using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Rendering optimizasyonu yapan ve mobil cihazlarda performansı artıran sınıf.
/// Singleton pattern kullanır ve oyun başladığında otomatik olarak çalışır.
/// </summary>
public class RenderingOptimizer : MonoBehaviour
{
    // Singleton instance
    public static RenderingOptimizer Instance { get; private set; }

    [Header("Platform Ayarları")]
    [Tooltip("Mobil platformda otomatik optimizasyon yapılsın mı?")]
    public bool optimizeMobile = true;

    [Tooltip("PC platformunda da optimizasyon yapılsın mı?")]
    public bool optimizePC = false;

    [Header("LOD Ayarları")]
    [Tooltip("LOD mesafe çarpanı (düşük değer = daha erken LOD geçişi)")]
    [Range(0.1f, 1.0f)]
    public float lodBiasMultiplier = 0.5f;

    [Tooltip("Maximum LOD seviyesi (0 = en detaylı, yüksek = daha az detaylı)")]
    [Range(0, 3)]
    public int maximumLODLevel = 2;

    [Header("Culling Ayarları")]
    [Tooltip("Kamera görüş mesafesi çarpanı (düşük değer = daha az uzak obje)")]
    [Range(0.1f, 1.0f)]
    public float cameraFarClipMultiplier = 0.7f;

    [Header("Gölge Ayarları")]
    [Tooltip("Gölge mesafesi çarpanı (düşük değer = daha yakın gölgeler)")]
    [Range(0.1f, 1.0f)]
    public float shadowDistanceMultiplier = 0.5f;

    [Tooltip("Gölge çözünürlüğü (düşük değer = daha düşük kalite)")]
    public UnityEngine.ShadowResolution shadowResolution = UnityEngine.ShadowResolution.Medium;

    [Tooltip("Gölge cascade sayısı (1 = en düşük kalite, 4 = en yüksek kalite)")]
    [Range(1, 4)]
    public int shadowCascadeCount = 1;

    [Header("Parçacık Ayarları")]
    [Tooltip("Parçacık sayısı çarpanı (düşük değer = daha az parçacık)")]
    [Range(0.1f, 1.0f)]
    public float particleCountMultiplier = 0.5f;

    [Tooltip("Maksimum parçacık sayısı")]
    public int maxParticleCount = 1000;

    [Header("Batching Ayarları")]
    [Tooltip("Static batching zorla etkinleştirilsin mi?")]
    public bool forceStaticBatching = true;

    [Tooltip("Dynamic batching zorla etkinleştirilsin mi?")]
    public bool forceDynamicBatching = true;

    [Header("Diğer Ayarlar")]
    [Tooltip("Pixel Light sayısı (düşük değer = daha az ışık)")]
    [Range(0, 4)]
    public int pixelLightCount = 1;

    [Tooltip("Texture kalitesi (düşük değer = daha düşük kalite)")]
    [Range(0, 3)]
    public int textureQuality = 1;

    [Tooltip("Anti-aliasing seviyesi")]
    public int antiAliasing = 0;

    [Tooltip("Soft particles kapatılsın mı?")]
    public bool disableSoftParticles = true;

    [Tooltip("Realtime reflection probes kapatılsın mı?")]
    public bool disableRealtimeReflections = true;

    private void Awake()
    {
        // Singleton pattern
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(gameObject);

        // Hemen optimizasyonu başlat
        ApplyOptimizations();
    }

    /// <summary>
    /// Tüm optimizasyonları uygular
    /// </summary>
    public void ApplyOptimizations()
    {
        // Platform kontrolü
        bool isMobile = Application.isMobilePlatform || SystemInfo.deviceType == DeviceType.Handheld;

        // Eğer mobil platformsa ve mobil optimizasyon açıksa veya
        // PC platformuysa ve PC optimizasyon açıksa devam et
        if ((isMobile && optimizeMobile) || (!isMobile && optimizePC))
        {
            Debug.Log($"[RenderingOptimizer] {(isMobile ? "Mobil" : "PC")} platformu için optimizasyonlar uygulanıyor...");

            // Quality Settings optimizasyonları
            OptimizeQualitySettings(isMobile);

            // Camera optimizasyonları
            OptimizeCameras();

            // LOD optimizasyonları
            OptimizeLODGroups();

            // Parçacık optimizasyonları
            OptimizeParticleSystems();

            // URP ayarlarını optimize et (eğer URP kullanılıyorsa)
            OptimizeURPSettings(isMobile);

            // Static batching uygula
            ApplyBatchingOptimizations();

            Debug.Log("[RenderingOptimizer] Tüm optimizasyonlar tamamlandı!");
        }
        else
        {
            Debug.Log($"[RenderingOptimizer] {(isMobile ? "Mobil" : "PC")} platformu için optimizasyon yapılmayacak.");
        }
    }

    /// <summary>
    /// Quality Settings optimizasyonları
    /// </summary>
    private void OptimizeQualitySettings(bool isMobile)
    {
        // Pixel light sayısını ayarla
        QualitySettings.pixelLightCount = pixelLightCount;

        // Texture kalitesini ayarla
        QualitySettings.globalTextureMipmapLimit = textureQuality;

        // LOD bias ayarla
        QualitySettings.lodBias = lodBiasMultiplier;

        // Maximum LOD seviyesini ayarla
        QualitySettings.maximumLODLevel = maximumLODLevel;

        // Anti-aliasing ayarla
        QualitySettings.antiAliasing = antiAliasing;

        // Soft particles kapat
        if (disableSoftParticles)
            QualitySettings.softParticles = false;

        // Realtime reflection probes kapat
        if (disableRealtimeReflections)
            QualitySettings.realtimeReflectionProbes = false;

        // Shadow ayarları
        if (isMobile)
        {
            // Gölge mesafesini ayarla
            QualitySettings.shadowDistance *= shadowDistanceMultiplier;

            // Gölge çözünürlüğünü ayarla
            QualitySettings.shadowResolution = shadowResolution;

            // Gölge cascade sayısını ayarla
            QualitySettings.shadowCascades = shadowCascadeCount;
        }

        Debug.Log($"[RenderingOptimizer] Quality Settings optimizasyonları uygulandı: " +
                  $"LOD Bias={QualitySettings.lodBias}, " +
                  $"Max LOD={QualitySettings.maximumLODLevel}, " +
                  $"Pixel Lights={QualitySettings.pixelLightCount}, " +
                  $"Shadow Dist={QualitySettings.shadowDistance}");
    }

    /// <summary>
    /// Kamera optimizasyonları
    /// </summary>
    private void OptimizeCameras()
    {
        // Tüm kameraları bul
        Camera[] allCameras = FindObjectsByType<Camera>(FindObjectsSortMode.None);

        foreach (Camera cam in allCameras)
        {
            // Ana kamera mı kontrol et
            if (cam.tag == "MainCamera" || cam.tag == "PlayerCamera")
            {
                // Far clip plane'i azalt
                cam.farClipPlane *= cameraFarClipMultiplier;

                // Occlusion culling'i etkinleştir
                cam.useOcclusionCulling = true;

                Debug.Log($"[RenderingOptimizer] Kamera '{cam.name}' optimizasyonu: " +
                          $"Far Clip={cam.farClipPlane}, " +
                          $"Occlusion Culling={cam.useOcclusionCulling}");
            }
        }
    }

    /// <summary>
    /// LOD gruplarını optimize eder
    /// </summary>
    private void OptimizeLODGroups()
    {
        // Tüm LOD gruplarını bul
        LODGroup[] lodGroups = FindObjectsByType<LODGroup>(FindObjectsSortMode.None);

        foreach (LODGroup lodGroup in lodGroups)
        {
            // Mevcut LOD'ları al
            LOD[] lods = lodGroup.GetLODs();

            // Her LOD seviyesi için mesafeyi azalt
            for (int i = 0; i < lods.Length; i++)
            {
                // Mesafeyi azalt (daha erken LOD geçişi)
                lods[i].screenRelativeTransitionHeight /= lodBiasMultiplier;
            }

            // Değiştirilmiş LOD'ları ayarla
            lodGroup.SetLODs(lods);
        }

        Debug.Log($"[RenderingOptimizer] {lodGroups.Length} LOD grubu optimize edildi.");
    }

    /// <summary>
    /// Parçacık sistemlerini optimize eder
    /// </summary>
    private void OptimizeParticleSystems()
    {
        // Tüm parçacık sistemlerini bul
        ParticleSystem[] particleSystems = FindObjectsByType<ParticleSystem>(FindObjectsSortMode.None);

        foreach (ParticleSystem ps in particleSystems)
        {
            // Ana modülü al
            var main = ps.main;

            // Maksimum parçacık sayısını azalt
            int originalMaxParticles = main.maxParticles;
            main.maxParticles = Mathf.Min(originalMaxParticles, maxParticleCount);
            main.maxParticles = Mathf.RoundToInt(main.maxParticles * particleCountMultiplier);

            // Emisyon oranını azalt
            var emission = ps.emission;
            if (emission.enabled)
            {
                var rate = emission.rateOverTime;
                rate.constant *= particleCountMultiplier;
                emission.rateOverTime = rate;
            }

            // Collision'ı basitleştir
            var collision = ps.collision;
            if (collision.enabled)
            {
                collision.quality = ParticleSystemCollisionQuality.Low;
            }
        }

        Debug.Log($"[RenderingOptimizer] {particleSystems.Length} parçacık sistemi optimize edildi.");
    }

    /// <summary>
    /// URP ayarlarını optimize eder
    /// </summary>
    private void OptimizeURPSettings(bool isMobile)
    {
        // URP asset'i bulmaya çalış
        UniversalRenderPipelineAsset urpAsset = null;

        // Aktif render pipeline'ı kontrol et
        if (GraphicsSettings.currentRenderPipeline is UniversalRenderPipelineAsset)
        {
            urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
        }

        // URP asset bulunamadıysa çık
        if (urpAsset == null)
        {
            Debug.LogWarning("[RenderingOptimizer] URP asset bulunamadı, URP optimizasyonları atlanıyor.");
            return;
        }

        // Mobil için URP optimizasyonları
        if (isMobile)
        {
            // Render scale azalt
            urpAsset.renderScale = 0.8f;

            // Depth texture kapat
            urpAsset.supportsCameraDepthTexture = false;

            // Opaque texture kapat
            urpAsset.supportsCameraOpaqueTexture = false;

            // HDR kapat
            urpAsset.supportsHDR = false;

            // MSAA kapat
            urpAsset.msaaSampleCount = 1;

            // Shadow distance azalt
            urpAsset.shadowDistance = urpAsset.shadowDistance * shadowDistanceMultiplier;

            Debug.Log("[RenderingOptimizer] URP mobil optimizasyonları uygulandı: " +
                      $"Render Scale={urpAsset.renderScale}, " +
                      $"Depth Texture={urpAsset.supportsCameraDepthTexture}, " +
                      $"Shadow Distance={urpAsset.shadowDistance}");
        }
    }

    /// <summary>
    /// Batching optimizasyonlarını uygular
    /// </summary>
    private void ApplyBatchingOptimizations()
    {
        // Static batching'i etkinleştir
        if (forceStaticBatching)
        {
            GraphicsSettings.useScriptableRenderPipelineBatching = true;
        }

        // Dynamic batching'i etkinleştir
        if (forceDynamicBatching)
        {
            // Not: Bu ayar sadece proje ayarlarında değiştirilebilir
            // Burada sadece URP asset üzerinden değiştirilebilir
            if (GraphicsSettings.currentRenderPipeline is UniversalRenderPipelineAsset urpAsset)
            {
                // URP'de dynamic batching'i etkinleştir
                // Bu yansıma ile yapılmalı ama URP API buna izin vermiyor
                // Bu yüzden sadece log yazıyoruz
                Debug.Log("[RenderingOptimizer] Dynamic batching URP asset üzerinden etkinleştirilmelidir.");
            }
        }

        // Tüm statik objeleri bul ve static batching'e zorla
        if (forceStaticBatching)
        {
            // Tüm MeshRenderer'ları bul
            MeshRenderer[] meshRenderers = FindObjectsByType<MeshRenderer>(FindObjectsSortMode.None);
            int forcedStaticCount = 0;

            foreach (MeshRenderer renderer in meshRenderers)
            {
                // Eğer hareket etmeyen bir obje ise ve batching flag'i yoksa
                if (!renderer.gameObject.isStatic && 
                    !renderer.GetComponent<Rigidbody>() && 
                    !renderer.GetComponent<Animator>())
                {
                    // Runtime'da editör bayrakları kullanılamaz; doğrudan statik olarak işaretle
                    renderer.gameObject.isStatic = true;
                    forcedStaticCount++;
                }
            }

            Debug.Log($"[RenderingOptimizer] {forcedStaticCount} obje static batching için işaretlendi.");
        }
    }

    // Editor-only yardımcı sınıf ve StaticEditorFlags kullanımları kaldırıldı; 
    // runtime'da gameObject.isStatic ile işaretleme yeterlidir.
}
