using UnityEngine;
using Unity.Netcode;
using System.Collections;

/// <summary>
/// RenkOyunu sahnesinde oyunu başlatan ve oyuncu sayısını kontrol eden script
/// </summary>
public class RenkOyunuGameStarter : NetworkBehaviour
{
    [Header("Oyun Başlangıç Ayarları")]
    public int minimumOyuncuSayisi = 1; // Test için 1, gerçek oyunda daha fazla olabilir
    public float oyuncuBeklemeZamani = 2f; // Kısa bekleme süresi
    public bool otomatikBaslat = true; // Lobi sisteminden geldiği için otomatik başlat
    public bool lobbySystemActive = true; // Lobi sistemi aktif
    
    [Header("Debug")]
    public bool showDebugLogs = true;
    
    private NetworkVariable<int> bagliOyuncuSayisi = new NetworkVariable<int>(0, 
        NetworkVariableReadPermission.Everyone, 
        NetworkVariableWritePermission.Server);
    
    private NetworkVariable<bool> oyunBasladi = new NetworkVariable<bool>(false,
        NetworkVariableReadPermission.Everyone, 
        NetworkVariableWritePermission.Server);
    
    private bool oyunBaslatildi = false;
    
    public override void OnNetworkSpawn()
    {
        if (IsServer)
        {
            // Bağlı oyuncu sayısını güncelle
            UpdatePlayerCount();

            // Network event'leri dinle
            NetworkManager.Singleton.OnClientConnectedCallback += OnClientConnected;
            NetworkManager.Singleton.OnClientDisconnectCallback += OnClientDisconnected;

            // Lobi sisteminden geldiği için otomatik başlat
            if (lobbySystemActive && otomatikBaslat)
            {
                if (showDebugLogs)
                    Debug.Log("RenkOyunuGameStarter: Lobi sisteminden gelindi, oyun otomatik başlatılıyor...");

                // Kısa bir bekleme sonrası oyunu başlat
                StartCoroutine(LobidenGelenOyunuBaslat());
            }
            else if (otomatikBaslat && !lobbySystemActive)
            {
                StartCoroutine(OyunBaslatmaKontrolü());
            }
        }
        
        // Client tarafında UI güncellemeleri
        if (IsClient)
        {
            bagliOyuncuSayisi.OnValueChanged += OnPlayerCountChanged;
            oyunBasladi.OnValueChanged += OnGameStartedChanged;
        }
    }
    
    public override void OnNetworkDespawn()
    {
        if (IsServer)
        {
            if (NetworkManager.Singleton != null)
            {
                NetworkManager.Singleton.OnClientConnectedCallback -= OnClientConnected;
                NetworkManager.Singleton.OnClientDisconnectCallback -= OnClientDisconnected;
            }
        }
    }
    
    private void OnClientConnected(ulong clientId)
    {
        if (!IsServer) return;
        
        UpdatePlayerCount();
        
        if (showDebugLogs)
            Debug.Log($"RenkOyunuGameStarter: Oyuncu bağlandı. Toplam: {bagliOyuncuSayisi.Value}");
    }
    
    private void OnClientDisconnected(ulong clientId)
    {
        if (!IsServer) return;
        
        UpdatePlayerCount();
        
        if (showDebugLogs)
            Debug.Log($"RenkOyunuGameStarter: Oyuncu ayrıldı. Toplam: {bagliOyuncuSayisi.Value}");
    }
    
    private void UpdatePlayerCount()
    {
        if (!IsServer) return;
        
        bagliOyuncuSayisi.Value = NetworkManager.Singleton.ConnectedClients.Count;
    }
    
    private void OnPlayerCountChanged(int oldValue, int newValue)
    {
        if (showDebugLogs)
            Debug.Log($"RenkOyunuGameStarter: Oyuncu sayısı değişti: {oldValue} -> {newValue}");
        
        // UI güncellemesi burada yapılabilir
        UpdateUI();
    }
    
    private void OnGameStartedChanged(bool oldValue, bool newValue)
    {
        if (newValue && !oldValue)
        {
            if (showDebugLogs)
                Debug.Log("RenkOyunuGameStarter: Oyun başladı!");
            
            // Oyun başladığında UI'ı güncelle
            UpdateUI();
        }
    }
    
    private IEnumerator LobidenGelenOyunuBaslat()
    {
        if (showDebugLogs)
            Debug.Log("RenkOyunuGameStarter: Lobi sisteminden gelen oyuncular için oyun başlatılıyor...");

        // Kısa bir bekleme (network spawn'ların tamamlanması için)
        yield return new WaitForSeconds(oyuncuBeklemeZamani);

        // Oyuncu sayısını güncelle
        UpdatePlayerCount();

        if (showDebugLogs)
            Debug.Log($"RenkOyunuGameStarter: Mevcut oyuncu sayısı: {bagliOyuncuSayisi.Value}");

        // Oyunu direkt başlat (lobi sisteminden geldiği için minimum kontrol yok)
        if (!oyunBaslatildi)
        {
            BaslatOyun();
        }
    }

    private IEnumerator OyunBaslatmaKontrolü()
    {
        if (showDebugLogs)
            Debug.Log("RenkOyunuGameStarter: Oyun başlatma kontrolü başladı...");

        // Oyuncuları bekle
        yield return new WaitForSeconds(oyuncuBeklemeZamani);

        // Minimum oyuncu sayısı kontrolü
        while (bagliOyuncuSayisi.Value < minimumOyuncuSayisi && !oyunBaslatildi)
        {
            if (showDebugLogs)
                Debug.Log($"RenkOyunuGameStarter: Minimum oyuncu sayısı bekleniyor. Mevcut: {bagliOyuncuSayisi.Value}, Minimum: {minimumOyuncuSayisi}");

            yield return new WaitForSeconds(1f);
        }

        // Oyunu başlat
        if (!oyunBaslatildi)
        {
            BaslatOyun();
        }
    }
    
    [ServerRpc(RequireOwnership = false)]
    public void ManuelOyunBaslatServerRpc()
    {
        // Güvenlik kontrolleri
        if (!IsServer)
        {
            Debug.LogWarning("RenkOyunuGameStarter: ManuelOyunBaslatServerRpc sadece server'da çalışabilir!");
            return;
        }

        if (!IsSpawned || NetworkObject == null)
        {
            Debug.LogWarning("RenkOyunuGameStarter: NetworkObject aktif değil, RPC çağrısı iptal edildi!");
            return;
        }

        if (oyunBaslatildi)
        {
            Debug.Log("RenkOyunuGameStarter: Oyun zaten başlatılmış, tekrar başlatma iptal edildi.");
            return;
        }

        BaslatOyun();
    }

    public void BaslatOyun()
    {
        if (!IsServer || oyunBaslatildi) return;

        oyunBaslatildi = true;
        oyunBasladi.Value = true;

        // RenkOyunuManager'a oyun başladığını bildir
        RenkOyunuManager renkManager = RenkOyunuManager.Instance;
        if (renkManager == null)
        {
            Debug.LogError("RenkOyunuGameStarter: RenkOyunuManager bulunamadı!");
        }
        
        // Tüm oyunculara oyun başladığını bildir
        OyunBasladiClientRpc();
    }
    
    [ClientRpc]
    private void OyunBasladiClientRpc()
    {
        if (showDebugLogs)
            Debug.Log("RenkOyunuGameStarter: Oyun başladı mesajı alındı!");
        
        // Oyunculara oyun başladığını göster
        UpdateUI();
    }
    
    private void UpdateUI()
    {
        // ArayuzYoneticisi varsa güncelle
        ArayuzYoneticisi ui = ArayuzYoneticisi.Instance;
        if (ui != null)
        {
            // UI güncellemesi burada yapılabilir
        }
        
        // Oyuncu sayısını göster (debug için)
        if (showDebugLogs)
        {
            string status = oyunBasladi.Value ? "BAŞLADI" : "BEKLİYOR";
            Debug.Log($"RenkOyunuGameStarter UI: Oyuncu: {bagliOyuncuSayisi.Value}, Durum: {status}");
        }
    }
    
    // Manuel kontrol metodları
    [ContextMenu("Manuel Oyun Başlat")]
    public void ManuelOyunBaslat()
    {
        if (IsServer)
        {
            BaslatOyun();
        }
        else
        {
            ManuelOyunBaslatServerRpc();
        }
    }
    
    [ContextMenu("Oyuncu Sayısını Güncelle")]
    public void ManuelOyuncuSayisiGuncelle()
    {
        if (IsServer)
        {
            UpdatePlayerCount();
        }
    }
    
    [ContextMenu("Durum Raporu")]
    public void DurumRaporu()
    {
        Debug.Log("=== RENK OYUNU DURUM RAPORU ===");
        Debug.Log($"Bağlı Oyuncu Sayısı: {bagliOyuncuSayisi.Value}");
        Debug.Log($"Minimum Oyuncu Sayısı: {minimumOyuncuSayisi}");
        Debug.Log($"Oyun Başladı: {oyunBasladi.Value}");
    }
}
