using UnityEngine;
using Unity.Netcode;

public class TomrukSpawner : NetworkBehaviour
{
    [<PERSON><PERSON>("Spawn Ayarları")]
    [Toolt<PERSON>("Spawn edilecek tomruk prefab'ı")]
    [SerializeField] private GameObject tomrukPrefab;

    [Toolt<PERSON>("Tomruklar arasındaki süre (saniye)")]
    [SerializeField] private float spawnAraligi = 3f;

    [<PERSON><PERSON><PERSON>("Rastgele spawn aralığı ekle")]
    [SerializeField] private bool rastgeleAralik = false;

    [Tooltip("Minimum spawn aralığı (rastgele aktifse) - ÖNERİLEN: En az 1.5")]
    [SerializeField] private float minSpawnAraligi = 1.5f;

    [Tooltip("Maksimum spawn aralığı (rastgele aktifse) - ÖNERİLEN: En az 3")]
    [SerializeField] private float maxSpawnAraligi = 4f;

    [Header("Spawn Pozisyon Ayarları")]
    [Tooltip("Spawn pozisyonunu rastgele yap")]
    [SerializeField] private bool rastgelePozisyon = false;

    [Tooltip("X ekseninde rastgele aralık")]
    [SerializeField] private Vector2 xAraligi = new Vector2(-2f, 2f);

    [Tooltip("Y ekseninde rastgele aralık")]
    [SerializeField] private Vector2 yAraligi = new Vector2(0f, 0f);

    [Tooltip("Z ekseninde rastgele aralık")]
    [SerializeField] private Vector2 zAraligi = new Vector2(-2f, 2f);

    [Header("Tomruk Hız Ayarları")]
    [Tooltip("Spawn edilen tomrukların hızını override et")]
    [SerializeField] private bool hizAyarlariniUygula = false;

    [Tooltip("Spawn edilen tomrukların yuvarlanma kuvveti")]
    [SerializeField] private float spawnYuvarlanmaKuvveti = 10f;

    [Tooltip("Spawn edilen tomrukların maksimum hızı")]
    [SerializeField] private float spawnMaksimumHiz = 15f;

    [Tooltip("Spawn edilen tomrukların başlangıç hızı")]
    [SerializeField] private float spawnBaslangicHizi = 0f;

    [Tooltip("Spawn edilen tomrukların hızlanma oranı")]
    [SerializeField] private float spawnHizlanmaOrani = 5f;

    [Tooltip("Spawn edilen tomrukların hareket yönü")]
    [SerializeField] private Vector3 spawnHareketYonu = new Vector3(0, 0, 1);

    [Tooltip("Rastgele hız değerleri uygula")]
    [SerializeField] private bool rastgeleHizDegerleri = false;

    [Tooltip("Rastgele hız aralığı (başlangıç ve maksimum hız aynı olacak)")]
    [SerializeField] private Vector2 rastgeleHizAraligi = new Vector2(10f, 20f);

    [Header("Spawn Kontrol")]
    [Tooltip("Oyun başladığında otomatik spawn başlasın")]
    [SerializeField] private bool otomatikBaslat = true;

    [Tooltip("Maksimum tomruk sayısı (0 = sınırsız) - ÖNERİLEN: 5-10")]
    [SerializeField] private int maksimumTomrukSayisi = 5;

    [Tooltip("Şu anda sahnedeki tomruk sayısı")]
    [SerializeField] private int mevcutTomrukSayisi = 0;

    [Tooltip("Toplam spawn edilen tomruk sayısı")]
    [SerializeField] private int toplamSpawnEdilen = 0;

    [Header("Güvenlik Ayarları")]
    [Tooltip("Spawn güvenlik kontrolü aktif")]
    [SerializeField] private bool guvenlikKontrolu = true;

    [Tooltip("Maksimum spawn hızı (saniyede kaç tomruk) - ÖNERİLEN: 2")]
    [SerializeField] private float maksimumSpawnHizi = 2f;

    [Tooltip("Son spawn zamanı (debug için)")]
    [SerializeField] private float sonSpawnZamani = 0f;

    private float sonrakiSpawnZamani;
    private bool spawnAktif = false;
    private int sonSaniyedekiSpawnSayisi = 0;
    private float sonSaniyeBaslangici = 0f;

    public override void OnNetworkSpawn()
    {
        Debug.Log($"TomrukSpawner OnNetworkSpawn çağrıldı! IsServer: {IsServer}, IsClient: {IsClient}");

        // Sadece server spawn işlemlerini yönetir
        if (!IsServer)
        {
            Debug.Log("TomrukSpawner: Client olduğu için spawn işlemleri atlandı.");
            return;
        }

        Debug.Log("TomrukSpawner: Server olarak başlatılıyor...");

        // Prefab kontrolü
        if (tomrukPrefab == null)
        {
            Debug.LogError("TomrukSpawner: Tomruk prefab'ı atanmamış!");
            enabled = false;
            return;
        }

        // NetworkObject kontrolü
        if (tomrukPrefab.GetComponent<NetworkObject>() == null)
        {
            Debug.LogError("TomrukSpawner: Tomruk prefab'ında NetworkObject komponenti bulunamadı!");
            enabled = false;
            return;
        }

        Debug.Log("TomrukSpawner: Prefab kontrolleri başarılı.");

        // Çoklu spawner kontrolü
        TomrukSpawner[] spawnerlar = FindObjectsOfType<TomrukSpawner>();
        if (spawnerlar.Length > 1)
        {
            Debug.LogError($"UYARI: Sahnede {spawnerlar.Length} tane TomrukSpawner bulundu! Sadece 1 tane olmalı. Fazlalıkları silin!");
            for (int i = 0; i < spawnerlar.Length; i++)
            {
                Debug.LogError($"Spawner {i + 1}: {spawnerlar[i].gameObject.name}", spawnerlar[i]);
            }
        }

        // Sahnedeki mevcut tomrukları say (script reload sonrası düzeltme)
        SahnedekileriSay();

        // Otomatik başlatma
        if (otomatikBaslat)
        {
            Debug.Log("TomrukSpawner: Otomatik başlatma aktif, spawn başlatılıyor...");
            SpawnBaslat();
        }
        else
        {
            Debug.Log("TomrukSpawner: Otomatik başlatma pasif.");
        }
    }

    /// <summary>
    /// Sahnedeki mevcut tomrukları sayar (script reload sonrası düzeltme için)
    /// </summary>
    private void SahnedekileriSay()
    {
        GameObject[] tomruklar = GameObject.FindGameObjectsWithTag("Tomruk");
        int eskiSayi = mevcutTomrukSayisi;
        mevcutTomrukSayisi = tomruklar.Length;
        
        if (eskiSayi != mevcutTomrukSayisi)
        {
            Debug.LogWarning($"TomrukSpawner: Mevcut tomruk sayısı düzeltildi. Eski: {eskiSayi}, Yeni: {mevcutTomrukSayisi}");
        }
        
        // Listener'ları ekle (script reload sonrası kaybolanlar için)
        foreach (GameObject tomruk in tomruklar)
        {
            if (tomruk.GetComponent<TomrukYokEdilmeListener>() == null)
            {
                TomrukYokEdilmeListener listener = tomruk.AddComponent<TomrukYokEdilmeListener>();
                listener.spawner = this;
                Debug.Log($"Listener eklendi: {tomruk.name}");
            }
        }
    }

    void Update()
    {
        // Sadece server spawn kontrolü yapar
        if (!IsServer) return;

        if (spawnAktif && Time.time >= sonrakiSpawnZamani)
        {
            Debug.Log($"TomrukSpawner: Spawn zamanı geldi! Mevcut: {mevcutTomrukSayisi}, Maksimum: {maksimumTomrukSayisi}");

            // Maksimum sayı kontrolü
            if (maksimumTomrukSayisi > 0 && mevcutTomrukSayisi >= maksimumTomrukSayisi)
            {
                Debug.Log("TomrukSpawner: Maksimum sayıya ulaşıldı, spawn etmiyor.");
                return; // Maksimum sayıya ulaşıldı, spawn etme
            }

            Debug.Log("TomrukSpawner: Tomruk spawn ediliyor...");
            TomrukSpawnEt();
            SonrakiSpawnZamaniniAyarla();
        }
    }

    /// <summary>
    /// Spawn sistemini başlatır
    /// </summary>
    [Rpc(SendTo.Server)]
    public void SpawnBaslatRpc()
    {
        if (!IsServer) return;

        spawnAktif = true;
        SonrakiSpawnZamaniniAyarla();
        Debug.Log("Tomruk spawn sistemi başlatıldı!");

        // Client'lara bildir
        SpawnDurumunuBildirRpc(true);
    }

    /// <summary>
    /// Spawn sistemini başlatır (local)
    /// </summary>
    public void SpawnBaslat()
    {
        if (IsServer)
        {
            SpawnBaslatRpc();
        }
        else
        {
            SpawnBaslatRpc();
        }
    }

    /// <summary>
    /// Spawn sistemini durdurur
    /// </summary>
    [Rpc(SendTo.Server)]
    public void SpawnDurdurRpc()
    {
        if (!IsServer) return;

        spawnAktif = false;
        Debug.Log("Tomruk spawn sistemi durduruldu!");

        // Client'lara bildir
        SpawnDurumunuBildirRpc(false);
    }

    /// <summary>
    /// Spawn sistemini durdurur (local)
    /// </summary>
    public void SpawnDurdur()
    {
        if (IsServer)
        {
            SpawnDurdurRpc();
        }
        else
        {
            SpawnDurdurRpc();
        }
    }

    /// <summary>
    /// Tek bir tomruk spawn eder
    /// </summary>
    [Rpc(SendTo.Server)]
    public void TekTomrukSpawnEtRpc()
    {
        if (!IsServer) return;
        TomrukSpawnEt();
    }

    /// <summary>
    /// Tek bir tomruk spawn eder (local)
    /// </summary>
    public void TekTomrukSpawnEt()
    {
        if (IsServer)
        {
            TomrukSpawnEt();
        }
        else
        {
            TekTomrukSpawnEtRpc();
        }
    }

    /// <summary>
    /// Tüm tomrukları yok eder
    /// </summary>
    [Rpc(SendTo.Server)]
    public void TumTomruklariYokEtRpc()
    {
        if (!IsServer) return;

        // NetworkObject'leri bul ve despawn et
        NetworkObject[] tomrukNetworkObjects = FindObjectsOfType<NetworkObject>();
        foreach (NetworkObject netObj in tomrukNetworkObjects)
        {
            if (netObj.gameObject.CompareTag("Tomruk"))
            {
                netObj.Despawn(true);
            }
        }

        mevcutTomrukSayisi = 0;
        Debug.Log("Tüm tomruklar yok edildi!");

        // Client'lara bildir
        TomrukSayisiniGuncelleRpc(0);
    }

    /// <summary>
    /// Tüm tomrukları yok eder (local)
    /// </summary>
    public void TumTomruklariYokEt()
    {
        if (IsServer)
        {
            TumTomruklariYokEtRpc();
        }
        else
        {
            TumTomruklariYokEtRpc();
        }
    }

    /// <summary>
    /// Spawn istatistiklerini sıfırlar
    /// </summary>
    public void IstatistikleriSifirla()
    {
        toplamSpawnEdilen = 0;
        mevcutTomrukSayisi = 0;
        Debug.Log("Spawn istatistikleri sıfırlandı!");
    }

    private void TomrukSpawnEt()
    {
        Debug.Log("TomrukSpawnEt() çağrıldı!");

        // Güvenlik kontrolleri
        if (guvenlikKontrolu)
        {
            Debug.Log("Güvenlik kontrolleri yapılıyor...");
            // Çok hızlı spawn kontrolü
            float simdikiZaman = Time.time;

            // Yeni saniye başladı mı?
            if (simdikiZaman - sonSaniyeBaslangici >= 1f)
            {
                sonSaniyeBaslangici = simdikiZaman;
                sonSaniyedekiSpawnSayisi = 0;
            }

            // Bu saniyede çok fazla spawn oldu mu?
            if (sonSaniyedekiSpawnSayisi >= maksimumSpawnHizi)
            {
                Debug.LogWarning($"Spawn hızı sınırı aşıldı! Bu saniyede {sonSaniyedekiSpawnSayisi} tomruk spawn oldu. Maksimum: {maksimumSpawnHizi}");
                return;
            }

            // Son spawn'dan çok az zaman geçti mi?
            if (simdikiZaman - sonSpawnZamani < 0.1f)
            {
                Debug.LogWarning("Çok hızlı spawn girişimi! En az 0.1 saniye bekle.");
                return;
            }
        }

        // Maksimum sayı kontrolü (ekstra güvenlik)
        if (maksimumTomrukSayisi > 0 && mevcutTomrukSayisi >= maksimumTomrukSayisi)
        {
            Debug.LogWarning($"Maksimum tomruk sayısına ulaşıldı: {maksimumTomrukSayisi}");
            return;
        }

        Debug.Log("Güvenlik kontrolleri geçildi, tomruk oluşturuluyor...");

        Vector3 spawnPozisyonu = transform.position;

        // Rastgele pozisyon hesapla
        if (rastgelePozisyon)
        {
            float randomX = Random.Range(xAraligi.x, xAraligi.y);
            float randomY = Random.Range(yAraligi.x, yAraligi.y);
            float randomZ = Random.Range(zAraligi.x, zAraligi.y);

            spawnPozisyonu += new Vector3(randomX, randomY, randomZ);
        }

        // Network tomruk oluştur
        Debug.Log($"Tomruk prefab instantiate ediliyor. Pozisyon: {spawnPozisyonu}");
        GameObject yeniTomruk = Instantiate(tomrukPrefab, spawnPozisyonu, transform.rotation);
        NetworkObject networkObject = yeniTomruk.GetComponent<NetworkObject>();

        if (networkObject != null)
        {
            Debug.Log("NetworkObject bulundu, spawn ediliyor...");
            // NetworkObject'i spawn et
            try
            {
                networkObject.Spawn();
                Debug.Log("NetworkObject başarıyla spawn edildi!");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"NetworkObject spawn hatası: {e.Message}");
                Destroy(yeniTomruk);
                return;
            }

            // TomrukController'ı al
            TomrukController tomrukController = yeniTomruk.GetComponent<TomrukController>();
            if (tomrukController != null)
            {
                // Spawner tarafından ayarlandığını bildir
                tomrukController.SpawnerTarafindanAyarlandi();

                // Hız ayarlarını uygula
                if (hizAyarlariniUygula)
                {
                    HizAyarlariniUygula(tomrukController);
                }
            }
            else
            {
                Debug.LogWarning("Spawn edilen tomrukta TomrukController bulunamadı!");
            }

            // Spawn zamanını kaydet
            sonSpawnZamani = Time.time;
            sonSaniyedekiSpawnSayisi++;

            // Tomruk sayısını artır
            mevcutTomrukSayisi++;
            toplamSpawnEdilen++;

            // Tomruk yok edildiğinde sayıyı azaltmak için listener ekle
            TomrukYokEdilmeListener listener = yeniTomruk.AddComponent<TomrukYokEdilmeListener>();
            listener.spawner = this;

            Debug.Log($"Tomruk spawn edildi! Mevcut: {mevcutTomrukSayisi}, Toplam: {toplamSpawnEdilen}");

            // Client'lara tomruk sayısını bildir
            TomrukSayisiniGuncelleRpc(mevcutTomrukSayisi);
        }
        else
        {
            Debug.LogError("Spawn edilen tomrukta NetworkObject bulunamadı!");
            Destroy(yeniTomruk);
        }
    }

    /// <summary>
    /// Spawn edilen tomruğa hız ayarlarını uygular
    /// </summary>
    private void HizAyarlariniUygula(TomrukController tomrukController)
    {
        // Reflection kullanarak private field'lara erişim
        var tomrukType = typeof(TomrukController);
        
        // Hız değerlerini hesapla (rastgele veya sabit)
        float uygulanacakHiz = spawnMaksimumHiz;
        
        if (rastgeleHizDegerleri)
        {
            // Rastgele hız seç - başlangıç ve maksimum aynı olacak
            uygulanacakHiz = Random.Range(rastgeleHizAraligi.x, rastgeleHizAraligi.y);
        }
        else
        {
            // Sabit hız kullan - başlangıç ve maksimum aynı olacak
            uygulanacakHiz = spawnMaksimumHiz;
        }
        
        // Başlangıç ve maksimum hız aynı olsun
        float uygulanacakMaksHiz = uygulanacakHiz;
        float uygulanacakBaslangicHiz = uygulanacakHiz;

        // SerializeField'ları güncelle
        try
        {
            var yuvarlanmaKuvvetiField = tomrukType.GetField("yuvarlanmaKuvveti", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var hareketYonuField = tomrukType.GetField("hareketYonu", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var maksimumHizField = tomrukType.GetField("maksimumHiz", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var baslangicHiziField = tomrukType.GetField("baslangicHizi", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var hizlanmaOraniField = tomrukType.GetField("hizlanmaOrani", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Değerleri ata
            yuvarlanmaKuvvetiField?.SetValue(tomrukController, spawnYuvarlanmaKuvveti);
            hareketYonuField?.SetValue(tomrukController, spawnHareketYonu);
            maksimumHizField?.SetValue(tomrukController, uygulanacakMaksHiz);
            baslangicHiziField?.SetValue(tomrukController, uygulanacakBaslangicHiz);
            hizlanmaOraniField?.SetValue(tomrukController, spawnHizlanmaOrani);

            Debug.Log($"Tomruk hız ayarları uygulandı - Maks: {uygulanacakMaksHiz:F1}, Başlangıç: {uygulanacakBaslangicHiz:F1}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Hız ayarları uygulanırken hata: {e.Message}");
        }
    }

    private void SonrakiSpawnZamaniniAyarla()
    {
        float aralik = spawnAraligi;

        if (rastgeleAralik)
        {
            aralik = Random.Range(minSpawnAraligi, maxSpawnAraligi);
        }

        sonrakiSpawnZamani = Time.time + aralik;
    }

    /// <summary>
    /// Tomruk yok edildiğinde çağrılır
    /// </summary>
    public void TomrukYokEdildi()
    {
        if (IsServer)
        {
            mevcutTomrukSayisi--;
            if (mevcutTomrukSayisi < 0) mevcutTomrukSayisi = 0;

            // Client'lara güncel sayıyı bildir
            TomrukSayisiniGuncelleRpc(mevcutTomrukSayisi);
        }
    }

    /// <summary>
    /// Client'lara spawn durumunu bildirir
    /// </summary>
    [Rpc(SendTo.ClientsAndHost)]
    private void SpawnDurumunuBildirRpc(bool aktif)
    {
        if (!IsServer)
        {
            spawnAktif = aktif;
            Debug.Log($"Spawn durumu güncellendi: {(aktif ? "Aktif" : "Pasif")}");
        }
    }

    /// <summary>
    /// Client'lara tomruk sayısını bildirir
    /// </summary>
    [Rpc(SendTo.ClientsAndHost)]
    private void TomrukSayisiniGuncelleRpc(int yeniSayi)
    {
        if (!IsServer)
        {
            mevcutTomrukSayisi = yeniSayi;
        }
    }

    /// <summary>
    /// Inspector'da değerler değiştirildiğinde çalışır
    /// </summary>
    private void OnValidate()
    {
        // Spawn aralığı kontrolleri
        if (spawnAraligi < 0.5f) spawnAraligi = 0.5f;
        
        // Rastgele aralık kontrolleri - ÇOK ÖNEMLİ!
        if (minSpawnAraligi < 1f) minSpawnAraligi = 1f;
        if (maxSpawnAraligi < 2f) maxSpawnAraligi = 2f;
        if (maxSpawnAraligi < minSpawnAraligi) maxSpawnAraligi = minSpawnAraligi + 1f;
        
        // Güvenlik ayarları
        if (maksimumTomrukSayisi < 0) maksimumTomrukSayisi = 0;
        if (maksimumTomrukSayisi > 20) maksimumTomrukSayisi = 20; // Çok fazla tomruk engelle
        if (maksimumSpawnHizi > 5f) maksimumSpawnHizi = 5f; // Çok hızlı spawn engelle
        if (maksimumSpawnHizi < 0.5f) maksimumSpawnHizi = 0.5f;
        
        // Rastgele aralık açıkken uyarı
        if (rastgeleAralik && minSpawnAraligi < 1.5f)
        {
            Debug.LogWarning("TomrukSpawner: Rastgele aralık açıkken minimum spawn aralığı en az 1.5 saniye olmalı!");
            minSpawnAraligi = 1.5f;
        }
    }

    /// <summary>
    /// Gizmos çizimi (Scene view'da spawn alanını gösterir)
    /// </summary>
    private void OnDrawGizmosSelected()
    {
        if (rastgelePozisyon)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireCube(transform.position, new Vector3(
                xAraligi.y - xAraligi.x,
                yAraligi.y - yAraligi.x,
                zAraligi.y - zAraligi.x
            ));
        }
        else
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, 0.5f);
        }
    }
}

/// <summary>
/// Tomruk yok edildiğinde spawner'a haber veren yardımcı component
/// </summary>
public class TomrukYokEdilmeListener : NetworkBehaviour
{
    [HideInInspector] public TomrukSpawner spawner;

    private void OnDestroy()
    {
        if (spawner != null)
        {
            spawner.TomrukYokEdildi();
        }
    }
}