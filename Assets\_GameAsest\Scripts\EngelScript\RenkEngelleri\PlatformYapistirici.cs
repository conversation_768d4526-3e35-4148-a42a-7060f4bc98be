using UnityEngine;
using System.Collections.Generic;

public class PlatformYapistirici : MonoBehaviour
{
    [Header("Platform Yapıştırma Ayarları")]
    [Tooltip("Platformları otomatik olarak yapıştır")]
    public bool otomatikYapistir = true;
    
    [Tooltip("Platform arası mesafe (0 = tam yapışık)")]
    public float platformAraMesafe = 0f;
    
    [Tooltip("Platformları hangi eksende sırala")]
    public YapistiricEksen siralamaEkseni = YapistiricEksen.X;
    
    [Tooltip("Kaç platform yan yana olsun (Grid için)")]
    public int yanYanaPlatformSayisi = 3;
    
    [Header("Grid Ayarları")]
    [Tooltip("Grid şeklinde mi yoksa tek sıra mı?")]
    public bool gridSeklinde = true;
    
    [Tooltip("Grid başlangıç pozisyonu")]
    public Vector3 gridBaslangicPozisyon = Vector3.zero;

    public enum YapistiricEksen { X, Z, XZ_Grid }

    private List<RenkPlatformu> platformlar = new List<RenkPlatformu>();

    void Start()
    {
        if (otomatikYapistir)
        {
            PlatformlariYapistir();
        }
    }

    [ContextMenu("Platformları Yapıştır")]
    public void PlatformlariYapistir()
    {
        // Tüm platformları bul
        BulPlatformlari();
        
        // Pozisyonları ayarla
        PozisyonlariAyarla();
        
        Debug.Log($"✅ {platformlar.Count} platform yapıştırıldı!");
    }

    void BulPlatformlari()
    {
        platformlar.Clear();
        
        // Sahne içindeki tüm RenkPlatformu'larını bul
        RenkPlatformu[] bulunanPlatformlar = FindObjectsOfType<RenkPlatformu>();
        
        foreach (RenkPlatformu platform in bulunanPlatformlar)
        {
            platformlar.Add(platform);
        }
        
        Debug.Log($"🔍 {platformlar.Count} platform bulundu");
    }

    void PozisyonlariAyarla()
    {
        if (platformlar.Count == 0) return;

        // Platform boyutunu hesapla (ilk platformdan)
        Bounds platformBounds = GetPlatformBounds(platformlar[0]);
        Vector3 platformBoyutu = platformBounds.size;

        switch (siralamaEkseni)
        {
            case YapistiricEksen.X:
                SiraliYapistir(platformBoyutu, Vector3.right);
                break;
                
            case YapistiricEksen.Z:
                SiraliYapistir(platformBoyutu, Vector3.forward);
                break;
                
            case YapistiricEksen.XZ_Grid:
                GridYapistir(platformBoyutu);
                break;
        }
    }

    void SiraliYapistir(Vector3 platformBoyutu, Vector3 yon)
    {
        Vector3 baslangicPozisyon = gridBaslangicPozisyon;
        float mesafe = (yon == Vector3.right ? platformBoyutu.x : platformBoyutu.z) + platformAraMesafe;

        for (int i = 0; i < platformlar.Count; i++)
        {
            Vector3 yeniPozisyon = baslangicPozisyon + (yon * mesafe * i);
            platformlar[i].transform.position = yeniPozisyon;
            
            Debug.Log($"📍 Platform {i}: {yeniPozisyon}");
        }
    }

    void GridYapistir(Vector3 platformBoyutu)
    {
        Vector3 baslangicPozisyon = gridBaslangicPozisyon;
        float xMesafe = platformBoyutu.x + platformAraMesafe;
        float zMesafe = platformBoyutu.z + platformAraMesafe;

        for (int i = 0; i < platformlar.Count; i++)
        {
            int satir = i / yanYanaPlatformSayisi;
            int sutun = i % yanYanaPlatformSayisi;

            Vector3 yeniPozisyon = baslangicPozisyon + new Vector3(
                sutun * xMesafe,
                0,
                satir * zMesafe
            );

            platformlar[i].transform.position = yeniPozisyon;
            
            Debug.Log($"📍 Platform {i} (Satır:{satir}, Sütun:{sutun}): {yeniPozisyon}");
        }
    }

    Bounds GetPlatformBounds(RenkPlatformu platform)
    {
        Renderer renderer = platform.GetComponent<Renderer>();
        if (renderer != null)
        {
            return renderer.bounds;
        }

        // Eğer renderer yoksa varsayılan boyut
        return new Bounds(platform.transform.position, Vector3.one);
    }

    void OnDrawGizmosSelected()
    {
        if (platformlar == null || platformlar.Count == 0) return;

        // Platform pozisyonlarını gizmo olarak göster
        Gizmos.color = Color.yellow;
        
        foreach (RenkPlatformu platform in platformlar)
        {
            if (platform != null)
            {
                Gizmos.DrawWireCube(platform.transform.position, Vector3.one);
            }
        }

        // Grid başlangıç noktasını göster
        Gizmos.color = Color.green;
        Gizmos.DrawSphere(gridBaslangicPozisyon, 0.5f);
    }

    [ContextMenu("Platformları Rastgele Dağıt")]
    public void PlatformlariRastgeleDagit()
    {
        BulPlatformlari();
        
        foreach (RenkPlatformu platform in platformlar)
        {
            Vector3 rastgelePozisyon = new Vector3(
                Random.Range(-10f, 10f),
                platform.transform.position.y,
                Random.Range(-10f, 10f)
            );
            
            platform.transform.position = rastgelePozisyon;
        }
        
        Debug.Log("🎲 Platformlar rastgele dağıtıldı!");
    }
}