using UnityEngine;
using Unity.Netcode;
using Unity.Netcode.Components;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Bu sınıf, bir karakterin ragdoll fiziğini ağ üzerinde senkronize bir şekilde yönetir.
/// --- DÜZELTME NOTLARI (v2) ---
/// Karakterin ragdoll'dan ç<PERSON> "havada uçma" sorunu, GetUpTransitionRoutine fonksiyonu
/// tamamen yenilenerek çözülmüştür. Yeni mantık, ayağa kalkmadan önce ragdoll'un yere inip
/// hareketinin durmasını bekler ve ardından karakteri Raycast ile güvenli bir şekilde zemine
/// yerleştirir. Bu, hem havada ayağa kalkmayı hem de yerden fırlamayı engeller.
/// </summary>
public class RagdollManager : NetworkBehaviour
{
    public event System.Action OnRagdollAktifOldu;
    public event System.Action OnRagdollKapandi;

    [Header("<PERSON><PERSON><PERSON><PERSON><PERSON>")]
    public Animator characterAnimator;
    public Rigidbody hipsBone;
    public Transform cameraTransform;

    [Header("Ağ Senkronizasyon Ayarları")]
    [Tooltip("Saniyede kaç kez ragdoll durumu sunucuya gönderilecek. 10-20 arası iyi bir başlangıçtır.")]
    public float syncRate = 15f;
    [Tooltip("Diğer client'larda ragdoll'un yumuşatma hızı. Yüksek değerler hedefe daha hızlı ulaşır.")]
    public float interpolationSpeed = 20f;

    [Header("Genel Ragdoll Ayarları")]
    public float maxTotalRagdollTime = 8f;

    private NetworkVariable<bool> isRagdollActiveNetworked = new NetworkVariable<bool>(
        false,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server
    );

    public bool IsRagdollActive => isRagdollActiveNetworked.Value;

    private Rigidbody[] ragdollRigidbodies;
    private CharacterController characterController;
    private Coroutine getUpCoroutine;

    private Transform[] ragdollTransforms;
    private float syncTimer;
    private NetworkTransform networkTransform;

    private Vector3[] targetPositions;
    private Quaternion[] targetRotations;
    private bool firstSyncReceived = false;


    void Awake()
    {
        characterController = GetComponent<CharacterController>();
        networkTransform = GetComponent<NetworkTransform>();
        ragdollRigidbodies = GetComponentsInChildren<Rigidbody>();
        
        ragdollTransforms = new Transform[ragdollRigidbodies.Length];
        targetPositions = new Vector3[ragdollRigidbodies.Length];
        targetRotations = new Quaternion[ragdollRigidbodies.Length];
        for (int i = 0; i < ragdollRigidbodies.Length; i++)
        {
            ragdollTransforms[i] = ragdollRigidbodies[i].transform;
        }

        if (hipsBone == null && GetComponentInChildren<Animator>() != null) {
            hipsBone = GetComponentInChildren<Animator>().GetBoneTransform(HumanBodyBones.Hips).GetComponent<Rigidbody>();
        }
        
        SetRagdollState(false, true);
    }

    public override void OnNetworkSpawn()
    {
        isRagdollActiveNetworked.OnValueChanged += OnRagdollStateChanged;
        SetRagdollState(isRagdollActiveNetworked.Value, true);
    }

    public override void OnNetworkDespawn()
    {
        isRagdollActiveNetworked.OnValueChanged -= OnRagdollStateChanged;
    }

    void FixedUpdate()
    {
        if (!IsOwner && IsRagdollActive && firstSyncReceived)
        {
            InterpolateRagdollTransforms();
        }
        // Ragdoll aktifken ana karakter transformunu hipsBone.position'a zorla eşitleme! Bu satır kaldırıldı.
    }

    void Update()
    {
        if (IsOwner && IsRagdollActive)
        {
            syncTimer += Time.deltaTime;
            if (syncTimer >= 1f / syncRate)
            {
                syncTimer = 0f;
                
                Vector3[] positions = new Vector3[ragdollRigidbodies.Length];
                Quaternion[] rotations = new Quaternion[ragdollRigidbodies.Length];
                for (int i = 0; i < ragdollRigidbodies.Length; i++)
                {
                    if (ragdollRigidbodies[i] == null) continue;
                    positions[i] = ragdollRigidbodies[i].position;
                    rotations[i] = ragdollRigidbodies[i].rotation;
                }
                SyncRagdollStateServerRpc(positions, rotations);
            }
        }
    }
    
    private void InterpolateRagdollTransforms()
    {
        float smoothFactor = 1 - Mathf.Exp(-interpolationSpeed * Time.fixedDeltaTime);

        for (int i = 0; i < ragdollRigidbodies.Length; i++)
        {
            if (ragdollRigidbodies[i] == null) continue;
            Vector3 newPos = Vector3.Lerp(ragdollRigidbodies[i].position, targetPositions[i], smoothFactor);
            Quaternion newRot = Quaternion.Slerp(ragdollRigidbodies[i].rotation, targetRotations[i], smoothFactor);
            ragdollRigidbodies[i].MovePosition(newPos);
            ragdollRigidbodies[i].MoveRotation(newRot);
        }
    }

    [ServerRpc]
    private void SyncRagdollStateServerRpc(Vector3[] positions, Quaternion[] rotations)
    {
        SyncRagdollStateClientRpc(positions, rotations);
    }

    [ClientRpc]
    private void SyncRagdollStateClientRpc(Vector3[] positions, Quaternion[] rotations)
    {
        if (IsOwner) return;

        this.targetPositions = positions;
        this.targetRotations = rotations;

        foreach (var rb in ragdollRigidbodies)
        {
            if (rb == null) continue;
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }

        if (!firstSyncReceived)
        {
            for (int i = 0; i < ragdollTransforms.Length; i++)
            {
                if (ragdollTransforms[i] == null) continue;
                ragdollTransforms[i].position = positions[i];
                ragdollTransforms[i].rotation = rotations[i];
            }
            firstSyncReceived = true;
        }
    }

    private void OnRagdollStateChanged(bool previousValue, bool newValue)
    {
        SetRagdollState(newValue);
    }

    private void SetRagdollState(bool isActive, bool forceInstant = false)
    {
        if (isActive)
        {
            if (networkTransform != null) networkTransform.enabled = false;
            if (characterController != null) characterController.enabled = false;
            if (characterAnimator != null) characterAnimator.enabled = false;

            foreach (var rb in ragdollRigidbodies)
            {
                if (rb == null) continue;
                rb.isKinematic = false;
                rb.useGravity = IsOwner;
            }
            OnRagdollAktifOldu?.Invoke();
        }
        else
        {
            firstSyncReceived = false;
            if (IsOwner && characterAnimator != null && characterAnimator.enabled) return;
            
            foreach (var rb in ragdollRigidbodies) 
            { 
                if (rb == null) continue;
                rb.isKinematic = true; 
            }

            if (IsOwner && !forceInstant)
            {
                StartCoroutine(GetUpTransitionRoutine());
            }
            else 
            {
                if(characterController != null) characterController.enabled = true;
                if(characterAnimator != null) characterAnimator.enabled = true;
                if (networkTransform != null) networkTransform.enabled = true;
                OnRagdollKapandi?.Invoke();
            }
        }
    }
    
    public void PatlamaEtkisiyleRagdollAktifEt(Vector3 patlamaMerkezi, float patlamaGucu, float ragdollSuresi)
    { RequestRagdollActivation(ragdollSuresi, patlamaMerkezi: patlamaMerkezi, patlamaGucu: patlamaGucu); }
    
    public void DarbelereKarsiRagdollAktifEt(Vector3 kuvvet, Vector3 tork, float ragdollSuresi)
    { RequestRagdollActivation(ragdollSuresi, kuvvet: kuvvet, tork: tork); }

    public void SadeceRagdolluAktifEt(float ragdollSuresi)
    { RequestRagdollActivation(ragdollSuresi); }

    /// <summary>
    /// Client'ta direkt ragdoll aktif eder ve force uygular (network döngüsü olmadan)
    /// </summary>
    public void ClienttaRagdollAktifEt(Vector3 kuvvet, Vector3 tork, float ragdollSuresi)
    {
        if (isRagdollActiveNetworked.Value) return;

        // Server'a ragdoll aktivasyonu isteği gönder
        RequestClientRagdollActivationServerRpc(kuvvet, tork, ragdollSuresi);
    }

    /// <summary>
    /// Client'ta sadece force uygular (ragdoll zaten aktifse)
    /// </summary>
    public void ClienttaSadeceForceUygula(Vector3 kuvvet, Vector3 tork)
    {
        if (!IsRagdollActive) return;

        // Force'u direkt uygula
        if (kuvvet.sqrMagnitude > 0)
        {
            foreach (var rb in ragdollRigidbodies) {
                if (rb == null) continue;
                rb.linearVelocity = Vector3.zero;
            }
            hipsBone.AddForce(kuvvet, ForceMode.Impulse);
            hipsBone.AddTorque(tork, ForceMode.Impulse);
        }
    }

    private void RequestRagdollActivation(float ragdollSuresi, Vector3? patlamaMerkezi = null, float patlamaGucu = 0f, Vector3? kuvvet = null, Vector3? tork = null)
    {
        if (!IsOwner || characterController == null) return;
        Vector3 currentVelocity = characterController.velocity;
        RequestRagdollServerRpc(ragdollSuresi, patlamaMerkezi ?? Vector3.zero, patlamaGucu, kuvvet ?? Vector3.zero, tork ?? Vector3.zero, currentVelocity);
    }

    [ServerRpc]
    private void RequestRagdollServerRpc(float ragdollSuresi, Vector3 patlamaMerkezi, float patlamaGucu, Vector3 kuvvet, Vector3 tork, Vector3 initialVelocity)
    {
        if (isRagdollActiveNetworked.Value) return;
        isRagdollActiveNetworked.Value = true;
        
        if (patlamaGucu > 0) ApplyExplosionForceClientRpc(patlamaMerkezi, patlamaGucu, initialVelocity);
        else if (kuvvet.sqrMagnitude > 0) ApplyImpulseForceClientRpc(kuvvet, tork, initialVelocity);
        
        if (getUpCoroutine != null) StopCoroutine(getUpCoroutine);
        getUpCoroutine = StartCoroutine(GetUpRoutineServer(ragdollSuresi));
    }

    [ClientRpc]
    private void ApplyExplosionForceClientRpc(Vector3 patlamaMerkezi, float patlamaGucu, Vector3 initialVelocity)
    {
        if (!IsOwner) return;
        foreach (var rb in ragdollRigidbodies) {
            if (rb == null) continue;
            rb.linearVelocity = initialVelocity;
            rb.AddExplosionForce(patlamaGucu, patlamaMerkezi, patlamaGucu, 1.0f, ForceMode.Impulse);
        }
    }

    [ClientRpc]
    private void ApplyImpulseForceClientRpc(Vector3 kuvvet, Vector3 tork, Vector3 initialVelocity)
    {
        if (!IsOwner) return;
        foreach (var rb in ragdollRigidbodies) {
            if (rb == null) continue;
            rb.linearVelocity = initialVelocity;
        }
        hipsBone.AddForce(kuvvet, ForceMode.Impulse);
        hipsBone.AddTorque(tork, ForceMode.Impulse);
    }

    private IEnumerator GetUpRoutineServer(float minRagdollTime)
    {
        yield return new WaitForSeconds(minRagdollTime);
        float totalTime = minRagdollTime;
        while (totalTime < maxTotalRagdollTime) {
            yield return null;
            totalTime += Time.deltaTime;
        }
        isRagdollActiveNetworked.Value = false;
    }

    [ServerRpc]
    private void RequestRagdollDeactivationServerRpc()
    {
        isRagdollActiveNetworked.Value = false;
    }

    [ServerRpc]
    private void RequestClientRagdollActivationServerRpc(Vector3 kuvvet, Vector3 tork, float ragdollSuresi)
    {
        if (isRagdollActiveNetworked.Value) return;
        
        isRagdollActiveNetworked.Value = true;
        
        // Force'u client'lara uygula
        if (kuvvet.sqrMagnitude > 0)
        {
            ApplyClientRagdollForceClientRpc(kuvvet, tork);
        }
        
        // Ragdoll süresini başlat
        if (getUpCoroutine != null) StopCoroutine(getUpCoroutine);
        getUpCoroutine = StartCoroutine(GetUpRoutineServer(ragdollSuresi));
    }

    [ClientRpc]
    private void ApplyClientRagdollForceClientRpc(Vector3 kuvvet, Vector3 tork)
    {
        // Ragdoll aktif olana kadar bekle, sonra force uygula
        StartCoroutine(ApplyForceWhenRagdollActive(kuvvet, tork));
    }

    private IEnumerator ApplyForceWhenRagdollActive(Vector3 kuvvet, Vector3 tork)
    {
        // Ragdoll aktif olana kadar bekle (max 1 saniye)
        float waitTime = 0f;
        while (!IsRagdollActive && waitTime < 1f)
        {
            waitTime += Time.deltaTime;
            yield return null;
        }

        // Ragdoll aktif olduysa force uygula
        if (IsRagdollActive)
        {
            // Bir frame daha bekle ki rigidbody'ler tam hazır olsun
            yield return new WaitForFixedUpdate();
            
            foreach (var rb in ragdollRigidbodies) {
                if (rb == null) continue;
                rb.linearVelocity = Vector3.zero;
            }
            hipsBone.AddForce(kuvvet, ForceMode.Impulse);
            hipsBone.AddTorque(tork, ForceMode.Impulse);
        }
    }

    /// <summary>
    /// Client'ta ragdoll süresini yöneten coroutine
    /// </summary>
    private IEnumerator GetUpRoutineClient(float ragdollSuresi)
    {
        yield return new WaitForSeconds(ragdollSuresi);
        // Client'ta direkt NetworkVariable değiştirilemez, server'a istek gönder
        RequestRagdollDeactivationServerRpc();
    }

    /// <summary>
    /// --- TAMAMEN YENİLENMİŞ FONKSİYON (v2) ---
    /// Sahip olan client'ta, ragdoll'dan çıkıp tekrar animasyonlu hale geçişi yönetir.
    /// "Havada uçma" sorununu çözmek için bu versiyon, ayağa kalkmadan önce ragdoll'un yere inip durmasını bekler
    /// ve ardından karakteri güvenli bir şekilde zemine yerleştirir.
    /// </summary>
    private IEnumerator GetUpTransitionRoutine()
    {
        // 1. ADIM: Ragdoll'un Yere İnmesini ve Durmasını Bekle
        // Bu, karakterin hala yüksekten düşerken veya hızla yuvarlanırken ayağa kalkmaya çalışmasını önler.
        // Bu kontrol, "havada uçma" sorununun ana nedenini ortadan kaldırır.
        float settleTimer = 0f;
        const float maxSettleTime = 3f; // Sonsuz döngüye girmeyi önlemek için zaman aşımı.
        const float settleSpeedThreshold = 0.5f; // Durmuş kabul edilecek hız eşiği.
        while (settleTimer < maxSettleTime)
        {
            if (hipsBone.linearVelocity.magnitude < settleSpeedThreshold)
            {
                // Hız eşiğin altına düştüğünde, birkaç frame daha bekleyerek tamamen durduğundan emin ol.
                // Bu, zıplama gibi küçük hareketlerin bitmesini sağlar.
                yield return new WaitForSeconds(0.2f);
                if (hipsBone.linearVelocity.magnitude < settleSpeedThreshold)
                {
                    break; // Ragdoll durdu, döngüden çık.
                }
            }
            settleTimer += Time.deltaTime;
            yield return null;
        }

        // 2. ADIM: Kalan Hızı Sıfırla
        // Bekleme sonrası kalabilecek küçük momentumları temizle.
        foreach (var rb in ragdollRigidbodies)
        {
            if (rb == null) continue;
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }

        // 3. ADIM: Ragdoll Fiziğini Kapat
        // Tüm rigidbody'leri tekrar kinematic yaparak fizik etkileşimlerini durdur.
        // Bu, ana transformu hareket ettirmeden önce yapılmalıdır.
        foreach (var rb in ragdollRigidbodies)
        {
            if (rb == null) continue;
            rb.isKinematic = true;
        }

        // 4. ADIM: Hedef Pozisyonu ve Rotasyonu GÜVENLİ Bir Şekilde Hesapla
        // ÖNCEKİ SORUN: Karakteri doğrudan kalça kemiği pozisyonuna koymak, eğer pivotu ortadaysa,
        // CharacterController'ın yarısının yerin içine girmesine ve fırlatılmasına neden oluyordu.
        // YENİ ÇÖZÜM: Yeri tespit etmek için güvenli bir Raycast kullanıp, CharacterController'ı tam zemin üzerine yerleştiriyoruz.
        Vector3 targetPosition = hipsBone.position; // Varsayılan pozisyon.
        RaycastHit hit;
        // Kalça kemiğinin biraz üzerinden aşağı doğru bir ışın göndererek zemini bul.
        if (characterController != null && Physics.Raycast(hipsBone.position + Vector3.up * 0.1f, Vector3.down, out hit, 2.0f))
        {
            // Karakterin pivot noktasını, zeminin tam üzerine, boyunun yarısı kadar yukarıya yerleştir.
            // skinWidth, karakterin yüzeylere ne kadar girebileceğini belirler, bunu da hesaba katmak önemlidir.
            float safeOffset = (characterController.height / 2f) + characterController.skinWidth + 0.01f;
            targetPosition = hit.point + Vector3.up * safeOffset;
        }
        
        // Karakterin ileri yönünü, ragdoll'un gövdesinin ileri yönünden al.
        Vector3 targetForward = hipsBone.transform.forward;
        targetForward.y = 0; // Y eksenindeki eğimi kaldırarak karakterin dik durmasını sağla.
        Quaternion targetRotation = transform.rotation;
        if (targetForward.sqrMagnitude > 0.01f)
        {
            targetRotation = Quaternion.LookRotation(targetForward.normalized);
        }

        // 5. ADIM: Ana Karakteri Yeni Pozisyona ve Rotasyona Anında Taşı
        transform.position = targetPosition;
        transform.rotation = targetRotation;

        // --- Ragdoll kemiklerini de yeni pozisyona çek ---
        for (int i = 0; i < ragdollTransforms.Length; i++)
        {
            if (ragdollTransforms[i] == null) continue;
            ragdollTransforms[i].position = targetPosition;
            ragdollTransforms[i].rotation = targetRotation;
        }

        // Pozisyon ve rotasyonu tüm client'lara senkronize et
        // (Artık NetworkTransform ile otomatik senkronize ediliyor)

        // Fizik motorunun bu yeni pozisyonu işlemesi için bir sonraki fizik karesini bekle.
        yield return new WaitForFixedUpdate();

        // 6. ADIM: Kontrolcüleri Tekrar Devreye Al
        if (characterController != null) characterController.enabled = true;
        if (characterAnimator != null) characterAnimator.enabled = true;

        // NetworkTransform'u pozisyon güncellendikten sonra aktifleştir ve Teleport ile sıfırla
        if (networkTransform != null)
        {
            networkTransform.enabled = true;
            networkTransform.Teleport(transform.position, transform.rotation, transform.localScale);
        }

        // --- RAGDOLL BİTTİKTEN SONRA OYUNCU KONTROLÜ GERİ AÇ ---
        var oyuncuKontrol = GetComponent<OyuncuKontrol>();
        if (oyuncuKontrol != null)
        {
            oyuncuKontrol.KontrolleriGeriAc();
        }

        OnRagdollKapandi?.Invoke();
    }

    // --- SyncTransform RPC'leri ---
    [ServerRpc]
    private void SyncTransformServerRpc(Vector3 pos, Quaternion rot)
    {
        // Sunucu pozisyonu uygulasın ve sonra client'lara göndersin
        transform.position = pos;
        transform.rotation = rot;
        SyncTransformClientRpc(pos, rot);
    }

    [ClientRpc]
    private void SyncTransformClientRpc(Vector3 pos, Quaternion rot)
    {
        if (IsOwner) return;
        transform.position = pos;
        transform.rotation = rot;
        if (networkTransform != null)
        {
            networkTransform.Teleport(pos, rot, transform.localScale);
        }
    }
}
