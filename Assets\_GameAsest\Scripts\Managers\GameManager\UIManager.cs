using UnityEngine;
using TMPro; // TextMeshPro kullanmak için bu satır şart.
using UnityEngine.UI; // Panel gibi UI elemanları için bu satır gerekli.

public class UIManager : MonoBehaviour
{
    [Header("UI Referansları")]
    public TextMeshProUGUI zamanMetni;
    public TextMeshProUGUI oyuncuSayisiMetni;
    public GameObject sonucEkraniPanel;
    public TextMeshProUGUI sonucMetni;
    public TextMeshProUGUI coinMetni; // Inspector'dan atanacak

    private bool sureSabit = false;

    void Start()
    {
        // Oyun başladığında sonuç ekranının kapalı olduğundan emin ol.
        if(sonucEkraniPanel != null)
        {
            sonucEkraniPanel.SetActive(false);
        }
    }

    public void SabitSifirSureGoster()
    {
        sureSabit = true;
        if (zamanMetni != null)
            zamanMetni.text = "Süre: 00:00";
    }

    public void ZamanGuncelle(float turSuresi, float gecenSure)
    {
        if (sureSabit) return; // Sabitlenmişse güncelleme!
        if (zamanMetni == null) return;
        
        float kalanSure = turSuresi - gecenSure;
        // Kalan sürenin 0'dan aşağı düşmesini engelle
        kalanSure = Mathf.Max(0, kalanSure);

        // Dakika ve saniye formatında yazdır (örn: 03:45)
        int dakikalar = Mathf.FloorToInt(kalanSure / 60);
        int saniyeler = Mathf.FloorToInt(kalanSure % 60);
        zamanMetni.text = string.Format("Süre: {0:00}:{1:00}", dakikalar, saniyeler);
    }

    public void OyuncuSayisiGuncelle(int bitirenOyuncu, int gerekenOyuncu)
    {
        if (oyuncuSayisiMetni == null) return;
        
        oyuncuSayisiMetni.text = string.Format("Turu Geçen: {0} / {1}", bitirenOyuncu, gerekenOyuncu);
    }

    public void SonucEkraniniGoster(bool kazandiMi)
    {
        if (sonucEkraniPanel == null || sonucMetni == null) return;
        
        sonucEkraniPanel.SetActive(true);
        if (kazandiMi)
        {
            sonucMetni.text = "TURU GEÇTİN!";
            sonucMetni.color = Color.green;
        }
        else
        {
            sonucMetni.text = "ELENDİN!";
            sonucMetni.color = Color.red;
        }
    }

    // Coin/puan güncelleme fonksiyonu
    public void CoinGuncelle(int coin)
    {
        if (coinMetni != null)
            coinMetni.text = $"Coin: {coin}";
    }

    // Dinamik oyuncu sayısı güncelleme fonksiyonu
    public void UpdatePlayerCountDisplay(int toplamOyuncuSayisi)
    {
        // Bu metod GameManager tarafından çağrılır
        // Şu anda özel bir UI güncellemesi yapmıyor ama gelecekte eklenebilir
        Debug.Log($"🎯 UIManager: Toplam oyuncu sayısı güncellendi - {toplamOyuncuSayisi}");
    }
}