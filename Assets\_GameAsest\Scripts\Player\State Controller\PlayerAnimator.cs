using UnityEngine;

public class PlayerAnimator : MonoBehaviour
{
    private Animator animator;

    void Awake()
    {
        animator = GetComponentInChildren<Animator>();
    }

    public void UpdateAnimationState(bool isMoving, bool isSliding, bool isGrounded)
    {
        if (animator == null) return;
        animator.SetBool("isMoving", isMoving);
        animator.SetBool("isSliding", isSliding);
        animator.SetBool("isGrounded", isGrounded);
    }

    public void TriggerJump()
    {
        if (animator == null) return;
        animator.SetTrigger("Jump");
    }

    // --- YENİ EKLENEN KOD ---
    /// <summary>
    /// Kutlama animasyonunu bir kereliğine tetikler.
    /// Yarış bittiğinde FinishedState tarafından çağrılır.
    /// </summary>
    public void TriggerCelebration()
    {
        if (animator == null) return;
        animator.SetTrigger("Celebrate");
    }
}