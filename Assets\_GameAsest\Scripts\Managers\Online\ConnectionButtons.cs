using Unity.Netcode;
using UnityEngine;
using UnityEngine.UI;

public class ConnectionButtons : MonoBehaviour
{
    public Button hostButton;
    public Button serverButton;
    public Button clientButton;

    void Start()
    {
        // Null kontrolü ekle - butonlar atanmamışsa hata vermesin
        if (hostButton != null)
        {
            hostButton.onClick.AddListener(() => {
                Debug.Log("🎮 Host başlatılıyor - Lobby sahnesine gidiliyor...");
string myName = PlayerData.PlayerName;
                NetworkManager.Singleton.NetworkConfig.ConnectionData =
                System.Text.Encoding.UTF8.GetBytes(myName);
                NetworkManager.Singleton.StartHost();
                NetworkManager.Singleton.SceneManager.LoadScene("3DLobby", UnityEngine.SceneManagement.LoadSceneMode.Single);
            });
        }

        if (serverButton != null)
            serverButton.onClick.AddListener(StartServerFromButton);

        if (clientButton != null)
        {
            clientButton.onClick.AddListener(() => {
                Debug.Log("🔗 Client başlatılıyor - Host'a katılınıyor...");

                // Localhost'a bağlan
                var transport = NetworkManager.Singleton.NetworkConfig.NetworkTransport;
                if (transport is Unity.Netcode.Transports.UTP.UnityTransport unityTransport)
                {
                    unityTransport.SetConnectionData("127.0.0.1", 7777);
                    Debug.Log("🌐 Localhost:7777'ye bağlanılıyor...");
                }

string myName = PlayerData.PlayerName;
                Debug.Log($"🔍 [ConnectionButtons] Client bağlanırken gönderilen isim: '{myName}'");
                NetworkManager.Singleton.NetworkConfig.ConnectionData =
                System.Text.Encoding.UTF8.GetBytes(myName);
                NetworkManager.Singleton.StartClient();
            });
        }
    }

    public void StartServerFromButton()
    {
        Debug.Log("BUTON ÇALIŞTI");

        if (NetworkManager.Singleton == null)
        {
            Debug.LogError("NetworkManager Singleton YOK");
            return;
        }

        Debug.Log("NetworkManager Singleton VAR");

        if (NetworkManager.Singleton.NetworkConfig == null)
        {
            Debug.LogError("NetworkConfig YOK");
            return;
        }

        if (NetworkManager.Singleton.NetworkConfig.NetworkTransport == null)
        {
            Debug.LogError("NetworkTransport ATANMAMIŞ!");
            return;
        }

        Debug.Log("HER ŞEY TAM, SUNUCU BAŞLIYOR");
        NetworkManager.Singleton.StartServer();
    }
}