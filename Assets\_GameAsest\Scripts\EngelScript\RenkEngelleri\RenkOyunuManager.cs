    // --- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> okuması için public metodlar ---
  
using UnityEngine;
using Unity.Netcode;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

public class RenkOyunuManager : NetworkBehaviour
{
    public static RenkOyunuManager Instance { get; private set; }

    [Header("Tur Ayarları")]
    public float turBeklemeSuresi = 5f;
    public float renkSecimSuresi = 10f;

    [Header("Zorluk Ayarları")] // YENİ
    public float sureAzalmaMiktari = 0.5f; // Her turda süre ne kadar azalsın?
    public float minimumSure = 4f; // Süre en fazla ne kadar kısalabilir?

    private float _mevcutTurSuresi; // YENİ: O anki turun süresini tutacak değişken
    
    [Header("Referanslar")]
    public ArayuzYoneticisi uiManager; // YENİ: UI Manager referansı

    // --- AĞ DEĞİŞKENLERİ ---
    // SADECE SERVER YAZABILIR - Client'lar sadece okuyabilir
    private NetworkVariable<PlatformRenkleri> _guvenliRenk = new NetworkVariable<PlatformRenkleri>(
        PlatformRenkleri.Kirmizi,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);

    private NetworkVariable<float> _kalanSure = new NetworkVariable<float>(
        0f,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);

    private NetworkVariable<int> _hayattakiOyuncuSayisi = new NetworkVariable<int>(
        0,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);

    private NetworkVariable<int> _turBasindakiOyuncuSayisi = new NetworkVariable<int>(
        0,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);

    private NetworkVariable<bool> _turDevamEdiyor = new NetworkVariable<bool>(
        false,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);
    
    // YENİ: Hayatta kalan oyuncuların ID'lerini tutan senkronize liste (Artık public)
    public NetworkList<ulong> HayattakiOyuncuIDleri { get; private set; }

    private List<RenkPlatformu> tumPlatformlar;
    private List<Vector3> orijinalPozisyonlar;

    [Header("Minigame Ayarları")]
    public float minigameToplamSuresi = 130f;

    private NetworkVariable<float> minigameKalanSure = new NetworkVariable<float>(
        0f,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);

    private void Awake()
    {
        Instance = this;
        HayattakiOyuncuIDleri = new NetworkList<ulong>(); // YENİ: Listeyi oluştur
    }

    // --- Arayüz için public getter metodları ---
    public float GetKalanSure()
    {
        return _kalanSure.Value;
    }

    public PlatformRenkleri GetGuvenliRenk()
    {
        return _guvenliRenk.Value;
    }

    public bool OyunBasladiMi()
    {
        return _turDevamEdiyor.Value; // _oyunBasladi yerine _turDevamEdiyor'u kullanalım
    }

    public override void OnNetworkSpawn()
    {
        // Platform'ları bul ve isimlerine göre sırala (tutarlı sıralama için)
        tumPlatformlar = FindObjectsByType<RenkPlatformu>(FindObjectsSortMode.None)
            .OrderBy(p => p.name)
            .ToList();
        orijinalPozisyonlar = tumPlatformlar.Select(p => p.transform.position).ToList();

        Debug.Log($"🎮 RenkOyunuManager: {tumPlatformlar.Count} platform bulundu ve sıralandı");
        
        if (IsClient)
        {
            _kalanSure.OnValueChanged += (eskiDeger, yeniDeger) => uiManager.ZamanGuncelle(renkSecimSuresi, renkSecimSuresi - yeniDeger);
            minigameKalanSure.OnValueChanged += (oldVal, newVal) =>
            {
                if (newVal == 0) return; // 0 olduğunda UI güncellenmesin
                uiManager.ZamanGuncelle(minigameToplamSuresi, minigameToplamSuresi - newVal);
            };

            // Güvenli renk değiştiğinde UI'ı güncelle
            _guvenliRenk.OnValueChanged += (eskiRenk, yeniRenk) =>
            {
                if (uiManager != null)
                {
                    // UI'da renk bilgisini güncelle
                    uiManager.ZamanGuncelle(_kalanSure.Value > 0 ? renkSecimSuresi : minigameToplamSuresi,
                                          _kalanSure.Value > 0 ? renkSecimSuresi - _kalanSure.Value : minigameToplamSuresi - minigameKalanSure.Value);
                }
            };
        }
        
        if (IsServer)
        {
            Debug.Log("🎮 RenkOyunuManager: Server'da başlatıldı.");
            // Tüm oyuncuların sahne yüklenmesini bekle
            StartCoroutine(TumOyuncularSahneYuklenmesiniBekle());
            NetworkManager.OnClientConnectedCallback += OnClientConnected;
        }
    }
    
    /// <summary>
    /// Tüm oyuncuların sahne yüklenmesini bekle
    /// </summary>
    private System.Collections.IEnumerator TumOyuncularSahneYuklenmesiniBekle()
    {
        if (!IsServer) yield break;

        Debug.Log("⏳ RenkOyunuManager: Tüm oyuncuların sahne yüklenmesi bekleniyor...");

        float beklemeSuresi = 0f;
        float maksimumBekleme = 15f; // 15 saniye maksimum bekleme
        int sonOyuncuSayisi = 0;
        float sabitSayiSuresi = 0f;

        while (beklemeSuresi < maksimumBekleme)
        {
            int mevcutOyuncuSayisi = NetworkManager.Singleton.ConnectedClientsIds.Count;

            // Oyuncu sayısı değişti mi?
            if (mevcutOyuncuSayisi != sonOyuncuSayisi)
            {
                sonOyuncuSayisi = mevcutOyuncuSayisi;
                sabitSayiSuresi = 0f;
                Debug.Log($"🔄 RenkOyunuManager: Oyuncu sayısı değişti: {mevcutOyuncuSayisi}");
            }
            else
            {
                sabitSayiSuresi += 0.5f;
            }

            // Oyuncu sayısı 2 saniye boyunca sabit kaldıysa başlat
            if (sabitSayiSuresi >= 2f)
            {
                Debug.Log($"✅ RenkOyunuManager: Oyuncu sayısı sabit ({mevcutOyuncuSayisi}), oyun başlatılıyor...");
                break;
            }

            // Tek oyuncuysa direkt başlat
            if (mevcutOyuncuSayisi <= 1)
            {
                Debug.Log("🎯 RenkOyunuManager: Tek oyuncu, oyun başlatılıyor...");
                break;
            }

            yield return new WaitForSeconds(0.5f);
            beklemeSuresi += 0.5f;
        }

        if (beklemeSuresi >= maksimumBekleme)
        {
            Debug.LogWarning("⚠️ RenkOyunuManager: Maksimum bekleme süresi aşıldı, oyun başlatılıyor...");
        }

        // Oyun başlarken tüm oyuncuları resetle ve listeye ekle
        if (IsServer)
        {
            HayattakiOyuncuIDleri.Clear();
            foreach (ulong clientId in NetworkManager.Singleton.ConnectedClientsIds)
            {
                if (NetworkManager.Singleton.ConnectedClients.ContainsKey(clientId))
                {
                    var clientObj = NetworkManager.Singleton.ConnectedClients[clientId].PlayerObject;
                    if (clientObj != null)
                    {
                        var oyuncu = clientObj.GetComponent<OyuncuKontrol>();
                        if (oyuncu != null)
                        {
                            // Oyuncuyu resetle (elendi durumunu false yap ve görünürlüğü aç)
                            oyuncu.OyuncuyuResetla();
                            HayattakiOyuncuIDleri.Add(clientId);
                        }
                    }
                }
            }
            _hayattakiOyuncuSayisi.Value = HayattakiOyuncuIDleri.Count;
            
            // Client'lara oyun başlangıcını bildir
            OyunBaslangiciClientRpc();
        }
        
        // Kısa bir delay ile oyun döngüsünü başlat (client sync için)
        yield return new WaitForSeconds(0.5f);
        StartCoroutine(OyunDongusu());
    }

    public override void OnNetworkDespawn() // YENİ: Hafızayı temizlemek için
    {
        if (IsServer)
        {
            NetworkManager.OnClientConnectedCallback -= OnClientConnected;
        }

        base.OnNetworkDespawn();
        HayattakiOyuncuIDleri?.Dispose();
    }
    
    // YENİ: Bir oyuncu elendiğinde bu RPC çağrılacak
    [ServerRpc(RequireOwnership = false)]
    /// <summary>
    /// Bir oyuncu elendiğinde çağrılan ServerRpc
    /// Oyuncu sayısını kontrol eder ve gerekirse oyunu bitirir
    /// </summary>
    public void OyuncuEleninceHaberVerServerRpc(ServerRpcParams rpcParams = default)
    {
        if (!IsServer) return;
        
        ulong elenenOyuncuID = rpcParams.Receive.SenderClientId;

        // Oyuncuyu listeden çıkar (eğer listede varsa)
        if (HayattakiOyuncuIDleri.Contains(elenenOyuncuID))
        {
            HayattakiOyuncuIDleri.Remove(elenenOyuncuID);
        }
        
        // Manuel kontrol: Gerçekte kaç oyuncu hayatta
        int gercekHayattaOyuncuSayisi = 0;
        foreach (ulong clientId in NetworkManager.Singleton.ConnectedClientsIds)
        {
            if (NetworkManager.Singleton.ConnectedClients.ContainsKey(clientId))
            {
                var clientObj = NetworkManager.Singleton.ConnectedClients[clientId].PlayerObject;
                if (clientObj != null)
                {
                    var oyuncu = clientObj.GetComponent<OyuncuKontrol>();
                    if (oyuncu != null && !oyuncu.elendi.Value)
                    {
                        gercekHayattaOyuncuSayisi++;
                    }
                }
            }
        }
        
        // Oyuncu sayısını güncelle
        _hayattakiOyuncuSayisi.Value = gercekHayattaOyuncuSayisi;
        
        // Tüm oyuncular elendiyse oyunu bitir
        if (gercekHayattaOyuncuSayisi <= 0)
        {
            minigameKalanSure.Value = 0f;
            TuruBitir();
            OyunBitinceSifirlaArayuzuClientRpc();
            // Oyuncuları resetleme - sadece sahne değişikliğinde resetlenecek

            // Minigame seçme ekranını göster
            StartCoroutine(ShowMinigameSelectionAfterDelay());
        }
        else
        {
            Debug.Log($"✅ Oyuncu elendi ama oyun devam ediyor. Gerçek kalan: {gercekHayattaOyuncuSayisi} oyuncu");
        }
    }

    /// <summary>
    /// Turu bitirir (sahne geçişi artık voting sistemi tarafından yapılacak)
    /// </summary>
    private void TuruBitir()
    {
        if (!_turDevamEdiyor.Value) return;
        _turDevamEdiyor.Value = false;

        // UniversalSceneManager çağrısı kaldırıldı - voting sistemi sahne geçişini yapacak
        Debug.Log("🏁 Tur bitti, voting sistemi sahne geçişini yapacak");
    }

    private IEnumerator OyunDongusu()
    {
        minigameKalanSure.Value = minigameToplamSuresi;
        float kalanMinigameSuresi = minigameToplamSuresi;
        _mevcutTurSuresi = renkSecimSuresi;

        // --- YENİ: Platform pozisyonlarını ve renklerini karıştır ---
        if (IsServer)
        {
            // Pozisyonları karıştır
            List<Vector3> karisikPozisyonlar = new List<Vector3>(orijinalPozisyonlar);
            for (int i = 0; i < karisikPozisyonlar.Count; i++)
            {
                Vector3 temp = karisikPozisyonlar[i];
                int randomIndex = Random.Range(i, karisikPozisyonlar.Count);
                karisikPozisyonlar[i] = karisikPozisyonlar[randomIndex];
                karisikPozisyonlar[randomIndex] = temp;
            }

            // Renkleri karıştır - tüm platform renklerini yeniden ata
            List<PlatformRenkleri> tumRenkler = new List<PlatformRenkleri>();
            foreach (var platform in tumPlatformlar)
            {
                tumRenkler.Add(platform.buPlatformunRengi);
            }

            // Renkleri karıştır
            for (int i = 0; i < tumRenkler.Count; i++)
            {
                PlatformRenkleri temp = tumRenkler[i];
                int randomIndex = Random.Range(i, tumRenkler.Count);
                tumRenkler[i] = tumRenkler[randomIndex];
                tumRenkler[randomIndex] = temp;
            }

            // Platform pozisyonlarını ve renklerini uygula
            for (int i = 0; i < tumPlatformlar.Count; i++)
            {
                tumPlatformlar[i].transform.position = karisikPozisyonlar[i];
                // Platform rengini server'da senkronize et
                tumPlatformlar[i].SetPlatformRenkServerRpc(tumRenkler[i]);
            }

            // Platform pozisyonlarını tüm client'lara senkronize et
            SenkronizePlatformPozisyonlariClientRpc(karisikPozisyonlar.ToArray());
        }

        // --- YENİ: Minigame süresi boyunca döngü ---
        while (kalanMinigameSuresi > 0f && _hayattakiOyuncuSayisi.Value > 0)
        {
            // Bekleme süresi boyunca puan artışı
            float beklemePuanZamanlayici = 0f;
            float beklemeSure = turBeklemeSuresi;
            while (beklemeSure > 0f && kalanMinigameSuresi > 0f)
            {
                float delta = Mathf.Min(Time.deltaTime, beklemeSure);
                beklemeSure -= delta;
                kalanMinigameSuresi -= delta;
                minigameKalanSure.Value = kalanMinigameSuresi;
                beklemePuanZamanlayici += delta;
                while (beklemePuanZamanlayici >= 0.5f)
                {
                    beklemePuanZamanlayici -= 0.5f;
                    foreach (ulong clientId in HayattakiOyuncuIDleri)
                    {
                        if (NetworkManager.Singleton.ConnectedClients.ContainsKey(clientId))
                        {
                            var clientObj = NetworkManager.Singleton.ConnectedClients[clientId].PlayerObject;
                            if (clientObj != null)
                            {
                                var oyuncu = clientObj.GetComponent<OyuncuKontrol>();
                                if (oyuncu != null && !oyuncu.elendi.Value)
                                {
                                    oyuncu.puan.Value += 1;
                                }
                            }
                        }
                    }
                }
                // --- YENİ: Arayüze kalan minigame süresini gönder ---
                if (IsClient || IsHost)
                    uiManager.ZamanGuncelle(minigameToplamSuresi, minigameToplamSuresi - kalanMinigameSuresi);
                yield return null;
            }

            // --- TUR BAŞLANGICI ---
            _turDevamEdiyor.Value = true;

            // ✅ SADECE İLK TURDA LİSTEYİ OLUŞTUR, SONRAKI TURLARDA SADECE GÜNCELLE
            if (IsServer)
            {
                // İlk tur mu kontrol et
                bool ilkTur = _turBasindakiOyuncuSayisi.Value == 0;
                
                if (ilkTur)
                {
                    // İlk turda tüm oyuncuları ekle
                    HayattakiOyuncuIDleri.Clear();
                    foreach (ulong clientId in NetworkManager.Singleton.ConnectedClientsIds)
                    {
                        if (NetworkManager.Singleton.ConnectedClients.ContainsKey(clientId))
                        {
                            var clientObj = NetworkManager.Singleton.ConnectedClients[clientId].PlayerObject;
                            if (clientObj != null)
                            {
                                var oyuncu = clientObj.GetComponent<OyuncuKontrol>();
                                if (oyuncu != null && !oyuncu.elendi.Value)
                                {
                                    HayattakiOyuncuIDleri.Add(clientId);
                                }
                            }
                        }
                    }
                }
                
                _turBasindakiOyuncuSayisi.Value = HayattakiOyuncuIDleri.Count;
                _hayattakiOyuncuSayisi.Value = HayattakiOyuncuIDleri.Count;
            }

            foreach (var platform in tumPlatformlar)
            {
                platform.GeriGelClientRpc();
            }

            // SADECE SERVER RENK DEĞİŞTİREBİLİR
            if (IsServer)
            {
                _guvenliRenk.Value = (PlatformRenkleri)Random.Range(0, System.Enum.GetValues(typeof(PlatformRenkleri)).Length);
                Debug.Log($"Server yeni güvenli renk belirledi: {_guvenliRenk.Value}");
            }

            // GÜNCELLENDİ: Sabit süre yerine, her tur azalan süreyi kullan
            _kalanSure.Value = _mevcutTurSuresi;
            float puanZamanlayici = 0f;
            while (_kalanSure.Value > 0 && _turDevamEdiyor.Value && kalanMinigameSuresi > 0f)
            {
                float delta = Mathf.Min(Time.deltaTime, _kalanSure.Value);
                _kalanSure.Value -= delta;
                kalanMinigameSuresi -= delta;
                minigameKalanSure.Value = kalanMinigameSuresi;
                puanZamanlayici += delta;
                while (puanZamanlayici >= 0.5f)
                {
                    puanZamanlayici -= 0.5f;
                    foreach (ulong clientId in HayattakiOyuncuIDleri)
                    {
                        if (NetworkManager.Singleton.ConnectedClients.ContainsKey(clientId))
                        {
                            var clientObj = NetworkManager.Singleton.ConnectedClients[clientId].PlayerObject;
                            if (clientObj != null)
                            {
                                var oyuncu = clientObj.GetComponent<OyuncuKontrol>();
                                if (oyuncu != null && !oyuncu.elendi.Value)
                                {
                                    oyuncu.puan.Value += 1;
                                }
                            }
                        }
                    }
                }
                // --- YENİ: Arayüze kalan minigame süresini gönder ---
                if (IsClient || IsHost)
                    uiManager.ZamanGuncelle(minigameToplamSuresi, minigameToplamSuresi - kalanMinigameSuresi);
                yield return null;
            }

            if (!_turDevamEdiyor.Value) continue; // Tur zaten bitmişse platformları yok etme

            // --- KONTROL AŞAMASI ---
            foreach (var platform in tumPlatformlar)
            {
                if (platform.buPlatformunRengi != _guvenliRenk.Value)
                {
                    platform.KaybolClientRpc();
                }
            }

            // YENİ: Tur başarıyla bittiğinde, bir sonraki tur için süreyi kısalt
            if (IsServer)
            {
                _mevcutTurSuresi -= sureAzalmaMiktari;
                if (_mevcutTurSuresi < minimumSure)
                {
                    _mevcutTurSuresi = minimumSure;
                }
            }
        }
        // --- Minigame bittiğinde burada oyun sonu işlemleri yapılabilir ---
        Debug.Log("Minigame bitti!");
        OyunBitinceSifirlaArayuzuClientRpc();
        // Oyuncuları resetleme - sadece sahne değişikliğinde resetlenecek

        // Minigame seçme ekranını göster
        StartCoroutine(ShowMinigameSelectionAfterDelay());
    }

    private void OnClientConnected(ulong clientId)
    {
        // NetworkVariable'lar server-only olduğu için otomatik olarak senkronize olacak
        // Yeni oyuncu bağlandığında herhangi bir manuel işlem yapmaya gerek yok
        Debug.Log($"Yeni oyuncu bağlandı: {clientId}. NetworkVariable'lar otomatik senkronize olacak.");
    }



    public IEnumerable<ulong> GetHayattakiOyuncuIDleri()
    {
        List<ulong> result = new List<ulong>();
        foreach (var id in HayattakiOyuncuIDleri)
            result.Add(id);
        return result;
    }

    public void HayattakiOyuncudanCikar(ulong clientId)
    {
        if (HayattakiOyuncuIDleri.Contains(clientId))
        {
            HayattakiOyuncuIDleri.Remove(clientId);
        }
    }

    /// <summary>
    /// SADECE SERVER tarafından çağrılabilir - Güvenli rengi değiştirir
    /// </summary>
    /// <param name="yeniRenk">Yeni güvenli renk</param>
    [ServerRpc(RequireOwnership = false)]
    public void GuvenliRenkDegistirServerRpc(PlatformRenkleri yeniRenk)
    {
        if (!IsServer)
        {
            Debug.LogError("GuvenliRenkDegistirServerRpc sadece server'da çalışabilir!");
            return;
        }

        _guvenliRenk.Value = yeniRenk;
        Debug.Log($"Server güvenli rengi değiştirdi: {yeniRenk}");
    }

    /// <summary>
    /// Güvenli rengi manuel olarak değiştirmek için (sadece server)
    /// </summary>
    public void ManuelRenkDegistir(PlatformRenkleri yeniRenk)
    {
        if (!IsServer)
        {
            Debug.LogError("ManuelRenkDegistir sadece server'da çalışabilir!");
            return;
        }

        _guvenliRenk.Value = yeniRenk;
        Debug.Log($"Server manuel olarak güvenli rengi değiştirdi: {yeniRenk}");
    }

    /// <summary>
    /// Platform pozisyonlarını tüm client'lara senkronize eder
    /// </summary>
    [ClientRpc]
    private void SenkronizePlatformPozisyonlariClientRpc(Vector3[] yeniPozisyonlar)
    {
        if (tumPlatformlar == null || yeniPozisyonlar == null) return;

        if (tumPlatformlar.Count != yeniPozisyonlar.Length)
        {
            Debug.LogError($"Platform sayısı ({tumPlatformlar.Count}) ile pozisyon sayısı ({yeniPozisyonlar.Length}) eşleşmiyor!");
            return;
        }

        // Tüm client'larda platform pozisyonlarını güncelle
        for (int i = 0; i < tumPlatformlar.Count; i++)
        {
            if (tumPlatformlar[i] != null)
            {
                tumPlatformlar[i].transform.position = yeniPozisyonlar[i];
                Debug.Log($"🔄 Client: Platform {tumPlatformlar[i].name} pozisyonu güncellendi: {yeniPozisyonlar[i]}");
            }
        }
    }



    /// <summary>
    /// Minigame bittiğinde MinigameSelection sahnesine geç
    /// </summary>
    private IEnumerator ShowMinigameSelectionAfterDelay()
    {
        // 3 saniye bekle (oyuncuların sonucu görmesi için)
        yield return new WaitForSeconds(3f);

        if (IsServer)
        {
            Debug.Log("🎯 Minigame bitti, MinigameSelection sahnesine geçiliyor...");

            // MinigameSelection sahnesine geç (eğer varsa)
            if (NetworkManager.Singleton != null && NetworkManager.Singleton.SceneManager != null)
            {
                // Önce MinigameSelection sahnesini dene
                try
                {
                    NetworkManager.Singleton.SceneManager.LoadScene("MinigameSelection", UnityEngine.SceneManagement.LoadSceneMode.Single);
                    Debug.Log("✅ MinigameSelection sahnesine geçiş başlatıldı");
                }
                catch
                {
                    // MinigameSelection sahnesi yoksa 3DLobby'ye geç
                    Debug.LogWarning("⚠️ MinigameSelection sahnesi bulunamadı, 3DLobby'ye geçiliyor...");
                    NetworkManager.Singleton.SceneManager.LoadScene("3DLobby", UnityEngine.SceneManagement.LoadSceneMode.Single);
                    Debug.Log("✅ 3DLobby sahnesine geçiş başlatıldı");
                }
            }
            else
            {
                Debug.LogError("❌ NetworkManager veya SceneManager bulunamadı!");
            }
        }
    }

    /// <summary>
    /// Oyun başlangıcında tüm client'lara gönderilir
    /// Client'larda oyuncu durumlarını resetler
    /// </summary>
    [ClientRpc]
    private void OyunBaslangiciClientRpc()
    {
        Debug.Log("🎮 Client: Oyun başlangıcı bildirimi alındı, oyuncular resetleniyor...");
        
        // Tüm oyuncuları bul ve resetle
        var tumOyuncular = FindObjectsByType<OyuncuKontrol>(FindObjectsSortMode.None);
        foreach (var oyuncu in tumOyuncular)
        {
            if (oyuncu != null)
            {
                // Client-side resetleme
                oyuncu.ClientSideReset();
            }
        }
    }

    [ClientRpc]
    private void OyunBitinceSifirlaArayuzuClientRpc()
    {
        if (ArayuzYoneticisi.Instance != null)
        {
            ArayuzYoneticisi.Instance.SabitSifirSureGoster();
        }
    }
}