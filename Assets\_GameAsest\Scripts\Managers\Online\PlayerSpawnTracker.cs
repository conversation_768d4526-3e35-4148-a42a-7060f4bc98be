using UnityEngine;
using Unity.Netcode;
using System.Collections.Generic;

/// <summary>
/// Global player spawn tracker - double spawn'ları önler
/// Tüm spawn sistemleri bu tracker'ı kontrol etmeli
/// </summary>
public class PlayerSpawnTracker : NetworkBehaviour
{
    public static PlayerSpawnTracker Instance { get; private set; }

    [Header("Debug")]
    public bool showDebugLogs = false;

    // Spawn edilmiş player'ları takip et
    private static HashSet<ulong> spawnedPlayers = new HashSet<ulong>();
    private static Dictionary<ulong, GameObject> playerObjects = new Dictionary<ulong, GameObject>();

    private void Awake()
    {
        // Singleton pattern
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();

        if (IsServer)
        {
            // Network event'leri dinle
            NetworkManager.Singleton.OnClientConnectedCallback += OnClientConnected;
            NetworkManager.Singleton.OnClientDisconnectCallback += OnClientDisconnected;
        }
    }

    public override void OnNetworkDespawn()
    {
        if (IsServer && NetworkManager.Singleton != null)
        {
            NetworkManager.Singleton.OnClientConnectedCallback -= OnClientConnected;
            NetworkManager.Singleton.OnClientDisconnectCallback -= OnClientDisconnected;
        }

        if (Instance == this)
        {
            Instance = null;
        }
    }

    private void OnClientConnected(ulong clientId)
    {
        if (showDebugLogs)
            Debug.Log($"🔍 PlayerSpawnTracker: Client {clientId} bağlandı");
    }

    private void OnClientDisconnected(ulong clientId)
    {
        if (showDebugLogs)
            Debug.Log($"🔍 PlayerSpawnTracker: Client {clientId} ayrıldı, spawn kaydı temizleniyor");

        // Spawn kaydını temizle
        RemovePlayerSpawnRecord(clientId);
    }



    /// <summary>
    /// Player spawn edilmeden önce kontrol et
    /// </summary>
    public static bool CanSpawnPlayer(ulong clientId)
    {
        bool canSpawn = !spawnedPlayers.Contains(clientId);
        
        if (Instance != null && Instance.showDebugLogs)
        {
            if (canSpawn)
                Debug.Log($"✅ PlayerSpawnTracker: Client {clientId} spawn edilebilir");
            else
                Debug.LogWarning($"⚠️ PlayerSpawnTracker: Client {clientId} zaten spawn edilmiş!");
        }

        return canSpawn;
    }

    /// <summary>
    /// Player spawn edildiğinde kaydet
    /// </summary>
    public static void RegisterPlayerSpawn(ulong clientId, GameObject playerObject = null)
    {
        spawnedPlayers.Add(clientId);
        
        if (playerObject != null)
        {
            playerObjects[clientId] = playerObject;
        }

        if (Instance != null && Instance.showDebugLogs)
            Debug.Log($"📝 PlayerSpawnTracker: Client {clientId} spawn kaydı eklendi (Toplam: {spawnedPlayers.Count})");
    }

    /// <summary>
    /// Player spawn kaydını kaldır
    /// </summary>
    public static void RemovePlayerSpawnRecord(ulong clientId)
    {
        bool removed = spawnedPlayers.Remove(clientId);
        playerObjects.Remove(clientId);

        if (Instance != null && Instance.showDebugLogs && removed)
            Debug.Log($"🗑️ PlayerSpawnTracker: Client {clientId} spawn kaydı kaldırıldı (Toplam: {spawnedPlayers.Count})");
    }

    /// <summary>
    /// Player object'ini al
    /// </summary>
    public static GameObject GetPlayerObject(ulong clientId)
    {
        playerObjects.TryGetValue(clientId, out GameObject playerObj);
        return playerObj;
    }

    /// <summary>
    /// Tüm spawn kayıtlarını temizle
    /// </summary>
    public static void ClearAllSpawnRecords()
    {
        int count = spawnedPlayers.Count;
        spawnedPlayers.Clear();
        playerObjects.Clear();

        if (Instance != null && Instance.showDebugLogs)
            Debug.Log($"🧹 PlayerSpawnTracker: Tüm spawn kayıtları temizlendi ({count} kayıt)");
    }

    /// <summary>
    /// Spawn edilmiş player sayısı
    /// </summary>
    public static int GetSpawnedPlayerCount()
    {
        return spawnedPlayers.Count;
    }

    /// <summary>
    /// Debug bilgileri
    /// </summary>
    [ContextMenu("Debug Spawn Records")]
    public void DebugSpawnRecords()
    {
        Debug.Log("=== PLAYER SPAWN RECORDS ===");
        Debug.Log($"Toplam spawn edilmiş player: {spawnedPlayers.Count}");
    }
}
