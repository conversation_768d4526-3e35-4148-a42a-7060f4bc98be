using UnityEngine;

/// <summary>
/// MainMenu sahnesinde para gösterimini güncellemek için script.
/// UniversalSceneManager ile entegre çalışır.
/// </summary>
public class MainMenuParaGuncelleme : MonoBehaviour
{
    [Header("Referanslar")]
    [Tooltip("MainMenuUI referansı")]
    public MainMenuUI mainMenuUI;
    
    [Header("Ayarlar")]
    [Tooltip("Sahne yüklendiğinde otomatik güncelle")]
    public bool otomatikGuncelleme = true;
    
    [Tooltip("Debug mesajlarını göster")]
    public bool showDebugLogs = true;

    private void Start()
    {
        if (otomatikGuncelleme)
        {
            // MainMenuUI'ı bul
            if (mainMenuUI == null)
            {
                mainMenuUI = FindFirstObjectByType<MainMenuUI>();
            }
            
            // Para gösterimini güncelle
            GuncelleParaGosterimi();
        }
    }

    /// <summary>
    /// Oyuncu bilgilerini güncelle (isim + para)
    /// </summary>
    public void GuncelleParaGosterimi()
    {
        if (mainMenuUI == null)
        {
            if (showDebugLogs)
                Debug.LogWarning("⚠️ MainMenuParaGuncelleme: MainMenuUI bulunamadı!");
            return;
        }

        // Tüm oyuncu verilerini yükle
        PlayerData.TumVerileriYukle();

        // UI'ı güncelle
        mainMenuUI.OyuncuBilgileriGuncelle();

        if (showDebugLogs)
            Debug.Log($"👤💰 MainMenuParaGuncelleme: Oyuncu bilgileri güncellendi - İsim: {PlayerData.PlayerName}, Para: {PlayerData.ToplamPara}");
    }

    /// <summary>
    /// UniversalSceneManager tarafından çağrılabilir
    /// </summary>
    public void OyunBittiParaGuncelle()
    {
        // Kısa bir gecikme ile güncelle (sahne geçişi tamamlansın diye)
        Invoke(nameof(GuncelleParaGosterimi), 0.5f);
    }

    [ContextMenu("Manuel Para Güncelleme")]
    public void ManuelGuncelleme()
    {
        GuncelleParaGosterimi();
    }

    [ContextMenu("Test Para Ekle")]
    public void TestParaEkle()
    {
        PlayerData.ParaEkle(100);
        GuncelleParaGosterimi();
        Debug.Log("💰 Test: 100 para eklendi!");
    }

    [ContextMenu("Oyuncu Durumunu Kontrol Et")]
    public void ParaDurumuKontrol()
    {
        Debug.Log("=== OYUNCU DURUMU KONTROLÜ ===");
        Debug.Log($"PlayerData.PlayerName: {PlayerData.PlayerName}");
        Debug.Log($"PlayerData.ToplamPara: {PlayerData.ToplamPara}");
        Debug.Log($"PlayerPrefs PlayerName: {PlayerPrefs.GetString("PlayerName", "Yok")}");
        Debug.Log($"PlayerPrefs ToplamPara: {PlayerPrefs.GetInt("ToplamPara", 0)}");
        Debug.Log($"MainMenuUI: {(mainMenuUI != null ? "✅ Mevcut" : "❌ Eksik")}");
        Debug.Log("=== KONTROL TAMAMLANDI ===");
    }
}
