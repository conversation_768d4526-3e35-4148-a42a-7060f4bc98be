using UnityEngine;
using Unity.Netcode;
using System.Collections.Generic;

/// <summary>
/// Kakt<PERSON>s engeli için ragdoll tetikleyici script.
/// Karakter kaktüse temas ettiğinde ragdoll'u aktif eder.
/// Network multiplayer desteği ile ZeminKarosu pattern'ini takip eder.
/// </summary>
public class KaktusVurusu : NetworkBehaviour
{
    [Header("Kaktüs Darbe Ayarları")]
    [Tooltip("Kaktüsün karaktere ne kadar güçlü vuracağı.")]
    public float vurmaGucu = 150f;

    [Tooltip("Ragdoll'un minimum ne kadar süre aktif kalacağı.")]
    public float ragdollSuresi = 2.5f;

    [Header("İtme Yönü Ayarları")]
    [Tooltip("Karaktere yukarı doğru uygulanacak itme oranı (0-1 arası).")]
    [Range(0f, 1f)]
    public float yukariItmeOrani = 0.3f;

    [Tooltip("<PERSON>kt<PERSON><PERSON>ün geri itme kuvveti çarpanı.")]
    public float geriItmeMultiplier = 1.2f;

    [Header("Debug Ayarları")]
    [Tooltip("Debug mesajlarını göster.")]
    public bool debugMesajlari = false;

    private void OnTriggerEnter(Collider other)
    {
        // Ana oyuncu objesini bul (ragdoll parçaları için parent hierarchy'de ara)
        GameObject oyuncuObjesi = BulAnaOyuncuObjesi(other.gameObject);
        
        if (oyuncuObjesi == null)
        {
            if (debugMesajlari)
                Debug.Log($"Kaktüs: {other.name} objesi için ana oyuncu objesi bulunamadı.");
            return;
        }

        // Network multiplayer için: Sadece oyuncunun sahibi tetikleyebilir
        var netObj = oyuncuObjesi.GetComponent<NetworkObject>();
        if (netObj != null && netObj.IsOwner)
        {
            // Sunucuya kaktüs teması bildir
            KaktusTemasServerRpc();
        }
    }

    // Mobil optimizasyon için cache
    private static readonly Dictionary<GameObject, GameObject> oyuncuObjesiCache = new Dictionary<GameObject, GameObject>();
    private const int MAX_PARENT_DEPTH = 10; // Sonsuz döngü koruması

    /// <summary>
    /// Ragdoll parçası veya ana oyuncu objesi için ana oyuncu objesini bulur (Mobil optimize)
    /// </summary>
    /// <param name="obj">Kontrol edilecek GameObject</param>
    /// <returns>Ana oyuncu objesi veya null</returns>
    private GameObject BulAnaOyuncuObjesi(GameObject obj)
    {
        // Cache kontrolü - mobil performans için
        if (oyuncuObjesiCache.TryGetValue(obj, out GameObject cachedResult))
        {
            return cachedResult;
        }

        GameObject sonuc = BulAnaOyuncuObjesiInternal(obj);
        
        // Sonucu cache'le (null bile olsa)
        oyuncuObjesiCache[obj] = sonuc;
        
        // Cache boyutunu kontrol et (memory leak önlemi)
        if (oyuncuObjesiCache.Count > 100)
        {
            oyuncuObjesiCache.Clear();
        }
        
        return sonuc;
    }

    private GameObject BulAnaOyuncuObjesiInternal(GameObject obj)
    {
        // Önce direkt "Player" tag'i kontrol et
        if (obj.CompareTag("Player"))
        {
            return obj;
        }

        // Tek döngüde hem tag hem component kontrolü yap (mobil optimize)
        Transform current = obj.transform;
        int depth = 0;
        
        while (current != null && depth < MAX_PARENT_DEPTH)
        {
            // Player tag kontrolü
            if (current.CompareTag("Player"))
            {
                return current.gameObject;
            }

            // OyuncuKontrol komponenti kontrolü (GetComponent cache'lenir)
            if (current.TryGetComponent<OyuncuKontrol>(out _))
            {
                return current.gameObject;
            }

            // NetworkObject kontrolü
            if (current.TryGetComponent<NetworkObject>(out _) && current.CompareTag("Player"))
            {
                return current.gameObject;
            }

            current = current.parent;
            depth++;
        }

        return null;
    }

    [ServerRpc(RequireOwnership = false)]
    public void KaktusTemasServerRpc(ServerRpcParams rpcParams = default)
    {
        // RPC'yi gönderen istemcinin ID'sini al
        ulong temasEdenOyuncuId = rpcParams.Receive.SenderClientId;

        if (debugMesajlari)
            Debug.Log($"Kaktüs: Sunucuda temas işleniyor - Oyuncu ID: {temasEdenOyuncuId}");

        // Sunucu, komutu gönderen oyuncunun objesini bulur
        var oyuncuNetworkObject = NetworkManager.Singleton.ConnectedClients[temasEdenOyuncuId].PlayerObject;
        if (oyuncuNetworkObject == null) return;

        var sunucudakiOyuncu = oyuncuNetworkObject.GetComponent<OyuncuKontrol>();
        if (sunucudakiOyuncu == null) return;

        // RagdollManager'ı bul
        RagdollManager ragdollManager = sunucudakiOyuncu.GetComponent<RagdollManager>();
        if (ragdollManager == null)
        {
            Debug.LogError($"Kaktüs: Oyuncu {temasEdenOyuncuId} üzerinde RagdollManager bulunamadı!");
            return;
        }

        // Ragdoll zaten aktif mi kontrol et
        if (ragdollManager.IsRagdollActive)
        {
            if (debugMesajlari)
                Debug.Log("Kaktüs: Ragdoll zaten aktif, işlem yapılmadı.");
            return;
        }

        // İtme yönünü ve kuvvetini hesapla
        Vector3 oyuncuPozisyonu = sunucudakiOyuncu.transform.position;
        Vector3 itmeYonu = CalculateKnockbackDirection(oyuncuPozisyonu);
        Vector3 kuvvet = itmeYonu * vurmaGucu;

        if (debugMesajlari)
        {
            Debug.Log($"Kaktüs: {sunucudakiOyuncu.name} karakterine ragdoll uygulanıyor!");
            Debug.Log($"İtme Yönü: {itmeYonu}, Kuvvet: {kuvvet}");
        }

        // Eskisi gibi: Kuvvetli ragdoll + Client'a da haber ver

        // 1. Sunucuda kuvvetli ragdoll aktif et
        ragdollManager.DarbelereKarsiRagdollAktifEt(kuvvet, Vector3.zero, ragdollSuresi);

        // 2. Aynı zamanda client'a da ragdoll komutunu ve kuvveti gönder (network sync için)
        if (temasEdenOyuncuId != NetworkManager.Singleton.LocalClientId) // Host değilse client'a gönder
        {
            // Client'a hem ragdoll süresini hem de kuvveti gönder
            RagdollVeKuvvetClientRpc(ragdollSuresi, kuvvet, new ClientRpcParams
            {
                Send = new ClientRpcSendParams
                {
                    TargetClientIds = new ulong[] { temasEdenOyuncuId }
                }
            });
        }
    }

    /// <summary>
    /// Client'a kuvvetli ragdoll komutunu gönderir.
    /// Bu metod client'ta ragdoll'u kuvvetle birlikte aktif eder.
    /// </summary>
    [ClientRpc]
    private void RagdollVeKuvvetClientRpc(float ragdollSuresi, Vector3 kuvvet, ClientRpcParams clientRpcParams = default)
    {
        // Client'ta local oyuncuyu bul
        var localPlayer = NetworkManager.Singleton.LocalClient.PlayerObject;
        if (localPlayer == null) return;

        var ragdollManager = localPlayer.GetComponent<RagdollManager>();
        if (ragdollManager == null) return;

        if (debugMesajlari)
        {
            Debug.Log($"Kaktüs Client: Ragdoll aktif ediliyor - Kuvvet: {kuvvet}, Süre: {ragdollSuresi}");
        }

        // Ragdoll zaten aktif mi kontrol et
        if (ragdollManager.IsRagdollActive)
        {
            // Ragdoll zaten aktifse sadece force uygula
            ragdollManager.ClienttaSadeceForceUygula(kuvvet, Vector3.zero);
        }
        else
        {
            // Ragdoll aktif değilse, aktif et ve force'u da uygula
            ragdollManager.ClienttaRagdollAktifEt(kuvvet, Vector3.zero, ragdollSuresi);
        }
    }

    /// <summary>
    /// Kaktüsün karakteri hangi yöne iteceğini hesaplar.
    /// Karakteri kaktüsten uzaklaştıracak şekilde geri iter.
    /// </summary>
    /// <param name="karakterPozisyonu">Karakterin pozisyonu</param>
    /// <returns>Normalize edilmiş itme yönü</returns>
    private Vector3 CalculateKnockbackDirection(Vector3 karakterPozisyonu)
    {
        // Kaktüsten karaktere doğru olan yönü hesapla (geri itme için)
        Vector3 itmeYonu = (karakterPozisyonu - transform.position).normalized;
        
        // Yukarı doğru itme ekle (kaktüs dikenli olduğu için karakteri hafif yukarı da iter)
        itmeYonu.y += yukariItmeOrani;
        
        // Geri itme kuvvetini artır
        itmeYonu *= geriItmeMultiplier;
        
        // Yönü normalize et
        return itmeYonu.normalized;
    }

    /// <summary>
    /// Inspector'da kaktüsün etki alanını görselleştirmek için Gizmo çizer.
    /// </summary>
    private void OnDrawGizmosSelected()
    {
        // Kaktüsün trigger collider'ının boyutunu göster
        Collider triggerCollider = GetComponent<Collider>();
        if (triggerCollider != null && triggerCollider.isTrigger)
        {
            Gizmos.color = Color.red;
            Gizmos.matrix = transform.localToWorldMatrix;
            
            if (triggerCollider is BoxCollider boxCollider)
            {
                Gizmos.DrawWireCube(boxCollider.center, boxCollider.size);
            }
            else if (triggerCollider is SphereCollider sphereCollider)
            {
                Gizmos.DrawWireSphere(sphereCollider.center, sphereCollider.radius);
            }
            else if (triggerCollider is CapsuleCollider capsuleCollider)
            {
                // Capsule için basit bir sphere çiz
                Gizmos.DrawWireSphere(capsuleCollider.center, capsuleCollider.radius);
            }
        }
        
        // Kaktüsün merkezinden itme yönünü göster
        Gizmos.color = Color.yellow;
        Gizmos.DrawRay(transform.position, transform.up * 2f);
    }
}
