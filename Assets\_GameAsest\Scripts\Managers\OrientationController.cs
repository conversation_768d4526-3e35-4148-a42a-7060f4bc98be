using UnityEngine;

public class OrientationController : <PERSON>oBeh<PERSON><PERSON>
{
    // Bu script'in tek bir görevi var:
    // Kameranın baktığı yönü alıp, kendini o yöne çevirmek.
    void Update()
    {
        // Ana kameranın ileri yönünü al
        Vector3 viewDir = Camera.main.transform.forward;
        
        // Y eksenini sıfırla ki karakter yukarı/aşağı bakmasın
        viewDir.y = 0;

        // Kendi yönünü kameranın baktığı yöne ayarla
        transform.forward = viewDir.normalized;
    }
}