using UnityEngine;
using System.Collections;
using System.Text;

/// <summary>
/// Ultimate FPS Debug Tool - Unity'de performans sorunlarını teşhis etmek için
/// Tüm önemli FPS ve performans metriklerini konsola yazdırır
/// </summary>
public class UltimateDebugFPS : MonoBehaviour
{
    [Header("Debug Settings")]
    [Tooltip("Konsola yazma aralığı (saniye)")]
    public float updateInterval = 1.0f;
    
    [Tooltip("Detaylı MonoBehaviour analizi yapılsın mı?")]
    public bool detailedMonoBehaviourAnalysis = true;
    
    [Tooltip("Debug mesajlarını ekranda göster")]
    public bool showOnScreen = true;
    
    // FPS hesaplama değişkenleri
    private float accum = 0.0f;
    private int frames = 0;
    private float timeLeft;
    private float fps;
    
    // UI için
    private string debugText = "";
    private GUIStyle guiStyle = new GUIStyle();
    
    void Start()
    {
        // GUI ayarları
        guiStyle.fontSize = 16;
        guiStyle.normal.textColor = Color.white;
        
        // Interval'i başlat
        timeLeft = updateInterval;
        
        // Başlangıçta hemen bilgileri yazdır
        StartCoroutine(PrintInitialDebugInfo());
    }
    
    IEnumerator PrintInitialDebugInfo()
    {
        // Bir frame bekle ki tüm sistemler başlasın
        yield return null;
        
        // Başlangıç bilgilerini yazdır
        StringBuilder sb = new StringBuilder();
        sb.AppendLine("===== ULTIMATE FPS DEBUG - BAŞLANGIÇ BİLGİLERİ =====");
        sb.AppendLine($"Application.targetFrameRate: {Application.targetFrameRate}");
        sb.AppendLine($"QualitySettings.vSyncCount: {QualitySettings.vSyncCount}");
        sb.AppendLine($"Screen.currentResolution: {Screen.currentResolution.width}x{Screen.currentResolution.height}@{Screen.currentResolution.refreshRate}Hz");
        sb.AppendLine($"Time.timeScale: {Time.timeScale}");
        sb.AppendLine($"Time.fixedDeltaTime: {Time.fixedDeltaTime} ({1.0f/Time.fixedDeltaTime} physics FPS)");
        sb.AppendLine($"Time.maximumDeltaTime: {Time.maximumDeltaTime}");
        sb.AppendLine($"Graphics.activeTier: {Graphics.activeTier}");
        sb.AppendLine($"SystemInfo.graphicsDeviceName: {SystemInfo.graphicsDeviceName}");
        sb.AppendLine($"SystemInfo.graphicsMemorySize: {SystemInfo.graphicsMemorySize} MB");
        sb.AppendLine($"SystemInfo.processorCount: {SystemInfo.processorCount} cores");
        sb.AppendLine($"SystemInfo.systemMemorySize: {SystemInfo.systemMemorySize} MB");
        
        // MonoBehaviour sayısı
        int activeMBCount = CountActiveMonoBehaviours();
        sb.AppendLine($"Aktif MonoBehaviour sayısı: {activeMBCount}");
        
        // Detaylı analiz
        if (detailedMonoBehaviourAnalysis)
        {
            var monoBehaviourCounts = GetMonoBehaviourTypeCount();
            sb.AppendLine("En çok kullanılan MonoBehaviour tipleri:");
            foreach (var kvp in monoBehaviourCounts)
            {
                if (kvp.Value > 1) // Sadece 1'den fazla olanları göster
                {
                    sb.AppendLine($"  - {kvp.Key}: {kvp.Value} adet");
                }
            }
        }
        
        sb.AppendLine("=================================================");
        
        Debug.Log(sb.ToString());
    }
    
    void Update()
    {
        // FPS hesaplama
        timeLeft -= Time.unscaledDeltaTime;
        accum += Time.unscaledDeltaTime;
        frames++;
        
        // Güncelleme zamanı geldi mi?
        if (timeLeft <= 0.0f)
        {
            // FPS hesapla
            fps = frames / accum;
            
            // Debug bilgilerini oluştur
            GenerateDebugText();
            
            // Konsola yazdır
            Debug.Log(debugText);
            
            // Değerleri sıfırla
            timeLeft = updateInterval;
            accum = 0.0f;
            frames = 0;
        }
    }
    
    void GenerateDebugText()
    {
        StringBuilder sb = new StringBuilder();
        sb.AppendLine("===== ULTIMATE FPS DEBUG - RUNTIME BİLGİLERİ =====");
        sb.AppendLine($"FPS: {fps:F1}");
        sb.AppendLine($"Frame Time: {1000.0f/fps:F1} ms");
        sb.AppendLine($"Application.targetFrameRate: {Application.targetFrameRate}");
        sb.AppendLine($"QualitySettings.vSyncCount: {QualitySettings.vSyncCount}");
        sb.AppendLine($"Screen.currentResolution.refreshRate: {Screen.currentResolution.refreshRate}Hz");
        sb.AppendLine($"Time.timeScale: {Time.timeScale}");
        sb.AppendLine($"Time.deltaTime: {Time.deltaTime * 1000:F2} ms");
        sb.AppendLine($"Time.smoothDeltaTime: {Time.smoothDeltaTime * 1000:F2} ms");
        
        // MonoBehaviour sayısı
        int activeMBCount = CountActiveMonoBehaviours();
        sb.AppendLine($"Aktif MonoBehaviour sayısı: {activeMBCount}");
        
        // Detaylı analiz
        if (detailedMonoBehaviourAnalysis)
        {
            var monoBehaviourCounts = GetMonoBehaviourTypeCount();
            sb.AppendLine("En çok kullanılan MonoBehaviour tipleri (Top 5):");
            int count = 0;
            foreach (var kvp in monoBehaviourCounts)
            {
                if (count >= 5) break;
                if (kvp.Value > 1) // Sadece 1'den fazla olanları göster
                {
                    sb.AppendLine($"  - {kvp.Key}: {kvp.Value} adet");
                    count++;
                }
            }
        }
        
        sb.AppendLine("=================================================");
        
        debugText = sb.ToString();
    }
    
    void OnGUI()
    {
        if (showOnScreen)
        {
            GUI.Label(new Rect(10, 10, 500, 500), debugText, guiStyle);
        }
    }
    
    int CountActiveMonoBehaviours()
    {
        MonoBehaviour[] allMonoBehaviours = FindObjectsOfType<MonoBehaviour>();
        int activeCount = 0;
        
        foreach (MonoBehaviour mb in allMonoBehaviours)
        {
            if (mb.enabled && mb.gameObject.activeInHierarchy)
            {
                activeCount++;
            }
        }
        
        return activeCount;
    }
    
    System.Collections.Generic.Dictionary<string, int> GetMonoBehaviourTypeCount()
    {
        MonoBehaviour[] allMonoBehaviours = FindObjectsOfType<MonoBehaviour>();
        System.Collections.Generic.Dictionary<string, int> typeCounts = new System.Collections.Generic.Dictionary<string, int>();
        
        foreach (MonoBehaviour mb in allMonoBehaviours)
        {
            if (mb.enabled && mb.gameObject.activeInHierarchy)
            {
                string typeName = mb.GetType().Name;
                if (typeCounts.ContainsKey(typeName))
                {
                    typeCounts[typeName]++;
                }
                else
                {
                    typeCounts[typeName] = 1;
                }
            }
        }
        
        return typeCounts;
    }
}
