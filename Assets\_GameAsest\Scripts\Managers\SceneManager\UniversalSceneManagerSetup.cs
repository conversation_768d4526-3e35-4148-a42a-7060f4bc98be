using UnityEngine;
using Unity.Netcode;

/// <summary>
/// UniversalSceneManager'ı sahnelere otomatik olarak eklemek için yardımcı script.
/// Bu script'i sahneye eklerseniz, UniversalSceneManager otomatik olarak oluşturulur.
/// </summary>
public class UniversalSceneManagerSetup : MonoBehaviour
{
    [Header("Otomatik Kurulum")]
    [Tooltip("Sahne yüklendiğinde UniversalSceneManager'ı otomatik oluştur")]
    public bool otomatikKurulum = true;
    
    [Header("UniversalSceneManager Prefab")]
    [Tooltip("UniversalSceneManager prefab'ı (boş bırakılırsa otomatik oluşturulur)")]
    public GameObject universalSceneManagerPrefab;

    private void Start()
    {
        if (otomatikKurulum)
        {
            KurulumYap();
        }
    }

    public void KurulumYap()
    {
        // Zaten var mı kontrol et
        if (UniversalSceneManager.Instance != null)
        {
            Debug.Log("✅ UniversalSceneManagerSetup: UniversalSceneManager zaten mevcut.");
            return;
        }

        // Prefab varsa onu kullan
        if (universalSceneManagerPrefab != null)
        {
            GameObject instance = Instantiate(universalSceneManagerPrefab);
            Debug.Log("✅ UniversalSceneManagerSetup: UniversalSceneManager prefab'ından oluşturuldu.");
        }
        else
        {
            // Prefab yoksa otomatik oluştur
            OtomatikOlustur();
        }
    }

    private void OtomatikOlustur()
    {
        // Yeni GameObject oluştur
        GameObject managerObj = new GameObject("UniversalSceneManager");
        
        // NetworkObject ekle
        NetworkObject networkObject = managerObj.AddComponent<NetworkObject>();
        networkObject.DontDestroyWithOwner = true;
        
        // UniversalSceneManager ekle
        UniversalSceneManager manager = managerObj.AddComponent<UniversalSceneManager>();
        
        // Varsayılan ayarları yap
        manager.miniOyunSahneleri = new string[] { "RenkOyunu", "fakeblock_Scene" };
        manager.anaMenuSahnesi = "MainMenu";
        manager.sahneGecisiBeklemeSuresi = 3f;
        
        Debug.Log("✅ UniversalSceneManagerSetup: UniversalSceneManager otomatik olarak oluşturuldu.");
    }

    [ContextMenu("Manuel Kurulum")]
    public void ManuelKurulum()
    {
        KurulumYap();
    }

    [ContextMenu("UniversalSceneManager Durumunu Kontrol Et")]
    public void KontrolEt()
    {
        Debug.Log("=== UNIVERSAL SCENE MANAGER DURUM KONTROLÜ ===");

        if (UniversalSceneManager.Instance != null)
        {
            Debug.Log("✅ UniversalSceneManager: Mevcut");
        }
        else
        {
            Debug.LogWarning("❌ UniversalSceneManager: Bulunamadı!");
        }
    }
}
