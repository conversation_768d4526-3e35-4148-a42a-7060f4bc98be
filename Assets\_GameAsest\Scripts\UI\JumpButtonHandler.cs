using UnityEngine;
using UnityEngine.UI;
using System.Collections;

[RequireComponent(typeof(Button))]
public class JumpButtonHandler : MonoBehaviour
{
    [Header("Debug")]
    public bool debugMesajlari = false;

    private Button jumpButton;
    private bool oyuncuAraniyorMu = false;

    void Start()
    {
        jumpButton = GetComponent<Button>();
        jumpButton.onClick.AddListener(HandleJumpClick);

        if (debugMesajlari) Debug.Log("🎮 JumpButtonHandler başlatıldı");

        // Oyuncuyu bulmaya başla
        StartCoroutine(OyuncuyuBekle());
    }

    /// <summary>
    /// Oyuncunun spawn olmasını bekler
    /// </summary>
    private IEnumerator OyuncuyuBekle()
    {
        int deneme = 0;
        while (OyuncuKontrol.LocalInstance == null && deneme < 50) // 5 saniye bekle
        {
            // Manuel LocalInstance ayarlama denemesi
            if (deneme % 10 == 0) // Her saniye
            {
                OyuncuKontrol[] tumOyuncular = FindObjectsByType<OyuncuKontrol>(FindObjectsSortMode.None);
                foreach (var oyuncu in tumOyuncular)
                {
                    if (oyuncu != null && oyuncu.IsOwner && OyuncuKontrol.LocalInstance == null)
                    {
                        OyuncuKontrol.LocalInstance = oyuncu;
                        break;
                    }
                }
            }

            deneme++;
            yield return new WaitForSeconds(0.1f);
        }
    }

    private void HandleJumpClick()
    {
        // Önce LocalInstance'ı dene
        if (OyuncuKontrol.LocalInstance != null)
        {
            OyuncuKontrol.LocalInstance.Jump();
            return;
        }

        // LocalInstance null ise manuel ara
        if (!oyuncuAraniyorMu)
        {
            StartCoroutine(ManuelOyuncuAra());
        }
    }

    /// <summary>
    /// Manuel oyuncu arama coroutine'i
    /// </summary>
    private IEnumerator ManuelOyuncuAra()
    {
        oyuncuAraniyorMu = true;

        // Kısa bir süre bekle (oyuncu spawn olabilir)
        yield return new WaitForSeconds(0.1f);

        // Tüm oyuncuları ara
        OyuncuKontrol[] allPlayers = FindObjectsByType<OyuncuKontrol>(FindObjectsSortMode.None);

        // Önce IsOwner olanları ara
        foreach (var player in allPlayers)
        {
            if (player != null && player.IsOwner)
            {
                // LocalInstance null ise manuel olarak ayarla
                if (OyuncuKontrol.LocalInstance == null)
                {
                    OyuncuKontrol.LocalInstance = player;
                }

                player.Jump();
                oyuncuAraniyorMu = false;
                yield break;
            }
        }

        // IsOwner bulunamadıysa, ilk oyuncuyu dene (single player için)
        if (allPlayers.Length > 0 && allPlayers[0] != null)
        {
            if (OyuncuKontrol.LocalInstance == null)
            {
                OyuncuKontrol.LocalInstance = allPlayers[0];
            }
            allPlayers[0].Jump();
        }

        oyuncuAraniyorMu = false;
    }

    /// <summary>
    /// Component destroy edildiğinde temizlik
    /// </summary>
    private void OnDestroy()
    {
        if (jumpButton != null)
        {
            jumpButton.onClick.RemoveListener(HandleJumpClick);
        }
    }
}
