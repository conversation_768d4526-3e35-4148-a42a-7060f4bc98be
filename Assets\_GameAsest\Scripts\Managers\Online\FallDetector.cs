using UnityEngine;
using Unity.Netcode;

[RequireComponent(typeof(Collider))]
public class FallDetector : MonoBehaviour
{
    private void Awake()
    {
        // Bu objenin collider'ının trigger olduğundan emin olalım.
        GetComponent<Collider>().isTrigger = true;
    }

    private void OnTriggerEnter(Collider other)
    {
        // Sadece sunucu bu mantığı işlemeli
        if (!NetworkManager.Singleton.IsServer) return;

        // Tetikleyiciye giren objenin bir oyuncu olup olmadığını kontrol et
        if (other.TryGetComponent<OyuncuKontrol>(out OyuncuKontrol playerController))
        {
            Debug.Log($"Player {playerController.OwnerClientId} düştü. Respawn ediliyor.");
            SpawnManager.Instance.RespawnPlayer(playerController);
        }
    }
}