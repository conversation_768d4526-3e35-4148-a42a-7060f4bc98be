using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using Unity.Netcode;

/// <summary>
/// Minigame voting sistemi için UI yöneticisi.
/// Oyuncu oylarını gösterir ve oy verme arayüzünü yönetir.
/// </summary>
public class MinigameVotingUI : MonoBehaviour
{
    [Header("UI Referansları")]
    [Tooltip("Voting ekranının ana paneli")]
    public GameObject votingPanel;

    [Tooltip("Voting süresini gösteren text")]
    public TextMeshProUGUI votingTimerText;

    [Tooltip("Voting başlığı")]
    public TextMeshProUGUI votingTitleText;

    [Tooltip("Minigame butonlarının parent'ı")]
    public Transform minigameButtonsParent;

    [Tooltip("Minigame buton prefab'ı")]
    public GameObject minigameButtonPrefab;

    [<PERSON><PERSON>("Prefab Ayarları")]
    [Tooltip("Prefab'da MinigameCardComponent olmalı")]
    public bool useMinigameCardComponent = true;

    [Header("Ayarlar")]
    [Tooltip("Voting başlığı metni")]
    public string votingTitle = "Hangi Oyunu Oynamak İstiyorsun?";

    [Tooltip("Timer formatı")]
    public string timerFormat = "Kalan Süre: {0:F0}s";

    [Tooltip("Timer kırmızı olacağı süre eşiği")]
    public float redTimerThreshold = 3f;

    [Header("Timer Renkleri")]
    [Tooltip("Normal timer rengi")]
    public Color normalTimerColor = Color.white;

    [Tooltip("Kırmızı timer rengi")]
    public Color redTimerColor = Color.red;

    // UI elemanları
    private List<MinigameVoteButton> minigameButtons = new List<MinigameVoteButton>();
    private bool isInitialized = false;
    private bool isSubscribedToEvents = false;

    private void Start()
    {
        Debug.Log("🔧 [MinigameVotingUI] Start çağrıldı!");
        
        // Eğer votingPanel null ise, kendi gameObject'ini kullan
        if (votingPanel == null)
        {
            Debug.LogWarning("⚠️ [MinigameVotingUI] votingPanel null, gameObject kullanılıyor...");
            votingPanel = gameObject;
        }
        
        // Başlangıçta voting panelini gizle
        if (votingPanel != null)
        {
            votingPanel.SetActive(false);
            Debug.Log("🔧 [MinigameVotingUI] Voting paneli gizlendi");
        }

        // MinigameVotingManager event'lerine abone ol
        if (MinigameVotingManager.Instance != null && !isSubscribedToEvents)
        {
            Debug.Log("✅ [MinigameVotingUI] MinigameVotingManager bulundu, event'lere abone olunuyor...");
            SubscribeToVotingEvents();
            if (MinigameVotingManager.Instance.IsVotingActive)
            {
                OnVotingStarted();
            }
        }
        else
        {
            Debug.LogWarning("⚠️ [MinigameVotingUI] MinigameVotingManager bulunamadı, 0.5s beklenecek...");
            // Manager henüz hazır değilse, biraz bekle
            Invoke(nameof(DelayedSubscribe), 0.5f);
        }
    }

    private void OnEnable()
    {
        // UI yeniden enable olduğunda da event'lere abone olmayı zorunlu kıl
        if (MinigameVotingManager.Instance != null && !isSubscribedToEvents)
        {
            Debug.Log("[MinigameVotingUI] OnEnable: Manager bulundu, event'lere abone olunuyor...");
            SubscribeToVotingEvents();
            if (MinigameVotingManager.Instance.IsVotingActive)
            {
                OnVotingStarted();
            }
        }
    }

    private void DelayedSubscribe()
    {
        if (MinigameVotingManager.Instance != null)
        {
            Debug.Log("✅ [MinigameVotingUI] DelayedSubscribe: MinigameVotingManager bulundu!");
            SubscribeToVotingEvents();
        }
        else
        {
            Debug.LogError("❌ [MinigameVotingUI] DelayedSubscribe: MinigameVotingManager hala bulunamadı!");
        }
    }

    private void Update()
    {
        // Sürekli kontrol et - eğer voting aktifse ama panel kapalıysa aç
        if (MinigameVotingManager.Instance != null &&
            MinigameVotingManager.Instance.IsVotingActive &&
            votingPanel != null &&
            !votingPanel.activeInHierarchy)
        {
            Debug.Log("🔧 [MinigameVotingUI] Update: Voting aktif ama panel kapalı, açılıyor...");
            votingPanel.SetActive(true);

            // UI'ı güncelle
            if (!isInitialized)
            {
                InitializeVotingUI();
            }
            UpdateVotingTimer();
        }

        // Timer güncelleme
        if (MinigameVotingManager.Instance != null && MinigameVotingManager.Instance.IsVotingActive)
        {
            UpdateVotingTimer();
        }

        // YENI: Eğer MinigameVotingManager varsa ama event'lere abone değilsek, abone ol
        if (MinigameVotingManager.Instance != null && !isSubscribedToEvents)
        {
            Debug.Log("🔧 [MinigameVotingUI] Update: Manager bulundu, event'lere abone olunuyor...");
            SubscribeToVotingEvents();
        }
    }

    public void SubscribeToVotingEvents()
    {
        if (isSubscribedToEvents) return; // Zaten abone olmuşsak tekrar abone olma

        MinigameVotingManager.Instance.OnVotingStarted += OnVotingStarted;
        MinigameVotingManager.Instance.OnVotingEnded += OnVotingEnded;
        MinigameVotingManager.Instance.OnVotesUpdated += OnVotesUpdated;

        isSubscribedToEvents = true;
        Debug.Log("✅ [MinigameVotingUI] Event'lere başarıyla abone olundu!");
    }

    private void OnDestroy()
    {
        // Event'lerden ayrıl
        if (MinigameVotingManager.Instance != null && isSubscribedToEvents)
        {
            MinigameVotingManager.Instance.OnVotingStarted -= OnVotingStarted;
            MinigameVotingManager.Instance.OnVotingEnded -= OnVotingEnded;
            MinigameVotingManager.Instance.OnVotesUpdated -= OnVotesUpdated;
            isSubscribedToEvents = false;
            Debug.Log("🔄 [MinigameVotingUI] Event'lerden ayrıldı");
        }
    }



    public void OnVotingStarted()
    {
        Debug.Log("🎯 [MinigameVotingUI] OnVotingStarted çağrıldı!");

        // Voting panelini aç
        if (votingPanel != null)
        {
            Debug.Log($"✅ [MinigameVotingUI] Voting paneli açılıyor... Mevcut durum: {votingPanel.activeInHierarchy}");
            votingPanel.SetActive(true);
            Debug.Log($"🔄 [MinigameVotingUI] Voting paneli açıldı! Yeni durum: {votingPanel.activeInHierarchy}");

            // Panel'in parent'larının da aktif olduğundan emin ol
            Transform parent = votingPanel.transform.parent;
            while (parent != null)
            {
                if (!parent.gameObject.activeInHierarchy)
                {
                    Debug.Log($"🔧 [MinigameVotingUI] Parent aktif değil, aktif ediliyor: {parent.name}");
                    parent.gameObject.SetActive(true);
                }
                parent = parent.parent;
            }

            // Canvas'ın da aktif olduğundan emin ol
            Canvas canvas = votingPanel.GetComponentInParent<Canvas>();
            if (canvas != null && !canvas.gameObject.activeInHierarchy)
            {
                Debug.Log($"🔧 [MinigameVotingUI] Canvas aktif değil, aktif ediliyor: {canvas.name}");
                canvas.gameObject.SetActive(true);
            }
        }
        else
        {
            Debug.LogError("❌ [MinigameVotingUI] votingPanel null!");
        }

        // Kısa bir delay ile UI'ı başlat (server sync için)
        StartCoroutine(InitializeVotingUIDelayed());
    }

    /// <summary>
    /// Kısa delay ile voting UI'ını başlat
    /// </summary>
    private System.Collections.IEnumerator InitializeVotingUIDelayed()
    {
        // Server'dan minigame sync'i için kısa bekle
        yield return new WaitForSeconds(0.1f);

        // Voting UI'ını başlat
        InitializeVotingUI();
    }

    private void OnVotingEnded()
    {
        // Voting panelini kapat
        if (votingPanel != null)
        {
            votingPanel.SetActive(false);
        }
    }

    private void OnVotesUpdated(Dictionary<string, int> voteCounts, Dictionary<string, float> votePercentages)
    {
        // Buton yüzdelerini güncelle
        UpdateMinigameButtons(voteCounts, votePercentages);
    }

    private void InitializeVotingUI()
    {
        if (isInitialized) return;

        // Başlık metnini ayarla
        if (votingTitleText != null)
        {
            votingTitleText.text = votingTitle;
        }

        // Minigame butonlarını oluştur
        CreateMinigameButtons();

        isInitialized = true;
    }

    private void CreateMinigameButtons()
    {
        if (MinigameVotingManager.Instance == null)
        {
            return;
        }

        if (minigameButtonPrefab == null)
        {
            return;
        }

        if (minigameButtonsParent == null)
        {

            // Canvas'ta bir container oluştur
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas != null)
            {
                GameObject container = new GameObject("MinigameButtonContainer");
                container.transform.SetParent(canvas.transform, false);

                RectTransform containerRect = container.AddComponent<RectTransform>();
                containerRect.anchorMin = new Vector2(0.2f, 0.2f);
                containerRect.anchorMax = new Vector2(0.8f, 0.8f);
                containerRect.offsetMin = Vector2.zero;
                containerRect.offsetMax = Vector2.zero;

                minigameButtonsParent = container.transform;
            }
            else
            {
                // Son çare: kendi transform'unu kullan
                minigameButtonsParent = transform;
            }
        }

        // Mevcut butonları temizle
        foreach (var button in minigameButtons)
        {
            if (button != null && button.gameObject != null)
            {
                Destroy(button.gameObject);
            }
        }
        minigameButtons.Clear();

        // Yeni butonları oluştur
        var manager = MinigameVotingManager.Instance;

        for (int i = 0; i < manager.availableMinigames.Length; i++)
        {

            // ZORLA parent'a ekle - persistent sorunu için hack
            GameObject buttonObj = null;

            // Önce normal Canvas'ta oluştur
            Canvas normalCanvas = null;
            Canvas[] allCanvases = FindObjectsOfType<Canvas>();
            foreach (Canvas c in allCanvases)
            {
                // Persistent olmayan Canvas bul
                if (!c.transform.root.GetComponent<NetworkObject>())
                {
                    normalCanvas = c;
                    break;
                }
            }

            if (normalCanvas != null)
            {
                buttonObj = Instantiate(minigameButtonPrefab, normalCanvas.transform);

                // Şimdi parent'a taşı
                buttonObj.transform.SetParent(minigameButtonsParent, false);
            }
            else
            {
                // Son çare: direkt parent'a dene
                buttonObj = Instantiate(minigameButtonPrefab);
                buttonObj.transform.SetParent(minigameButtonsParent, false);
            }

            // Eğer hala parent yok ise, manuel olarak pozisyonu ayarla
            if (buttonObj.transform.parent == null)
            {
                // Parent'ın pozisyonunu al ve butonları ona göre yerleştir
                Vector3 parentPos = minigameButtonsParent.position;
                buttonObj.transform.position = parentPos + new Vector3(0, -i * 100, 0); // Dikey sıralama

                // RectTransform ayarları
                RectTransform buttonRect = buttonObj.GetComponent<RectTransform>();
                if (buttonRect != null)
                {
                    buttonRect.anchoredPosition = new Vector2(0, -i * 100);
                }
            }

            // Buton bilgilerini ayarla
            MinigameData minigameData = manager.availableMinigames[i];

            // MinigameCardComponent'i kontrol et
            MinigameCardComponent cardComponent = buttonObj.GetComponent<MinigameCardComponent>();
            MinigameVoteButton voteButton = buttonObj.GetComponent<MinigameVoteButton>();

            if (useMinigameCardComponent && cardComponent != null)
            {
                // MinigameVoteButton ekle
                if (voteButton == null)
                    voteButton = buttonObj.AddComponent<MinigameVoteButton>();

                // Referansları cardComponent'ten al
                voteButton.voteButton = cardComponent.mainButton;
                voteButton.minigameNameText = cardComponent.minigameNameText;
                voteButton.previewImage = cardComponent.previewImage;
                voteButton.backgroundImage = cardComponent.backgroundImage;

                // Kartı ayarla
                cardComponent.SetMinigameName(minigameData.displayName);
                cardComponent.SetPreviewSprite(minigameData.previewSprite);
                cardComponent.SetSelected(false);

                // Click event'ini ayarla
                int buttonIndex = i; // Closure için
                cardComponent.SetButtonClickAction(() => {
                    // MinigameVotingUI'a oy verme isteği gönder
                    VoteForMinigame(buttonIndex);
                });
            }
            else
            {
                // Eski yöntem - fallback
                if (voteButton == null)
                    voteButton = buttonObj.AddComponent<MinigameVoteButton>();

                voteButton.voteButton = buttonObj.GetComponent<UnityEngine.UI.Button>();
                if (voteButton.voteButton == null)
                    voteButton.voteButton = buttonObj.GetComponentInChildren<UnityEngine.UI.Button>();

                voteButton.minigameNameText = buttonObj.GetComponentInChildren<TMPro.TextMeshProUGUI>();
                voteButton.backgroundImage = buttonObj.GetComponent<UnityEngine.UI.Image>();
            }

            if (voteButton != null)
            {
                voteButton.Initialize(i, minigameData.minigameName, minigameData.displayName);

                // Preview image'i ayarla
                if (voteButton.previewImage != null)
                {
                    if (minigameData.previewSprite != null)
                    {
                        voteButton.previewImage.sprite = minigameData.previewSprite;
                    }
                    else
                    {
                        // Varsayılan sprite'ı temizle
                        voteButton.previewImage.sprite = null;
                        voteButton.previewImage.color = Color.clear; // Görünmez yap
                    }
                }
                // PreviewImage yoksa sorun değil, atlıyoruz
                minigameButtons.Add(voteButton);
            }
        }


    }

    private void UpdateMinigameButtons(Dictionary<string, int> voteCounts, Dictionary<string, float> votePercentages)
    {
        foreach (var button in minigameButtons)
        {
            if (button != null)
            {
                string minigameName = button.MinigameName;
                int voteCount = voteCounts.ContainsKey(minigameName) ? voteCounts[minigameName] : 0;
                float percentage = votePercentages.ContainsKey(minigameName) ? votePercentages[minigameName] : 0f;

                button.UpdateVoteInfo(voteCount, percentage);
            }
        }
    }

    private void UpdateVotingTimer()
    {
        if (votingTimerText != null && MinigameVotingManager.Instance != null)
        {
            float timeRemaining = MinigameVotingManager.Instance.VotingTimeRemaining;
            votingTimerText.text = string.Format(timerFormat, timeRemaining);

            // Timer rengini güncelle
            if (timeRemaining <= redTimerThreshold)
            {
                // Kırmızı renk (3 saniye ve altı)
                votingTimerText.color = redTimerColor;

                // Yanıp sönme efekti (opsiyonel)
                if (timeRemaining <= 1f)
                {
                    float alpha = Mathf.PingPong(Time.time * 3f, 1f); // Hızlı yanıp sönme
                    Color blinkColor = redTimerColor;
                    blinkColor.a = alpha;
                    votingTimerText.color = blinkColor;
                }
            }
            else
            {
                // Normal renk
                votingTimerText.color = normalTimerColor;
            }
        }
    }

    /// <summary>
    /// Oyuncu oy verdiğinde çağrılır
    /// </summary>
    public void OnPlayerVote(int minigameIndex)
    {
        if (MinigameVotingManager.Instance != null && NetworkManager.Singleton != null)
        {
            MinigameVotingManager.Instance.CastVoteServerRpc(minigameIndex);

            // Oyuncunun mevcut oyunu vurgula
            HighlightPlayerVote(minigameIndex);
        }
    }

    private void HighlightPlayerVote(int minigameIndex)
    {
        // Tüm butonların vurgusunu kaldır
        foreach (var button in minigameButtons)
        {
            if (button != null)
            {
                button.SetSelected(false);
            }
        }

        // Seçilen butonu vurgula
        if (minigameIndex >= 0 && minigameIndex < minigameButtons.Count)
        {
            minigameButtons[minigameIndex].SetSelected(true);
        }
    }

    /// <summary>
    /// Oyuncunun mevcut oyunu kontrol et ve vurgula
    /// </summary>
    public void CheckPlayerCurrentVote()
    {
        if (MinigameVotingManager.Instance != null && NetworkManager.Singleton != null)
        {
            ulong clientId = NetworkManager.Singleton.LocalClientId;
            int currentVote = MinigameVotingManager.Instance.GetPlayerVote(clientId);

            if (currentVote >= 0)
            {
                HighlightPlayerVote(currentVote);
            }
        }
    }

    /// <summary>
    /// Belirtilen minigame için oy ver
    /// </summary>
    public void VoteForMinigame(int minigameIndex)
    {
        if (MinigameVotingManager.Instance != null)
        {
            MinigameVotingManager.Instance.CastVoteServerRpc(minigameIndex);
        }
    }

    /// <summary>
    /// Voting panelini manuel olarak aç (debug için)
    /// </summary>
    [ContextMenu("Force Open Voting Panel")]
    public void ForceOpenVotingPanel()
    {
        if (votingPanel != null)
        {
            Debug.Log("🔧 [MinigameVotingUI] Manuel olarak voting paneli açılıyor...");
            votingPanel.SetActive(true);

            // Parent'ları da kontrol et
            Transform parent = votingPanel.transform.parent;
            while (parent != null)
            {
                if (!parent.gameObject.activeInHierarchy)
                {
                    Debug.Log($"🔧 [MinigameVotingUI] Parent aktif ediliyor: {parent.name}");
                    parent.gameObject.SetActive(true);
                }
                parent = parent.parent;
            }

            // UI'ı başlat
            if (!isInitialized)
            {
                InitializeVotingUI();
            }
        }
        else
        {
            Debug.LogError("❌ [MinigameVotingUI] votingPanel null!");
        }
    }

    /// <summary>
    /// Transform'un tam path'ini al
    /// </summary>
    private string GetFullPath(Transform transform)
    {
        string path = transform.name;
        Transform parent = transform.parent;
        while (parent != null)
        {
            path = parent.name + "/" + path;
            parent = parent.parent;
        }
        return path;
    }
}

/// <summary>
/// Tek bir minigame için oy verme butonu
/// </summary>
public class MinigameVoteButton : MonoBehaviour
{
    [Header("UI Referansları")]
    public Button voteButton;
    public TextMeshProUGUI minigameNameText;
    public Image previewImage; // Minigame önizleme resmi
    public Image backgroundImage;

    [Header("Görsel Ayarlar")]
    public Color normalColor = Color.white;
    public Color selectedColor = Color.green;

    // Buton bilgileri
    public int MinigameIndex { get; private set; }
    public string MinigameName { get; private set; }

    private bool isSelected = false;

    public void Initialize(int index, string minigameName, string displayName)
    {
        MinigameIndex = index;
        MinigameName = minigameName;

        // UI'ı ayarla
        if (minigameNameText != null)
        {
            minigameNameText.text = displayName;
        }

        // Preview image artık MinigameVotingUI'da ayarlanıyor

        // Buton event'ini ayarla
        if (voteButton != null)
        {
            voteButton.onClick.RemoveAllListeners();
            voteButton.onClick.AddListener(OnVoteButtonClicked);
        }

        // Başlangıç rengini ayarla
        SetSelected(false);
    }

    private void OnVoteButtonClicked()
    {
        // MinigameVotingUI'a oy verme isteği gönder
        MinigameVotingUI votingUI = FindObjectOfType<MinigameVotingUI>();
        if (votingUI != null)
        {
            votingUI.OnPlayerVote(MinigameIndex);
        }
    }

    public void UpdateVoteInfo(int voteCount, float percentage)
    {
        // Şimdilik yüzde gösterimini kaldırdık
        // Daha sonra güzel görsellikle eklenecek
        // Debug.Log($"🗳️ {MinigameName}: {voteCount} oy ({percentage:F1}%)");
    }

    public void SetSelected(bool selected)
    {
        isSelected = selected;

        if (backgroundImage != null)
        {
            backgroundImage.color = selected ? selectedColor : normalColor;
        }
    }
}
