using UnityEngine;

/// <summary>
/// Oyuncu spawn noktası işaretleyicisi
/// Bu component'li objeler otomatik olarak spawn noktası olarak algılanır
/// </summary>
public class SpawnPoint : MonoBehaviour
{
    [Header("Spawn Point Ayarları")]
    [Tooltip("Bu spawn noktasının <PERSON>ğ<PERSON> (yüksek öncelik önce kullanılır)")]
    public int priority = 0;

    [Tooltip("Bu spawn noktası aktif mi?")]
    public bool isActive = true;

    [Tooltip("Maksimum kaç oyuncu bu noktada spawn olabilir")]
    public int maxPlayers = 1;

    [Header("Görselleştirme")]
    public bool showGizmo = true;
    public Color gizmoColor = Color.green;
    public float gizmoSize = 1f;

    private int currentPlayerCount = 0;

    public bool CanSpawn()
    {
        return isActive && currentPlayerCount < maxPlayers;
    }

    public void OnPlayerSpawned()
    {
        currentPlayerCount++;
    }

    public void OnPlayerDespawned()
    {
        currentPlayerCount = Mathf.Max(0, currentPlayerCount - 1);
    }

    private void OnDrawGizmos()
    {
        if (!showGizmo) return;

        Gizmos.color = isActive ? gizmoColor : Color.red;

        // Spawn noktası göster
        Gizmos.DrawWireSphere(transform.position, gizmoSize * 0.5f);
        Gizmos.DrawLine(transform.position, transform.position + Vector3.up * gizmoSize);

        // Yön göster
        Gizmos.color = Color.blue;
        Vector3 forward = transform.forward * gizmoSize;
        Gizmos.DrawLine(transform.position, transform.position + forward);

        #if UNITY_EDITOR
        // Bilgi göster (sadece editor'da)
        if (Application.isEditor)
        {
            string info = $"Spawn Point\nPriority: {priority}\nPlayers: {currentPlayerCount}/{maxPlayers}";
            UnityEditor.Handles.Label(transform.position + Vector3.up * (gizmoSize + 0.5f), info);
        }
        #endif
    }
}
