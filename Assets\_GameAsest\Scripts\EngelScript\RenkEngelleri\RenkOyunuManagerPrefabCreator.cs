using UnityEngine;
using Unity.Netcode;
#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// RenkOyunu sahnesinde gerekli manager'ları otomatik oluşturan yardımcı script
/// Bu scripti bir kere çalıştırıp prefab oluşturmak için kullanın
/// </summary>
public class RenkOyunuManagerPrefabCreator : MonoBehaviour
{
    [ContextMenu("RenkOyunu Manager'larını Oluştur")]
    public void CreateRenkOyunuManagers()
    {
        // 1. Setup Manager oluştur
        GameObject setupManagerObj = new GameObject("RenkOyunu Setup Manager");
        setupManagerObj.AddComponent<NetworkObject>();
        setupManagerObj.AddComponent<RenkOyunuSetupManager>();
        
        // Setup Manager ayarları
        RenkOyunuSetupManager setupManager = setupManagerObj.GetComponent<RenkOyunuSetupManager>();
        setupManager.autoSetupOnStart = true;
        setupManager.createMissingComponents = true;
        setupManager.showDebugLogs = true;
        
        // 2. Game Starter oluştur
        GameObject gameStarterObj = new GameObject("RenkOyunu Game Starter");
        gameStarterObj.AddComponent<NetworkObject>();
        gameStarterObj.AddComponent<RenkOyunuGameStarter>();
        
        // Game Starter ayarları
        RenkOyunuGameStarter gameStarter = gameStarterObj.GetComponent<RenkOyunuGameStarter>();
        gameStarter.minimumOyuncuSayisi = 1;
        gameStarter.oyuncuBeklemeZamani = 5f;
        gameStarter.otomatikBaslat = true;
        gameStarter.showDebugLogs = true;
        
        Debug.Log("✅ RenkOyunu Manager'ları oluşturuldu!");
        Debug.Log("📝 Şimdi bu objeleri prefab olarak kaydedin ve RenkOyunu sahnesine ekleyin.");

        // Selection'ı yeni oluşturulan objelere ayarla (sadece Editor'da)
#if UNITY_EDITOR
        UnityEditor.Selection.objects = new Object[] { setupManagerObj, gameStarterObj };
#endif
    }
    
    [ContextMenu("Sahne Durumunu Kontrol Et")]
    public void CheckSceneStatus()
    {
        Debug.Log("=== RENK OYUNU SAHNESİ DURUM KONTROLÜ ===");
        
        // Manager'ları kontrol et
        RenkOyunuSetupManager setupManager = FindFirstObjectByType<RenkOyunuSetupManager>();
        Debug.Log($"RenkOyunuSetupManager: {(setupManager != null ? "✅ Mevcut" : "❌ Eksik")}");
        
        RenkOyunuGameStarter gameStarter = FindFirstObjectByType<RenkOyunuGameStarter>();
        Debug.Log($"RenkOyunuGameStarter: {(gameStarter != null ? "✅ Mevcut" : "❌ Eksik")}");
        
        RenkOyunuManager renkManager = FindFirstObjectByType<RenkOyunuManager>();
        Debug.Log($"RenkOyunuManager: {(renkManager != null ? "✅ Mevcut" : "❌ Eksik")}");
        
        // Diğer bileşenleri kontrol et
        InstantHybridCameraController camera = FindFirstObjectByType<InstantHybridCameraController>();
        Debug.Log($"InstantHybridCameraController: {(camera != null ? "✅ Mevcut" : "❌ Eksik")}");
        
        FloatingJoystick joystick = FindFirstObjectByType<FloatingJoystick>();
        Debug.Log($"FloatingJoystick: {(joystick != null ? "✅ Mevcut" : "❌ Eksik")}");
        
        ArayuzYoneticisi ui = FindFirstObjectByType<ArayuzYoneticisi>();
        Debug.Log($"ArayuzYoneticisi: {(ui != null ? "✅ Mevcut" : "❌ Eksik")}");
        
        Canvas canvas = FindFirstObjectByType<Canvas>();
        Debug.Log($"Canvas: {(canvas != null ? "✅ Mevcut" : "❌ Eksik")}");
        
        // Network bileşenleri
        Unity.Netcode.NetworkManager netManager = FindFirstObjectByType<Unity.Netcode.NetworkManager>();
        Debug.Log($"NetworkManager: {(netManager != null ? "✅ Mevcut" : "❌ Eksik")}");
        
        CustomNetworkManager customNetManager = FindFirstObjectByType<CustomNetworkManager>();
        Debug.Log($"CustomNetworkManager: {(customNetManager != null ? "✅ Mevcut" : "❌ Eksik")}");
        
        Debug.Log("=== KONTROL TAMAMLANDI ===");
        
        // Eksik olanları listele
        if (setupManager == null || gameStarter == null)
        {
            Debug.LogWarning("⚠️ Eksik manager'lar var! 'RenkOyunu Manager'larını Oluştur' butonuna basın.");
        }
        
        if (renkManager == null)
        {
            Debug.LogError("❌ RenkOyunuManager eksik! Bu sahneye RenkOyunuManager prefab'ını ekleyin.");
        }
    }
}
