using UnityEngine;

public class DonenKolVurusu : MonoBehaviour
{
    // YENİ: Hangi tür fırlatma istediğimizi seçeceğimiz bir menü oluşturuyoruz.
    public enum FirlatmaTuru
    {
        HareketYonu, // Kolun gittiği yöne doğru ittirir
        VurusYonu,  // Çarpma noktasından dışarı doğru ittirir
        Hibrid      // İkisinin karışımı
    }

    [Header("Fırlatma Tipi")]
    [Tooltip("Oyuncunun nasıl fırlatılacağını seçin.")]
    public FirlatmaTuru firlatmaTipi = FirlatmaTuru.Hibrid;

    [Header("Referanslar")]
    [Tooltip("Tüm mekanizmanın döndüğü ana pivot obje.")]
    public Transform donmeMerkezi;

    [Header("Darbe Ayarları")]
    [Tooltip("Kolun oyuncuya uygulayacağı ana itme kuvveti.")]
    public float vurmaGucu = 120f;
    
    [Tooltip("Oyuncuyu yerden 'kurtarmak' için uygulanacak dikey itme kuvveti.")]
    [Range(0f, 1f)]
    public float yukariItmeOrani = 0.3f;

    [Tooltip("Ragdoll'un minimum ne kadar süre aktif kalacağı.")]
    public float ragdollSuresi = 2.5f;

    private DonenKolController _donenKolController;

    void Start()
    {
        if (donmeMerkezi != null)
        {
            _donenKolController = donmeMerkezi.GetComponent<DonenKolController>();
        }
        else
        {
            Debug.LogError("Donme Merkezi referansı atanmamış!", this.gameObject);
        }
    }

    private void OnTriggerEnter(Collider other)
    {
        if (!other.CompareTag("Player")) return;
        
        RagdollManager ragdollManager = other.GetComponentInParent<RagdollManager>();

        if (ragdollManager == null || _donenKolController == null) return;
        if (ragdollManager.IsRagdollActive) return;

        // --- YENİ: SEÇİME GÖRE YÖN HESAPLAMA ---
        Vector3 sonYon = Vector3.zero;

        // switch-case yapısı ile Inspector'dan seçtiğimiz fırlatma tipine göre farklı kodlar çalıştırıyoruz.
        switch (firlatmaTipi)
        {
            case FirlatmaTuru.HareketYonu:
                Vector3 hareketYonu = HesaplaHareketYonu(other);
                if (hareketYonu.magnitude < 0.1f) return;
                sonYon = hareketYonu.normalized;
                break;

            case FirlatmaTuru.VurusYonu:
                // "Vuruş yönü", bu objenin merkezinden oyuncunun merkezine doğru olan yöndür.
                Vector3 vurusYonu = other.transform.position - transform.position;
                sonYon = vurusYonu.normalized;
                break;

            case FirlatmaTuru.Hibrid:
                Vector3 hareket = HesaplaHareketYonu(other);
                if (hareket.magnitude < 0.1f) return;
                Vector3 vurus = other.transform.position - transform.position;
                // İki yönü birleştirip ortalamasını alarak daha doğal bir his elde ediyoruz.
                sonYon = (hareket.normalized + vurus.normalized).normalized;
                break;
        }

        // Hesaplanan son yöne dikey itme kuvvetini ekle
        sonYon.y += yukariItmeOrani;
        
        // Son kuvveti hesapla ve uygula
        Vector3 kuvvet = sonYon.normalized * vurmaGucu;
        ragdollManager.DarbelereKarsiRagdollAktifEt(kuvvet, Vector3.zero, ragdollSuresi);
    }

    // Hareket yönünü hesaplama işini ayrı bir fonksiyona taşıyarak kodu daha temiz hale getirdik.
    private Vector3 HesaplaHareketYonu(Collider other)
    {
        float donusHizi = _donenKolController.donusHizi;
        Vector3 donusEkseni = _donenKolController.donusEkseni;
        
        Vector3 acisalHiz = donusEkseni.normalized * donusHizi * Mathf.Deg2Rad;
        Vector3 yariCapVektoru = other.transform.position - donmeMerkezi.position;
        
        return Vector3.Cross(acisalHiz, yariCapVektoru);
    }
}