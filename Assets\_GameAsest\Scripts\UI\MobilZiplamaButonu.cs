using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

/// <summary>
/// Mobil cihazlar için özel olarak tasarlanmış zıplama butonu
/// Hem Button onClick hem de dokunmatik eventleri yakalar
/// </summary>
public class MobilZiplamaButonu : MonoBehaviour, IPointerDownHandler, IPointerUpHandler
{
    [Header("Debug")]
    [Toolt<PERSON>("Debug mesajlarını göster")]
    public bool debugMesajlari = true;

    private Button button;
    private bool butonBasili = false;

    void Start()
    {
        // Button component'ini al
        button = GetComponent<Button>();
        
        if (button != null)
        {
            // Normal button onClick event'ini de bağla
            button.onClick.AddListener(OnButtonClick);
            if (debugMesajlari) Debug.Log("🎮 MobilZiplamaButonu: Button onClick event'i bağlandı");
        }
        else
        {
            Debug.LogError("❌ MobilZiplamaButonu: Button component bulunamadı!");
        }
        
        if (debugMesajlari) Debug.Log("🎮 MobilZiplamaButonu başlatıldı");
    }

    /// <summary>
    /// Normal Button onClick event'i
    /// </summary>
    private void OnButtonClick()
    {
        if (debugMesajlari) Debug.Log("🎮 Button onClick tetiklendi");
        ZiplamaYap();
    }

    /// <summary>
    /// Dokunmatik: Parmak butona değdiğinde
    /// </summary>
    public void OnPointerDown(PointerEventData eventData)
    {
        if (debugMesajlari) Debug.Log("👆 OnPointerDown tetiklendi");
        butonBasili = true;
        ZiplamaYap();
    }

    /// <summary>
    /// Dokunmatik: Parmak butondan kalktığında
    /// </summary>
    public void OnPointerUp(PointerEventData eventData)
    {
        if (debugMesajlari) Debug.Log("👆 OnPointerUp tetiklendi");
        butonBasili = false;
    }

    /// <summary>
    /// Ana zıplama fonksiyonu
    /// </summary>
    private void ZiplamaYap()
    {
        if (debugMesajlari) Debug.Log("🚀 ZiplamaYap çağrıldı");

        // Önce LocalInstance'ı dene
        if (OyuncuKontrol.LocalInstance != null)
        {
            if (debugMesajlari) Debug.Log("✅ LocalInstance bulundu");
            OyuncuKontrol.LocalInstance.Jump();
            return;
        }

        if (debugMesajlari) Debug.Log("⚠️ LocalInstance null, alternatif yöntem deneniyor...");

        // Alternatif: Tüm oyuncuları ara
        OyuncuKontrol[] allPlayers = FindObjectsByType<OyuncuKontrol>(FindObjectsSortMode.None);
        if (debugMesajlari) Debug.Log($"🔍 Bulunan oyuncu sayısı: {allPlayers.Length}");

        foreach (var player in allPlayers)
        {
            if (player.IsOwner)
            {
                if (debugMesajlari) Debug.Log("✅ Owner oyuncu bulundu");
                player.Jump();
                return;
            }
        }

        Debug.LogError("❌ Hiçbir owner oyuncu bulunamadı!");
    }

    /// <summary>
    /// Test için manuel zıplama
    /// </summary>
    [ContextMenu("Test Zıplama")]
    public void TestZiplama()
    {
        Debug.Log("🧪 Test zıplama başlatıldı");
        ZiplamaYap();
    }

    /// <summary>
    /// Component destroy edildiğinde temizlik
    /// </summary>
    private void OnDestroy()
    {
        if (button != null)
        {
            button.onClick.RemoveListener(OnButtonClick);
        }
    }

    /// <summary>
    /// Debug bilgilerini göster
    /// </summary>
    void Update()
    {
        // Sadece debug aktifse ve buton basılıysa
        if (debugMesajlari && butonBasili && Time.frameCount % 30 == 0) // Her 30 frame'de bir
        {
            Debug.Log($"🎮 Buton durumu: Basılı, LocalInstance: {(OyuncuKontrol.LocalInstance != null ? "VAR" : "YOK")}");
        }
    }
}
