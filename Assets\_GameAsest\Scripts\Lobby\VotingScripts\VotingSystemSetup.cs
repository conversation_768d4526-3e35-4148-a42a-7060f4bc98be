using UnityEngine;
using Unity.Netcode;

/// <summary>
/// Lobby sahnesinde voting sistemini otomatik olarak kuran setup scripti.
/// Bu script'i Lobby sahnesine ekleyin, gerekli componentleri otomatik oluşturacak.
/// </summary>
public class VotingSystemSetup : MonoBehaviour
{
    [Header("Otomatik Kurulum")]
    [Tooltip("Voting sistemini otomatik olarak kur")]
    public bool autoSetup = true;

    [Header("Voting Ayarları")]
    [Tooltip("Voting süresi (saniye)")]
    public float votingDuration = 30f;

    [Tooltip("Oylanacak minigame'lerin listesi")]
    public MinigameData[] availableMinigames = {
        new MinigameData { minigameName = "RenkOyunu", displayName = "Renk Oyunu", previewSprite = null },
        new MinigameData { minigameName = "fakeblock_Scene", displayName = "Fake Block", previewSprite = null }
    };

    [Header("UI Ayarları")]
    [Tooltip("Voting UI'ının parent'ı (Canvas)")]
    public Transform uiParent;

    [Tooltip("Voting paneli prefab'ı (opsiyonel)")]
    public GameObject votingPanelPrefab;

    private void Awake()
    {
        if (autoSetup)
        {
            SetupVotingSystem();
        }
    }

    private void Start()
    {
        // Voting sisteminin düzgün çalıştığından emin olmak için kontrol et
        StartCoroutine(CheckVotingSystemHealth());
    }

    private System.Collections.IEnumerator CheckVotingSystemHealth()
    {
        yield return new WaitForSeconds(1f); // Sistemin başlaması için bekle

        // MinigameVotingManager var mı kontrol et
        var votingManager = FindObjectOfType<MinigameVotingManager>();
        if (votingManager == null)
        {
            Debug.LogWarning("⚠️ [VotingSystemSetup] MinigameVotingManager bulunamadı!");
        }

        // MinigameVotingUI var mı kontrol et
        var votingUI = FindObjectOfType<MinigameVotingUI>();
        if (votingUI == null)
        {
            Debug.LogWarning("⚠️ [VotingSystemSetup] MinigameVotingUI bulunamadı!");
        }
        else if (votingUI.votingPanel == null)
        {
            Debug.LogWarning("⚠️ [VotingSystemSetup] MinigameVotingUI'da votingPanel null!");
        }
    }

    [ContextMenu("Manuel Kurulum")]
    public void SetupVotingSystem()
    {
        // MinigameVotingManager'ı kur
        SetupVotingManager();

        // UI sistemini kur
        SetupVotingUI();
    }

    private void SetupVotingManager()
    {
        // Mevcut MinigameVotingManager var mı kontrol et
        MinigameVotingManager existingManager = FindObjectOfType<MinigameVotingManager>();
        
        if (existingManager != null)
        {
            // Mevcut manager'ın ayarlarını güncelle
            existingManager.votingDuration = votingDuration;
            existingManager.availableMinigames = availableMinigames;
            return;
        }

        // Yeni MinigameVotingManager oluştur
        GameObject managerObj = new GameObject("MinigameVotingManager");
        
        // NetworkObject ekle
        NetworkObject networkObject = managerObj.AddComponent<NetworkObject>();
        networkObject.DontDestroyWithOwner = true;
        
        // MinigameVotingManager ekle
        MinigameVotingManager manager = managerObj.AddComponent<MinigameVotingManager>();
        
        // Ayarları yap
        manager.votingDuration = votingDuration;
        manager.availableMinigames = availableMinigames;
    }

    private void SetupVotingUI()
    {
        // UI parent'ı bul
        Transform targetParent = uiParent;
        if (targetParent == null)
        {
            // Canvas bul - tüm Canvas'ları kontrol et
            Canvas[] allCanvases = FindObjectsOfType<Canvas>();
            Canvas canvas = null;
            
            // Aktif olan Canvas'ı bul
            foreach (Canvas c in allCanvases)
            {
                if (c.gameObject.activeInHierarchy)
                {
                    canvas = c;
                    break;
                }
            }
            
            if (canvas != null)
            {
                targetParent = canvas.transform;
                Debug.Log("✅ [VotingSystemSetup] Canvas bulundu: " + canvas.name);
            }
            else
            {
                Debug.LogError("❌ [VotingSystemSetup] Aktif Canvas bulunamadı!");
                
                // Canvas listesini debug için yazdır
                Debug.Log($"🔍 Toplam Canvas sayısı: {allCanvases.Length}");
                for (int i = 0; i < allCanvases.Length; i++)
                {
                    Debug.Log($"Canvas {i}: {allCanvases[i].name} - Active: {allCanvases[i].gameObject.activeInHierarchy}");
                }
                return;
            }
        }

        // Mevcut MinigameVotingUI var mı kontrol et
        MinigameVotingUI existingUI = FindObjectOfType<MinigameVotingUI>();
        if (existingUI != null)
        {
            Debug.Log("✅ [VotingSystemSetup] MinigameVotingUI zaten var");
            
            // Referansları kontrol et ve eksikse düzelt
            if (existingUI.votingPanel == null)
            {
                Debug.LogWarning("⚠️ [VotingSystemSetup] Mevcut UI'da votingPanel null, düzeltiliyor...");
                existingUI.votingPanel = existingUI.gameObject;
            }
            return;
        }

        Debug.Log("🔧 [VotingSystemSetup] MinigameVotingUI oluşturuluyor...");

        // Voting UI oluştur
        GameObject uiObj;

        if (votingPanelPrefab != null)
        {
            // Prefab varsa onu kullan
            uiObj = Instantiate(votingPanelPrefab, targetParent);
            Debug.Log("✅ [VotingSystemSetup] Prefab'dan UI oluşturuldu");
        }
        else
        {
            // Basit UI oluştur
            uiObj = CreateSimpleVotingUI(targetParent);
            Debug.Log("✅ [VotingSystemSetup] Basit UI oluşturuldu");
        }

        // MinigameVotingUI component'ini ekle (CreateSimpleVotingUI'da zaten ekleniyor ama güvenlik için)
        MinigameVotingUI votingUI = uiObj.GetComponent<MinigameVotingUI>();
        if (votingUI == null)
        {
            votingUI = uiObj.AddComponent<MinigameVotingUI>();
        }
        
        // Referansları kontrol et
        if (votingUI.votingPanel == null)
        {
            votingUI.votingPanel = uiObj;
            Debug.Log("🔧 [VotingSystemSetup] votingPanel referansı düzeltildi");
        }
        
        Debug.Log("✅ [VotingSystemSetup] MinigameVotingUI başarıyla oluşturuldu!");

        // --- EKLENDİ: Event subscription ve panel açma (SendMessage yerine doğrudan çağrı) ---
        var votingManager = MinigameVotingManager.Instance;
        if (votingManager != null && votingUI != null)
        {
            votingUI.SubscribeToVotingEvents();
            if (votingManager.IsVotingActive)
            {
                votingUI.OnVotingStarted();
            }
        }
        // --- SON EK ---
    }

    private GameObject CreateSimpleVotingUI(Transform parent)
    {
        // Ana image (panel yerine)
        GameObject votingImage = new GameObject("VotingImage");
        votingImage.transform.SetParent(parent, false);

        // RectTransform ekle
        RectTransform imageRect = votingImage.AddComponent<RectTransform>();
        imageRect.anchorMin = Vector2.zero;
        imageRect.anchorMax = Vector2.one;
        imageRect.offsetMin = Vector2.zero;
        imageRect.offsetMax = Vector2.zero;

        // Image background
        UnityEngine.UI.Image backgroundImage = votingImage.AddComponent<UnityEngine.UI.Image>();
        backgroundImage.color = new Color(0, 0, 0, 0.8f); // Yarı saydam siyah

        // Başlık
        GameObject titleObj = new GameObject("Title");
        titleObj.transform.SetParent(votingImage.transform, false);
        
        RectTransform titleRect = titleObj.AddComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0, 0.8f);
        titleRect.anchorMax = new Vector2(1, 1f);
        titleRect.offsetMin = Vector2.zero;
        titleRect.offsetMax = Vector2.zero;

        TMPro.TextMeshProUGUI titleText = titleObj.AddComponent<TMPro.TextMeshProUGUI>();
        titleText.text = "Hangi Oyunu Oynamak İstiyorsun?";
        titleText.fontSize = 24;
        titleText.alignment = TMPro.TextAlignmentOptions.Center;
        titleText.color = Color.white;

        // Timer
        GameObject timerObj = new GameObject("Timer");
        timerObj.transform.SetParent(votingImage.transform, false);
        
        RectTransform timerRect = timerObj.AddComponent<RectTransform>();
        timerRect.anchorMin = new Vector2(0, 0.7f);
        timerRect.anchorMax = new Vector2(1, 0.8f);
        timerRect.offsetMin = Vector2.zero;
        timerRect.offsetMax = Vector2.zero;

        TMPro.TextMeshProUGUI timerText = timerObj.AddComponent<TMPro.TextMeshProUGUI>();
        timerText.text = "Kalan Süre: 30s";
        timerText.fontSize = 18;
        timerText.alignment = TMPro.TextAlignmentOptions.Center;
        timerText.color = Color.yellow;

        // Buton container
        GameObject buttonContainer = new GameObject("ButtonContainer");
        buttonContainer.transform.SetParent(votingImage.transform, false);
        
        RectTransform containerRect = buttonContainer.AddComponent<RectTransform>();
        containerRect.anchorMin = new Vector2(0.1f, 0.2f);
        containerRect.anchorMax = new Vector2(0.9f, 0.7f);
        containerRect.offsetMin = Vector2.zero;
        containerRect.offsetMax = Vector2.zero;

        // Vertical Layout Group
        UnityEngine.UI.VerticalLayoutGroup layoutGroup = buttonContainer.AddComponent<UnityEngine.UI.VerticalLayoutGroup>();
        layoutGroup.spacing = 10;
        layoutGroup.childAlignment = TextAnchor.MiddleCenter;
        layoutGroup.childControlHeight = true;
        layoutGroup.childControlWidth = true;

        // MinigameVotingUI component'ini ayarla
        MinigameVotingUI votingUI = votingImage.AddComponent<MinigameVotingUI>();
        votingUI.votingPanel = votingImage;
        votingUI.votingTitleText = titleText;
        votingUI.votingTimerText = timerText;
        votingUI.minigameButtonsParent = buttonContainer.transform;

        // Basit buton prefab'ı oluştur
        GameObject buttonPrefab = CreateSimpleButtonPrefab();
        votingUI.minigameButtonPrefab = buttonPrefab;

        // Image'i başlangıçta gizle
        votingImage.SetActive(false);

        return votingImage;
    }

    private GameObject CreateSimpleButtonPrefab()
    {
        // Buton prefab'ı oluştur
        GameObject buttonObj = new GameObject("MinigameVoteButton");
        
        RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(300, 60);

        // Button component
        UnityEngine.UI.Button button = buttonObj.AddComponent<UnityEngine.UI.Button>();
        
        // Button background
        UnityEngine.UI.Image buttonImage = buttonObj.AddComponent<UnityEngine.UI.Image>();
        buttonImage.color = Color.white;

        // Button text
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(buttonObj.transform, false);
        
        RectTransform textRect = textObj.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;

        TMPro.TextMeshProUGUI buttonText = textObj.AddComponent<TMPro.TextMeshProUGUI>();
        buttonText.text = "Minigame";
        buttonText.fontSize = 16;
        buttonText.alignment = TMPro.TextAlignmentOptions.Center;
        buttonText.color = Color.black;

        // Vote info text
        GameObject voteInfoObj = new GameObject("VoteInfo");
        voteInfoObj.transform.SetParent(buttonObj.transform, false);
        
        RectTransform voteInfoRect = voteInfoObj.AddComponent<RectTransform>();
        voteInfoRect.anchorMin = new Vector2(0, 0);
        voteInfoRect.anchorMax = new Vector2(1, 0.3f);
        voteInfoRect.offsetMin = Vector2.zero;
        voteInfoRect.offsetMax = Vector2.zero;

        TMPro.TextMeshProUGUI voteInfoText = voteInfoObj.AddComponent<TMPro.TextMeshProUGUI>();
        voteInfoText.text = "0 oy (0%)";
        voteInfoText.fontSize = 12;
        voteInfoText.alignment = TMPro.TextAlignmentOptions.Center;
        voteInfoText.color = Color.gray;

        // MinigameVoteButton component
        MinigameVoteButton voteButton = buttonObj.AddComponent<MinigameVoteButton>();
        voteButton.voteButton = button;
        voteButton.minigameNameText = buttonText;
        // voteInfoText kaldırıldı
        voteButton.backgroundImage = buttonImage;

        return buttonObj;
    }
}
