using UnityEngine;

public class ZıplatıcıController : MonoBehaviour
{
    [<PERSON><PERSON>("Zıplatma Ayarları")]
    [Tooltip("Oyuncuya verilecek dikey hız. Bu değer ne kadar yüks<PERSON>, o kadar yükseğe zıplar.")]
    public float zıplatmaHizi = 15f;

    [Header("Görsel Efekt")]
    [Tooltip("Zıplatma anında oluşacak parçacık efekti (isteğe bağlı).")]
    public GameObject zıplatmaEfekti;


    private void OnTriggerEnter(Collider other)
    {
        // İlk filtre olarak hala "Player" etiketini kontrol etmek iyi bir pratiktir.
        // Botlarına da "Player" etiketi vereceksin.
        if (!other.CompareTag("Player")) return;
        
        // --- DEĞİŞİKLİK BURADA BAŞLIYOR ---

        // Artık "OyuncuKontrol" yerine, bizim standardımız olan "IEtkilesimeGirebilir" arıyoruz.
        IEtkilesimeGirebilir etkilesenObje = other.GetComponentInParent<IEtkilesimeGirebilir>();

        // Eğer çarptığımız obje bu standarda (fişe) sahipse...
        if (etkilesenObje != null)
        {
            // Komutu, bulduğumuz yeni genel arayüz değişkeni üzerinden veriyoruz.
            etkilesenObje.ZıplatmaYap(zıplatmaHizi);

            // Görsel efekt kısmı aynı kalabilir.
            if (zıplatmaEfekti != null)
            {
                Instantiate(zıplatmaEfekti, transform.position, Quaternion.identity);
            }
        }
    }

    // Bu kısım görselleştirme için, aynı kalabilir.
    private void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.cyan;
        Gizmos.DrawRay(transform.position, Vector3.up * zıplatmaHizi * 0.5f);
    }
}