using UnityEngine;
using UnityEngine.EventSystems; // Dokunma olayları için bu kütüphane şart!

public class DinamikJoystickTetikleyici : <PERSON>o<PERSON><PERSON><PERSON>our, IPointerDownHandler, IPointerUpHandler
{
    // Inspector'dan kendi joystick nesnenizi buraya sürükleyeceksiniz.
    [Tooltip("Kontrol edilecek joystick nesnesini buraya sürükleyin")]
    public GameObject mevcutJoystick;

    // Bu alana (görünmez panelimize) basıldığı anda çalışır
    public void OnPointerDown(PointerEventData eventData)
    {
        // Joystick'in merkezini, parmağımızı bastığımız yere ayarla
        mevcutJoystick.transform.position = eventData.position;
        
        // Joystick'i görünür (aktif) yap
        mevcutJoystick.SetActive(true);
    }

    // Parmağımızı bu alandan kaldırdığımız anda çalışır
    public void OnPointerUp(PointerEventData eventData)
    {
        // Joystick'i tekrar gizle (deaktif et)
        mevcutJoystick.SetActive(false);

        // ÖNEMLİ NOT: Eğer joystick'iniz parmağınızı çekince sıfırlanmıyorsa,
        // joystick'inizin script'indeki sıfırlama (reset) fonksiyonunu burada çağırmanız gerekebilir.
        // Örneğin: mevcutJoystick.GetComponent<FloatingJoystick>().ResetHandle(); gibi.
        // Bu, kullandığınız joystick asset'ine göre değişir.
    }
}