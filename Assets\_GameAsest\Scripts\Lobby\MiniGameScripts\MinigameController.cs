using UnityEngine;
using Unity.Netcode;

/// <summary>
/// Her minigame scene'inde bulunması gereken controller
/// Minigame bittiğinde otomatik döngüyü tetikler
/// </summary>
public class MinigameController : NetworkBehaviour
{
    [Header("Minigame Ayarları")]
    [Tooltip("Minigame süresi (saniye) - 0 ise sınırsız")]
    public float minigameDuration = 120f; // 2 dakika
    
    [<PERSON>lt<PERSON>("Minigame otomatik bitsin mi?")]
    public bool autoEndMinigame = true;

    [Header("UI Referansları")]
    [Tooltip("Minigame bittiğinde gösterilecek sonuç paneli")]
    public GameObject resultPanel;
    
    [Tooltip("Sonuç text'i")]
    public TMPro.TextMeshProUGUI resultText;

    // Network Variables
    private NetworkVariable<bool> isMinigameActive = new NetworkVariable<bool>(false,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);

    private NetworkVariable<float> timeRemaining = new NetworkVariable<float>(0f,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);

    // Events
    public System.Action OnMinigameStarted;
    public System.Action OnMinigameEnded;
    public System.Action<float> OnTimeUpdated;

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();

        if (IsServer)
        {
            // Minigame'i başlat
            StartMinigame();
        }

        // Network variable event'lerini dinle
        isMinigameActive.OnValueChanged += OnMinigameActiveChanged;
        timeRemaining.OnValueChanged += OnTimeRemainingChanged;
    }

    /// <summary>
    /// Minigame'i başlat (sadece server)
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    public void StartMinigameServerRpc()
    {
        if (!IsServer) return;
        StartMinigame();
    }

    private void StartMinigame()
    {
        if (!IsServer) return;

        Debug.Log("🎮 Minigame başlatılıyor...");
        
        isMinigameActive.Value = true;
        timeRemaining.Value = minigameDuration;

        // Timer başlat (eğer süre sınırı varsa)
        if (autoEndMinigame && minigameDuration > 0)
        {
            StartCoroutine(MinigameTimer());
        }
    }

    /// <summary>
    /// Minigame timer'ı
    /// </summary>
    private System.Collections.IEnumerator MinigameTimer()
    {
        while (timeRemaining.Value > 0 && isMinigameActive.Value)
        {
            yield return new WaitForSeconds(1f);
            
            if (IsServer)
            {
                timeRemaining.Value = Mathf.Max(0, timeRemaining.Value - 1f);
            }
        }

        // Süre doldu, minigame'i bitir
        if (IsServer && isMinigameActive.Value)
        {
            EndMinigame("Süre doldu!");
        }
    }

    /// <summary>
    /// Minigame'i bitir (sadece server)
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    public void EndMinigameServerRpc(string result = "Minigame tamamlandı!")
    {
        if (!IsServer) return;
        EndMinigame(result);
    }

    private void EndMinigame(string result)
    {
        if (!IsServer || !isMinigameActive.Value) return;

        Debug.Log($"🏁 Minigame bitiyor: {result}");

        isMinigameActive.Value = false;
        timeRemaining.Value = 0f;

        // Sonucu tüm client'lara gönder
        ShowResultClientRpc(result);

        // Lobby'ye dönüş için delay başlat
        StartCoroutine(ReturnToLobbyAfterDelay());
    }

    /// <summary>
    /// Sonucu göster (tüm client'lar)
    /// </summary>
    [ClientRpc]
    private void ShowResultClientRpc(string result)
    {
        Debug.Log($"📊 Minigame sonucu: {result}");

        if (resultPanel != null)
        {
            resultPanel.SetActive(true);
        }

        if (resultText != null)
        {
            resultText.text = result;
        }
    }

    /// <summary>
    /// Belirtilen süre sonra lobby'ye dön
    /// </summary>
    private System.Collections.IEnumerator ReturnToLobbyAfterDelay()
    {
        // Sonuç gösterme süresi kadar bekle
        float waitTime = 5f; // 5 saniye sonuç göster
        Debug.Log($"⏱️ {waitTime} saniye sonra lobby'ye dönülecek...");

        yield return new WaitForSeconds(waitTime);

        // Lobby'ye dön
        ReturnToLobbyClientRpc();
    }

    /// <summary>
    /// Tüm client'ları lobby'ye döndür
    /// </summary>
    [ClientRpc]
    private void ReturnToLobbyClientRpc()
    {
        Debug.Log("🏠 Lobby'ye dönülüyor...");

        // Scene değiştir
        if (NetworkManager.Singleton != null)
        {
            // Eğer NetworkSceneManager varsa onu kullan
            if (NetworkManager.Singleton.SceneManager != null)
            {
                NetworkManager.Singleton.SceneManager.LoadScene("Lobby", UnityEngine.SceneManagement.LoadSceneMode.Single);
            }
            else
            {
                // Fallback: Normal scene loading
                UnityEngine.SceneManagement.SceneManager.LoadScene("Lobby");
            }
        }

        // MinigameVotingManager'a bildir (lobby'de olacak)
        StartCoroutine(NotifyVotingManagerAfterSceneLoad());
    }

    /// <summary>
    /// Scene yüklendikten sonra voting manager'a bildir
    /// </summary>
    private System.Collections.IEnumerator NotifyVotingManagerAfterSceneLoad()
    {
        // Scene yüklenene kadar bekle
        yield return new WaitForSeconds(2f);

        // MinigameVotingManager'ı bul ve bildir
        if (MinigameVotingManager.Instance != null)
        {
            Debug.Log("🔄 MinigameVotingManager'a otomatik döngü bildirimi gönderiliyor...");
            MinigameVotingManager.Instance.OnMinigameCompletedServerRpc();
        }
        else
        {
            Debug.LogWarning("⚠️ MinigameVotingManager bulunamadı!");
        }
    }

    /// <summary>
    /// Minigame aktiflik durumu değiştiğinde
    /// </summary>
    private void OnMinigameActiveChanged(bool oldValue, bool newValue)
    {
        if (newValue)
        {
            OnMinigameStarted?.Invoke();
            Debug.Log("✅ Minigame başladı!");
        }
        else
        {
            OnMinigameEnded?.Invoke();
            Debug.Log("🏁 Minigame bitti!");
        }
    }

    /// <summary>
    /// Kalan süre değiştiğinde
    /// </summary>
    private void OnTimeRemainingChanged(float oldValue, float newValue)
    {
        OnTimeUpdated?.Invoke(newValue);
    }

    // Public getters
    public bool IsMinigameActive => isMinigameActive.Value;
    public float TimeRemaining => timeRemaining.Value;

    /// <summary>
    /// Manuel olarak minigame bitir (test için)
    /// </summary>
    [ContextMenu("Minigame'i Bitir")]
    public void TestEndMinigame()
    {
        if (IsServer)
        {
            EndMinigame("Manuel olarak bitirildi!");
        }
        else
        {
            EndMinigameServerRpc("Manuel olarak bitirildi!");
        }
    }
}
