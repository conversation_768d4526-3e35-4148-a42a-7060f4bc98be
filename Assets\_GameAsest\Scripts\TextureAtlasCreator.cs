using UnityEngine;

public class TextureAtlasCreator : MonoBehaviour
{
    [Header("Textures to Combine")]
    public Texture2D lavaTexture;
    public Texture2D noiseTexture;
    
    [Header("Output")]
    public string outputName = "LavaAtlas";
    
    [ContextMenu("Create Atlas")]
    void CreateAtlas()
    {
        if (lavaTexture == null || noiseTexture == null)
        {
            Debug.LogError("Both textures must be assigned!");
            return;
        }
        
        // Create new texture with alpha channel
        Texture2D atlas = new Texture2D(lavaTexture.width, lavaTexture.height, TextureFormat.RGBA32, false);
        
        Color[] lavaPixels = lavaTexture.GetPixels();
        Color[] noisePixels = noiseTexture.GetPixels();
        Color[] atlasPixels = new Color[lavaPixels.Length];
        
        // Combine: RGB from lava, Alpha from noise
        for (int i = 0; i < lavaPixels.Length; i++)
        {
            atlasPixels[i] = new Color(
                lavaPixels[i].r,
                lavaPixels[i].g, 
                lavaPixels[i].b,
                noisePixels[i].r // Use noise red channel as alpha
            );
        }
        
        atlas.SetPixels(atlasPixels);
        atlas.Apply();
        
        // Save as asset
        byte[] bytes = atlas.EncodeToPNG();
        System.IO.File.WriteAllBytes(Application.dataPath + "/" + outputName + ".png", bytes);
        
        Debug.Log($"Atlas created: {outputName}.png");
        
        #if UNITY_EDITOR
        UnityEditor.AssetDatabase.Refresh();
        #endif
    }
}