using UnityEngine;
using Unity.Netcode; // Netcode kütüphanesini ekledik.

public class BitisCizgisi : MonoBehaviour
{
    // Artık public referansa ihtiyacımız yok.
    // public GameManager gameManager; <-- BU SATIRI SİLİN

    private void Awake()
    {
        // Collider'ın trigger olduğundan emin ol.
        GetComponent<Collider>().isTrigger = true;
    }

    private void OnTriggerEnter(Collider other)
    {
        // Temas eden objenin bir oyuncu olup olmadığını kontrol et.
        // GetComponentInParent yerine direkt GetComponent kullanmak daha güvenilir olabilir.
        if (other.TryGetComponent<OyuncuKontrol>(out OyuncuKontrol oyuncu))
        {
            // Sadece bu objenin sahibi olan oyuncu kendi bitirişini tetikleyebilir.
            // Bu, başka bir oyuncunun size çarpıp sizin yerinize bitirmenizi engeller.
            if (!oyuncu.IsOwner) return;

            // GameManager'a "Ben bitiş çizgisine ulaştım" diye haber veriyoruz.
            // Bu istek sunucudaki GameManager'da işlenecek.
            if (GameManager.Instance != null)
            {
                GameManager.Instance.PlayerCrossedFinishLineServerRpc();
            }
        }
    }
}