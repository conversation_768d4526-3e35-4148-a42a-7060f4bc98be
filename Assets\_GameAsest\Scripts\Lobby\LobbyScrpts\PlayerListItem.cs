using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Lobideki oyuncu listesinde her oyuncu için görünen UI elemanı
/// </summary>
public class PlayerListItem : MonoBehaviour
{
    [Header("UI Components")]
    [Tooltip("Oyuncunun ismini gösteren text")]
    public TextMeshProUGUI playerNameText;
    
    [<PERSON>lt<PERSON>("Oyuncunun hazır durumunu gösteren text")]
    public TextMeshProUGUI readyStatusText;
    
    [Tooltip("Oyuncunun hazır durumunu gösteren ikon")]
    public Image readyIcon;
    
    [Tooltip("Oyuncu panelinin arkaplan resmi")]
    public Image backgroundImage;
    
    [Tooltip("Host ikonunu gösteren image")]
    public Image hostIcon;
    
    [Header("Visual Settings")]
    [Tooltip("Oyuncu hazır olduğunda kullanılacak renk")]
    public Color readyColor = Color.green;
    
    [Tooltip("Oyuncu hazır olmadığında kullanılacak renk")]
    public Color notReadyColor = Color.red;
    
    [Tooltip("Host oyuncu için arkaplan rengi")]
    public Color hostBackgroundColor = Color.yellow;
    
    [Tooltip("Normal oyuncu için arkaplan rengi")]
    public Color normalBackgroundColor = Color.white;
    
    private ulong clientId;
    private bool isHost;
    private bool isReady;
    
    /// <summary>
    /// Oyuncu bilgilerini günceller
    /// </summary>
    public void UpdatePlayerInfo(ulong clientId, string playerName, bool isReady, bool isHost)
    {
        this.clientId = clientId;
        this.isHost = isHost;
        this.isReady = isReady;

        if (playerNameText != null)
        {
            playerNameText.text = playerName;
        }
        else
        {
            Debug.LogError("[PlayerListItem] playerNameText null!");
        }

        UpdateVisuals();
    }


    /// <summary>
    /// Tüm görsel elemanları (hazır durumu, host durumu vb.) günceller.
    /// </summary>
    private void UpdateVisuals()
    {
        // Host durumunu ayarla
        if (hostIcon != null)
        {
            hostIcon.gameObject.SetActive(isHost);
        }

        // Arka plan rengini ayarla
        if (backgroundImage != null)
        {
            backgroundImage.color = isHost ? hostBackgroundColor : normalBackgroundColor;
        }

        // Hazır durumu metni ve rengi
        if (readyStatusText != null)
        {
            if (isHost)
            {
                readyStatusText.text = "HOST";
                readyStatusText.color = Color.blue; // Host için özel renk
            }
            else
            {
                readyStatusText.text = isReady ? "HAZIR" : "BEKLENİYOR";
                readyStatusText.color = isReady ? readyColor : notReadyColor;
            }
        }

        // Hazır durumu ikonu
        if (readyIcon != null)
        {
            if (isHost)
            {
                readyIcon.color = Color.blue;
            }
            else
            {
                readyIcon.color = isReady ? readyColor : notReadyColor;
            }
        }
    }

    /// <summary>
    /// Bu oyuncunun client ID'sini döndürür
    /// </summary>
    public ulong GetClientId()
    {
        return clientId;
    }
}
