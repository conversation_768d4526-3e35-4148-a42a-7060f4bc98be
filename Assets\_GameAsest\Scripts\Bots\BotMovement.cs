using UnityEngine;

// Bot'un oyuncuyla aynı etkileşimlere sahip olması için arayüzü kullanıyoruz.
public class BotMovement : MonoBehaviour, IEtkilesimeGirebilir
{
    [Header("Dependencies")]
    public Transform playerBody;
    public PlayerAnimator playerAnimator;

    [Header("Movement Settings")]
    public float playerSpeed = 5.0f;
    public float rotationSpeed = 15f;

    [Header("Jump Settings")]
    public float jumpHeight = 1.5f;
    public float jumpCooldown = 1f;
    private bool canJump = true;

    [Header("Physics Settings")]
    public float gravityValue = -9.81f;

    // --- Private Değ<PERSON><PERSON>kenler ---
    private CharacterController controller;
    private Vector3 playerVelocity;
    private Vector3 moveDirection;
    private float _orijinalHiz;
    private Vector3 _hariciHareketVektoru;

    void Awake()
    {
        controller = GetComponent<CharacterController>();
        _orijinalHiz = playerSpeed;
    }

    void Update()
    {
        // <PERSON>u script, dı<PERSON><PERSON><PERSON><PERSON> (BotController'dan) gelen moveDirection'a göre hareket eder.
        
        bool groundedPlayer = controller.isGrounded;
        if (groundedPlayer && playerVelocity.y < 0)
        {
            playerVelocity.y = -2f;
        }

        // --- Ortak Hareket Mantığı ---
        if (moveDirection.magnitude > 0.1f)
        {
            Quaternion targetRotation = Quaternion.LookRotation(moveDirection, Vector3.up);
            playerBody.rotation = Quaternion.Slerp(playerBody.rotation, targetRotation, rotationSpeed * Time.deltaTime);
        }

        playerVelocity.y += gravityValue * Time.deltaTime;

        Vector3 finalVelocity = moveDirection * playerSpeed;
        finalVelocity.y = playerVelocity.y;

        Vector3 nihaiHareket = finalVelocity + _hariciHareketVektoru;

        if (controller.enabled)
        {
            controller.Move(nihaiHareket * Time.deltaTime);
        }
    }
    
    // --- BotController'dan Gelen Komutlar ---
    
    /// <summary>
    /// Bot'un beyninden (BotController) gelen hareket komutunu işler.
    /// </summary>
    public void Move(Vector3 direction)
    {
        moveDirection = direction.normalized;
        if (playerAnimator != null)
        {
            bool isMoving = direction.magnitude > 0.1f;
            playerAnimator.UpdateAnimationState(isMoving, false, controller.isGrounded);
        }
    }
    
    /// <summary>
    /// Bot'un beyninden (BotController) gelen zıplama komutunu işler.
    /// </summary>
    public void Jump()
    {
        if (controller.isGrounded && canJump)
        {
            canJump = false;
            if (playerAnimator != null)
                playerAnimator.TriggerJump();

            playerVelocity.y = Mathf.Sqrt(jumpHeight * -2.0f * gravityValue);
            Invoke(nameof(ResetJump), jumpCooldown);
        }
    }

    private void ResetJump()
    {
        canJump = true;
    }

    #region Arayüz Fonksiyonları (IEtkilesimeGirebilir)
    // Bu kodlar OyuncuKontrol ile birebir aynıdır, böylece botlar da aynı fiziksel etkileşimlere maruz kalır.

    /// <summary>
    /// Karakterin o anda yere basıp basmadığını döndürür.
    /// </summary>
    public bool IsGrounded()
    {
        return controller.isGrounded;
    }

    public void ZıplatmaYap(float ziplamaHizi)
    {
        playerVelocity.y = ziplamaHizi;
    }

    public void HiziDegistir(float hizCarpanı)
    {
        playerSpeed = _orijinalHiz * hizCarpanı;
    }

    public void HiziSifirla()
    {
        playerSpeed = _orijinalHiz;
    }

    public void HariciHareketiAyarla(Vector3 hareketVektoru)
    {
        _hariciHareketVektoru = hareketVektoru;
    }

    public void HariciHareketiSifirla()
    {
        _hariciHareketVektoru = Vector3.zero;
    }

    public void ZiplamayaZorla(float ziplamaGucu)
    {
        playerVelocity.y = ziplamaGucu;
    }

    public Transform GetTransform()
    {
        return this.transform;
    }
    #endregion
}