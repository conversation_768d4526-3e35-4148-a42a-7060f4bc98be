using UnityEngine;
using Unity.Netcode;

/// <summary>
/// Sahne geçişleri için siyah fader yöneticisi
/// SafeAnimationSystem kullanarak güvenli geçişler sağlar
/// </summary>
public class SceneTransitionManager : MonoBehaviour
{
    // Singleton pattern
    private static SceneTransitionManager instance;

    public static SceneTransitionManager Instance
    {
        get
        {
            // Fader devre dışı bırakıldığı için, eğer instance yoksa yeni bir tane oluşturmayız.
            // Manuel olarak sahneye yerleştirilmesi gerekir.
            return instance;
        }
    }

    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    /// <summary>
    /// Siyah fade ile sahne geçişi başlat - Artık sadece sahne yüklemesi yapar.
    /// </summary>
    public void StartTransitionToScene(string sceneName, bool isHost = true)
    {
        if (NetworkManager.Singleton != null)
        {
            if (isHost)
            {
                NetworkManager.Singleton.StartHost();
                if (NetworkManager.Singleton.SceneManager != null)
                {
                    NetworkManager.Singleton.SceneManager.LoadScene(sceneName, UnityEngine.SceneManagement.LoadSceneMode.Single);
                    Debug.Log($"🎮 SceneTransitionManager: Host sahne yükleniyor - {sceneName}");
                }
            }
            else
            {
                NetworkManager.Singleton.StartClient();
                Debug.Log($"🔗 SceneTransitionManager: Client bağlanıyor - {sceneName}");
            }
        }
    }

    /// <summary>
    /// Sadece fade in efekti (sahne yüklendiğinde) - Artık bir şey yapmaz.
    /// </summary>
    public void FadeIn()
    {
        // Bu metod artık bir şey yapmıyor.
    }

    /// <summary>
    /// Sadece fade out efekti - Artık bir şey yapmaz.
    /// </summary>
    public void FadeOut(System.Action onComplete = null)
    {
        // Bu metod artık bir şey yapmıyor.
        onComplete?.Invoke(); // Callback'i yine de çağır.
    }

    private void OnDestroy()
    {
        if (instance == this)
        {
            instance = null;
        }
    }
}
