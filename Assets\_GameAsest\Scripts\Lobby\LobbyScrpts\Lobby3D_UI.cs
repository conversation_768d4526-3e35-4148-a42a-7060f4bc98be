using UnityEngine.SceneManagement;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Unity.Netcode;

/// <summary>
/// 3D Lobi arayüzünü yönetir. Oyuncu sayısını gösterir ve host için oyun başlatma butonunu kontrol eder.
/// </summary>
public class Lobby3D_UI : MonoBehaviour
{
    public static Lobby3D_UI Instance { get; private set; }

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
    }

    [Header("UI Elements")]
    [Tooltip("Oyunu başlatma butonu. Sadece host tarafından görülebilir.")]
    public Button startGameButton;

    [Tooltip("Lobideki oyuncu sayısını gösteren metin.")]
    public TextMeshProUGUI playerCountText;

    [Tooltip("Lobi durumu hakkında dinamik geri bildirim veren metin.")]
    public TextMeshProUGUI feedbackText;

    [Tooltip("Lobiden ayrılma butonu.")]
    public Button leaveLobbyButton;

        [Toolt<PERSON>("Oyuncunun hazır durumunu değiştiren buton. Sadece clientlar için görünür.")]
        public Button readyButton;

        [Tooltip("Paneli açıp kapatan buton.")]
        public Button togglePanelButton;

        [Tooltip("Açılıp kapanacak panel.")]
        public GameObject panelToToggle;

    private Coroutine ellipsisCoroutine;

    private void Start()
    {
        // Gerekli UI elemanlarının atanıp atanmadığını kontrol et.
        if (startGameButton == null || playerCountText == null || feedbackText == null || leaveLobbyButton == null || readyButton == null)
        {
            return;
        }

        // Butonların tıklama olaylarını ayarla.
        if (startGameButton != null)
        {
            startGameButton.onClick.AddListener(OnStartGameClicked);
        }

        leaveLobbyButton.onClick.AddListener(OnLeaveLobbyClicked);
        readyButton.onClick.AddListener(OnReadyButtonClicked);
        togglePanelButton.onClick.AddListener(OnTogglePanelClicked);

        // Lobi yöneticisinin event'ine abone ol.
        if (Lobby3DManager.Instance != null)
        {
            Lobby3DManager.Instance.OnPlayerListChanged += UpdateUI;
        }

        // Başlangıçta UI'ı bir kez güncelle.
        UpdateUI();
    }

    private void OnDestroy()
    {
        if (Instance == this)
        {
            Instance = null;
        }

        // Nesne yok edildiğinde event aboneliğini kaldır.
        if (Lobby3DManager.Instance != null)
        {
            Lobby3DManager.Instance.OnPlayerListChanged -= UpdateUI;
        }
        // Coroutine'i durdur.
        if (ellipsisCoroutine != null)
        {
            StopCoroutine(ellipsisCoroutine);
        }
    }

    /// <summary>
    /// UI elemanlarını günceller. Oyuncu sayısı ve buton görünürlüğü.
    /// </summary>
    private void UpdateUI()
    {
        if (Lobby3DManager.Instance == null) return;

        // Oyuncu sayısını ve formatını güncelle.
        int playerCount = Lobby3DManager.Instance.GetPlayerList().Count;
        int maxPlayers = Lobby3DManager.Instance.maxPlayers;
        playerCountText.text = $"Players: {playerCount} / {maxPlayers}";

        // Oyunu başlatma butonunu sadece host için görünür yap ve oyuncu sayısına göre etkileşimini ayarla.
        if (NetworkManager.Singleton != null)
        {
            bool isHost = NetworkManager.Singleton.IsHost;
            startGameButton.gameObject.SetActive(isHost);
            
            // Ready butonunu sadece client'lar için göster (host için gizle)
            readyButton.gameObject.SetActive(!isHost);

            if (isHost)
            {
                int minPlayers = Lobby3DManager.Instance.minPlayers;
                
                // Tüm client'ların hazır olup olmadığını kontrol et
                bool allClientsReady = true;
                int clientCount = 0;
                
                foreach (var player in Lobby3DManager.Instance.GetPlayerList())
                {
                    // Host değilse (client ise) kontrol et
                    if (player.ClientId != NetworkManager.Singleton.LocalClientId)
                    {
                        clientCount++;
                        if (!player.IsReady)
                        {
                            allClientsReady = false;
                        }
                    }
                }
                
                bool canStart = playerCount >= minPlayers && (clientCount == 0 || allClientsReady);
                startGameButton.interactable = canStart;

                // Geri bildirim metnini güncelle.
                if (playerCount < minPlayers)
                {
                    feedbackText.text = $"Oyunu başlatmak için en az {minPlayers} oyuncu gerekiyor.";
                    if (ellipsisCoroutine != null) StopCoroutine(ellipsisCoroutine);
                }
                else if (clientCount > 0 && !allClientsReady)
                {
                    feedbackText.text = "Tüm oyuncuların hazır olması bekleniyor...";
                    if (ellipsisCoroutine != null) StopCoroutine(ellipsisCoroutine);
                }
                else
                {
                    feedbackText.text = "Oyun başlatılmaya hazır!";
                    if (ellipsisCoroutine != null) StopCoroutine(ellipsisCoroutine);
                }
            }
            else
            {
                // Client için geri bildirim metni.
                string baseMessage = "Host'un oyunu başlatması bekleniyor";
                if (ellipsisCoroutine == null) // Sadece bir kez başlat
                {
                    ellipsisCoroutine = StartCoroutine(AnimateEllipsis(baseMessage));
                }

                // Ready butonunun durumunu güncelle
                UpdateReadyButtonText();
            }
        }
    }

    private void UpdateReadyButtonText()
    {
        if (Lobby3DManager.Instance == null || NetworkManager.Singleton == null) return;

        ulong localClientId = NetworkManager.Singleton.LocalClientId;
        bool isReady = false;

        foreach (var player in Lobby3DManager.Instance.GetPlayerList())
        {
            if (player.ClientId == localClientId)
            {
                isReady = player.IsReady;
                break;
            }
        }

        if (readyButton.GetComponentInChildren<TextMeshProUGUI>() != null)
        {
            readyButton.GetComponentInChildren<TextMeshProUGUI>().text = isReady ? "HAZIR" : "HAZIR OL";
        }
    }

    private void OnReadyButtonClicked()
    {
        if (Lobby3DManager.Instance != null && NetworkManager.Singleton != null)
        {
            Lobby3DManager.Instance.ToggleReadyStatusServerRpc(NetworkManager.Singleton.LocalClientId);
        }
    }

    private void OnTogglePanelClicked()
    {
        if (panelToToggle != null)
        {
            panelToToggle.SetActive(!panelToToggle.activeSelf);
        }
    }

    private System.Collections.IEnumerator AnimateEllipsis(string baseMessage)
    {
        int dotCount = 0;
        bool increasing = true;
        int maxDots = 3; // Maksimum üç nokta sayısı

        while (true)
        {
            string currentMessage = baseMessage;
            for (int i = 0; i < dotCount; i++)
            {
                currentMessage += ".";
            }
            feedbackText.text = currentMessage;

            if (increasing)
            {
                dotCount++;
                if (dotCount > maxDots)
                {
                    dotCount = maxDots - 1; // Geri saymaya başla
                    increasing = false;
                }
            }
            else
            {
                dotCount--;
                if (dotCount < 0)
                {
                    dotCount = 1; // İleri saymaya başla
                    increasing = true;
                }
            }
            yield return new WaitForSeconds(0.5f);
        }
    }

    /// <summary>
    /// Oyunu başlatma butonuna tıklandığında çağrılır.
    /// </summary>
    private void OnStartGameClicked()
    {
        if (Lobby3DManager.Instance == null)
        {
            return;
        }

        if (NetworkManager.Singleton == null)
        {
            return;
        }

        if (!NetworkManager.Singleton.IsHost)
        {
            return;
        }

        // Sunucuya oyunu başlatma komutunu gönder.
        Lobby3DManager.Instance.StartGameServerRpc(NetworkManager.Singleton.LocalClientId);
    }

    /// <summary>
    /// Lobiden ayrılma butonuna tıklandığında çağrılır.
    /// </summary>
    private void OnLeaveLobbyClicked()
    {
        if (NetworkManager.Singleton != null)
        {
            NetworkManager.Singleton.Shutdown();
            SceneManager.LoadScene("Menu");
        }
    }
}
