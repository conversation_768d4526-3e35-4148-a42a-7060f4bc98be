using UnityEngine;
using Unity.Netcode;
using System.Collections.Generic;
using System.Linq;
using System;

/// <summary>
/// Tek bir minigame'in tüm bilgilerini tutan data class
/// </summary>
[System.Serializable]
public class MinigameData : Unity.Netcode.INetworkSerializable
{
    [Tooltip("Minigame'in kod adı (scene adı)")]
    public string minigameName = "";

    [Toolt<PERSON>("Oyuncuya gösterilecek isim")]
    public string displayName = "";

    [Tooltip("Önizleme resmi")]
    public Sprite previewSprite;

    // Constructor
    public MinigameData()
    {
    }

    public MinigameData(string name, string display, Sprite sprite = null)
    {
        minigameName = name;
        displayName = display;
        previewSprite = sprite;
    }

    // INetworkSerializable implementation
    public void NetworkSerialize<T>(Unity.Netcode.BufferSerializer<T> serializer) where T : Unity.Netcode.IReaderWriter
    {
        serializer.SerializeValue(ref minigameName);
        serializer.SerializeValue(ref displayName);
        // Not: Sprite network serialize edilemez, sadece string bilgileri gönderiyoruz
        // Sprite'lar client'da local olarak bulunacak
    }
}

/// <summary>
/// Lobby'de minigame seçimi için voting sistemi.
/// Oyuncuların oy vermesini yönetir ve kazanan oyunu belirler.
/// </summary>
public class MinigameVotingManager : NetworkBehaviour
{
    public static MinigameVotingManager Instance { get; private set; }

    [Header("Voting Ayarları")]
    [Tooltip("Tüm minigame'lerin listesi - buradan rastgele seçilecek")]
    public MinigameData[] allMinigames = new MinigameData[4];

    [Tooltip("Voting'de gösterilecek maksimum minigame sayısı")]
    public int maxVotingOptions = 2;

    // Şu anda voting'de olan minigame'ler
    [System.NonSerialized]
    public MinigameData[] availableMinigames;

    [Header("Oynanmış Minigame Takibi")]
    [Tooltip("Bu session'da oynanmış minigame'lerin isimleri")]
    public List<string> playedMinigames = new List<string>();

    [Header("Otomatik Döngü Ayarları")]
    [Tooltip("Voting süresi (saniye)")]
    public float votingDuration = 30f;

    [Tooltip("Minigame bitince sonuç gösterme süresi (saniye)")]
    public float resultDisplayDuration = 10f;

    [Tooltip("Otomatik döngü aktif mi?")]
    public bool autoLoopEnabled = true;

    [Header("Events")]
    public System.Action OnVotingStarted;
    public System.Action OnVotingEnded;
    public System.Action<Dictionary<string, int>, Dictionary<string, float>> OnVotesUpdated;

    // Network Variables
    private NetworkVariable<bool> isVotingActive = new NetworkVariable<bool>(false,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);

    private NetworkVariable<float> votingTimeRemaining = new NetworkVariable<float>(0f,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);

    // Oyuncu oyları: ClientId -> MinigameIndex
    private NetworkList<VoteData> playerVotes;

    // Oy sayıları ve yüzdeler
    private Dictionary<string, int> voteCounts = new Dictionary<string, int>();
    private Dictionary<string, float> votePercentages = new Dictionary<string, float>();

    [System.Serializable]
    public struct VoteData : INetworkSerializable, System.IEquatable<VoteData>
    {
        public ulong clientId;
        public int minigameIndex; // -1 = oy vermedi

        public void NetworkSerialize<T>(BufferSerializer<T> serializer) where T : IReaderWriter
        {
            serializer.SerializeValue(ref clientId);
            serializer.SerializeValue(ref minigameIndex);
        }

        public bool Equals(VoteData other)
        {
            return clientId == other.clientId && minigameIndex == other.minigameIndex;
        }

        public override bool Equals(object obj)
        {
            return obj is VoteData other && Equals(other);
        }

        public override int GetHashCode()
        {
            return System.HashCode.Combine(clientId, minigameIndex);
        }
    }

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            playerVotes = new NetworkList<VoteData>();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();

        // Network variable değişikliklerini dinle
        isVotingActive.OnValueChanged += OnVotingActiveChanged;
        votingTimeRemaining.OnValueChanged += OnVotingTimeChanged;
        playerVotes.OnListChanged += OnPlayerVotesChanged;

        // Minigame listesini başlat
        InitializeVoteCounts();

        // UI yoksa oluştur - Client'lerde de çalışması için aktif edildi
        // Kısa bir delay ile kontrol et (network sync için)
        StartCoroutine(CheckAndCreateUI());
        
        // Ek güvenlik: Her 2 saniyede bir UI kontrolü yap (sadece ilk 10 saniye)
        StartCoroutine(PeriodicUICheck());
    }

    public override void OnNetworkDespawn()
    {
        base.OnNetworkDespawn();

        isVotingActive.OnValueChanged -= OnVotingActiveChanged;
        votingTimeRemaining.OnValueChanged -= OnVotingTimeChanged;
        playerVotes.OnListChanged -= OnPlayerVotesChanged;
    }

    /// <summary>
    /// UI'ın var olup olmadığını kontrol et ve yoksa oluştur
    /// </summary>
    private System.Collections.IEnumerator CheckAndCreateUI()
    {
        // Network sync için kısa bekle
        yield return new WaitForSeconds(0.5f);

        Debug.Log($"🔍 MinigameVotingManager: UI kontrol ediliyor... IsServer: {IsServer}");

        MinigameVotingUI existingUI = FindObjectOfType<MinigameVotingUI>();
        if (existingUI == null)
        {
            Debug.Log("🔧 MinigameVotingManager: UI bulunamadı, oluşturuluyor...");

            // BASIT ÇÖZÜM: Direkt basit UI oluştur
            Debug.Log("🔧 Basit UI oluşturuluyor...");
            CreateSimpleVotingUI();

            // Tekrar kontrol et
            yield return new WaitForSeconds(0.2f);
            existingUI = FindObjectOfType<MinigameVotingUI>();
            if (existingUI != null)
            {
                Debug.Log("✅ MinigameVotingManager: UI başarıyla oluşturuldu!");
            }
            else
            {
                Debug.LogError("❌ MinigameVotingManager: UI oluşturulamadı!");
            }
        }
        else
        {
            Debug.Log("✅ MinigameVotingManager: UI zaten var");
        }
        
        // UI referanslarını kontrol et ve düzelt
        if (existingUI != null && existingUI.votingPanel == null)
        {
            Debug.LogWarning("⚠️ MinigameVotingManager: UI'da votingPanel null, düzeltiliyor...");
            existingUI.votingPanel = existingUI.gameObject;
        }
    }

    /// <summary>
    /// İlk 10 saniye boyunca periyodik UI kontrolü yap
    /// </summary>
    private System.Collections.IEnumerator PeriodicUICheck()
    {
        float elapsedTime = 0f;
        float maxCheckTime = 10f; // 10 saniye boyunca kontrol et
        float checkInterval = 2f; // Her 2 saniyede bir kontrol et

        while (elapsedTime < maxCheckTime)
        {
            yield return new WaitForSeconds(checkInterval);
            elapsedTime += checkInterval;

            // UI var mı kontrol et
            MinigameVotingUI votingUI = FindObjectOfType<MinigameVotingUI>();
            if (votingUI == null)
            {
                Debug.LogWarning($"⚠️ MinigameVotingManager: UI hala yok! ({elapsedTime}s geçti) Yeniden oluşturuluyor...");
                
                // UI'ı yeniden oluşturmaya çalış
                var votingSetup = FindObjectOfType<VotingSystemSetup>();
                if (votingSetup != null)
                {
                    votingSetup.SetupVotingSystem();
                }
                else
                {
                    CreateVotingUI();
                }
            }
            else
            {
                Debug.Log($"✅ MinigameVotingManager: UI kontrolü OK ({elapsedTime}s)");
                
                // UI referanslarını kontrol et
                if (votingUI.votingPanel == null)
                {
                    Debug.LogWarning("⚠️ MinigameVotingManager: votingPanel null, düzeltiliyor...");
                    votingUI.votingPanel = votingUI.gameObject;
                }
                
                // UI bulundu, periyodik kontrolü durdur
                break;
            }
        }

        Debug.Log("🔄 MinigameVotingManager: Periyodik UI kontrolü tamamlandı");
    }

    private void InitializeVoteCounts()
    {
        voteCounts.Clear();
        votePercentages.Clear();

        // Null check ekle
        if (availableMinigames == null || availableMinigames.Length == 0)
        {
            Debug.LogError("❌ [MinigameVotingManager] availableMinigames null veya boş! Inspector'dan minigame'leri atayın.");
            return;
        }

        for (int i = 0; i < availableMinigames.Length; i++)
        {
            if (availableMinigames[i] != null && !string.IsNullOrEmpty(availableMinigames[i].minigameName))
            {
                voteCounts[availableMinigames[i].minigameName] = 0;
                votePercentages[availableMinigames[i].minigameName] = 0f;
            }
            else
            {
                Debug.LogWarning($"⚠️ [MinigameVotingManager] availableMinigames[{i}] null veya ismi boş!");
            }
        }

        Debug.Log($"✅ [MinigameVotingManager] {voteCounts.Count} minigame için vote counts başlatıldı");
    }

    /// <summary>
    /// Voting'i başlat (sadece server)
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    public void StartVotingServerRpc()
    {
        if (!IsServer || isVotingActive.Value) return;

        // Voting için minigame'leri seç
        SelectMinigamesForVoting();

        // Voting durumunu aktif et
        isVotingActive.Value = true;
        votingTimeRemaining.Value = votingDuration;

        // Mevcut oyları temizle
        playerVotes.Clear();
        InitializeVoteCounts();

        // Voting timer'ını başlat
        StartCoroutine(VotingTimer());
    }

    /// <summary>
    /// Voting için minigame'leri seç (oynanmayanlardan max 2 tane)
    /// </summary>
    private void SelectMinigamesForVoting()
    {
        // Null check
        if (allMinigames == null || allMinigames.Length == 0)
        {
            Debug.LogError("❌ allMinigames array null veya boş!");
            // Fallback: Default minigame'ler oluştur
            CreateDefaultMinigames();
        }

        // Oynanmamış minigame'leri bul
        List<MinigameData> unplayedMinigames = new List<MinigameData>();

        foreach (var minigame in allMinigames)
        {
            if (minigame != null && !string.IsNullOrEmpty(minigame.minigameName) &&
                !playedMinigames.Contains(minigame.minigameName))
            {
                unplayedMinigames.Add(minigame);
            }
        }

        // Eğer oynanmamış minigame kalmadıysa, tümünü sıfırla
        if (unplayedMinigames.Count == 0)
        {
            playedMinigames.Clear();
            unplayedMinigames.AddRange(allMinigames);
        }

        // Listeyi rastgele karıştır
        ShuffleList(unplayedMinigames);

        // İlk N tanesini seç (max 2 tane)
        List<MinigameData> selectedMinigames = new List<MinigameData>();
        int selectCount = Mathf.Min(maxVotingOptions, unplayedMinigames.Count);

        for (int i = 0; i < selectCount; i++)
        {
            selectedMinigames.Add(unplayedMinigames[i]);
        }

        availableMinigames = selectedMinigames.ToArray();

        // Eğer hiç minigame seçilmediyse fallback
        if (availableMinigames.Length == 0)
        {
            availableMinigames = new MinigameData[]
            {
                new MinigameData("RenkOyunu", "Renk Oyunu"),
                new MinigameData("fakeblock_Scene", "Fake Block")
            };
        }

        // Seçilen minigame'leri tüm client'lara gönder
        SyncMinigamesClientRpc(availableMinigames);
    }

    /// <summary>
    /// Default minigame'leri oluştur
    /// </summary>
    private void CreateDefaultMinigames()
    {
        allMinigames = new MinigameData[]
        {
            new MinigameData("RenkOyunu", "Renk Oyunu"),
            new MinigameData("fakeblock_Scene", "Fake Block"),
            new MinigameData("TestScene", "Test Oyunu"),
            new MinigameData("TestMinigame2", "Test Oyunu 2")
        };
    }

    /// <summary>
    /// Seçilen minigame'leri tüm client'lara gönder
    /// </summary>
    [ClientRpc]
    private void SyncMinigamesClientRpc(MinigameData[] selectedMinigames)
    {
        // Server'dan gelen minigame'leri local array'e kopyala
        availableMinigames = new MinigameData[selectedMinigames.Length];
        for (int i = 0; i < selectedMinigames.Length; i++)
        {
            // Client'da sprite'ı local array'den bul
            Sprite localSprite = FindLocalSprite(selectedMinigames[i].minigameName);

            availableMinigames[i] = new MinigameData(
                selectedMinigames[i].minigameName,
                selectedMinigames[i].displayName,
                localSprite
            );
        }

        // UI'ı güncelle (eğer voting aktifse)
        if (isVotingActive.Value)
        {
            OnVotingStarted?.Invoke();
        }
    }

    /// <summary>
    /// Client'da local sprite'ı bul
    /// </summary>
    private Sprite FindLocalSprite(string minigameName)
    {
        // allMinigames array'inde aynı isimli minigame'i bul
        if (allMinigames != null)
        {
            foreach (var minigame in allMinigames)
            {
                if (minigame != null && minigame.minigameName == minigameName)
                {
                    return minigame.previewSprite;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Liste elemanlarını rastgele karıştır (Fisher-Yates shuffle)
    /// </summary>
    private void ShuffleList<T>(List<T> list)
    {
        for (int i = list.Count - 1; i > 0; i--)
        {
            int randomIndex = UnityEngine.Random.Range(0, i + 1);
            T temp = list[i];
            list[i] = list[randomIndex];
            list[randomIndex] = temp;
        }
    }

    /// <summary>
    /// Minigame bittiğinde çağrılır - otomatik döngü için
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    public void OnMinigameCompletedServerRpc()
    {
        if (!IsServer || !autoLoopEnabled) return;

        // Sonuç gösterme süresinden sonra yeni voting başlat
        StartCoroutine(StartNextVotingAfterDelay());
    }

    /// <summary>
    /// Belirtilen süre sonra yeni voting başlat
    /// </summary>
    private System.Collections.IEnumerator StartNextVotingAfterDelay()
    {
        // Sonuç gösterme süresi kadar bekle
        yield return new WaitForSeconds(resultDisplayDuration);

        // Yeni voting başlat
        if (autoLoopEnabled)
        {
            StartVotingServerRpc();
        }
    }

    /// <summary>
    /// Otomatik döngüyü durdur/başlat
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    public void SetAutoLoopEnabledServerRpc(bool enabled)
    {
        if (!IsServer) return;

        autoLoopEnabled = enabled;
    }

    /// <summary>
    /// Oyuncu oy verme
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    public void CastVoteServerRpc(int minigameIndex, ServerRpcParams rpcParams = default)
    {
        if (!IsServer || !isVotingActive.Value) return;

        ulong clientId = rpcParams.Receive.SenderClientId;

        // Geçerli minigame index kontrolü
        if (minigameIndex < 0 || minigameIndex >= availableMinigames.Length)
        {
            return;
        }

        // Mevcut oyunu bul veya yeni ekle
        bool foundExistingVote = false;
        for (int i = 0; i < playerVotes.Count; i++)
        {
            if (playerVotes[i].clientId == clientId)
            {
                // Mevcut oyunu güncelle
                VoteData updatedVote = playerVotes[i];
                updatedVote.minigameIndex = minigameIndex;
                playerVotes[i] = updatedVote;
                foundExistingVote = true;
                break;
            }
        }

        if (!foundExistingVote)
        {
            // Yeni oy ekle
            playerVotes.Add(new VoteData
            {
                clientId = clientId,
                minigameIndex = minigameIndex
            });
        }

        // Oy sayılarını güncelle
        UpdateVoteCounts();
    }

    private void UpdateVoteCounts()
    {
        if (!IsServer) return;

        // Oy sayılarını sıfırla
        InitializeVoteCounts();

        // Oyları say
        foreach (var vote in playerVotes)
        {
            if (vote.minigameIndex >= 0 && vote.minigameIndex < availableMinigames.Length)
            {
                string minigameName = availableMinigames[vote.minigameIndex].minigameName;
                voteCounts[minigameName]++;
            }
        }

        // Yüzdeleri hesapla
        int totalVotes = playerVotes.Count;
        if (totalVotes > 0)
        {
            foreach (var minigame in availableMinigames)
            {
                votePercentages[minigame.minigameName] = (float)voteCounts[minigame.minigameName] / totalVotes * 100f;
            }
        }

        // UI'ı güncelle (tüm clientlara)
        UpdateVotingUIClientRpc(voteCounts.Values.ToArray(), votePercentages.Values.ToArray());
    }

    [ClientRpc]
    private void UpdateVotingUIClientRpc(int[] votes, float[] percentages)
    {
        // Sözlükleri yeniden oluştur
        for (int i = 0; i < availableMinigames.Length && i < votes.Length && i < percentages.Length; i++)
        {
            voteCounts[availableMinigames[i].minigameName] = votes[i];
            votePercentages[availableMinigames[i].minigameName] = percentages[i];
        }

        // UI event'ini tetikle
        OnVotesUpdated?.Invoke(voteCounts, votePercentages);
    }

    private System.Collections.IEnumerator VotingTimer()
    {
        while (votingTimeRemaining.Value > 0 && isVotingActive.Value)
        {
            yield return new WaitForSeconds(1f);
            votingTimeRemaining.Value -= 1f;

            // Tüm oyuncular oy verdiyse ve süre 3'ten fazlaysa hızlandır
            if (AllPlayersVoted() && votingTimeRemaining.Value > 3f)
            {
                Debug.Log("⚡ [MinigameVotingManager] Tüm oyuncular oy verdi, süre 3 saniyeye düşürülüyor!");
                votingTimeRemaining.Value = 3f;
            }
        }

        if (isVotingActive.Value)
        {
            EndVoting();
        }
    }

    /// <summary>
    /// Tüm oyuncuların oy verip vermediğini kontrol eder
    /// </summary>
    private bool AllPlayersVoted()
    {
        if (NetworkManager.Singleton == null) return false;

        int totalPlayers = NetworkManager.Singleton.ConnectedClientsIds.Count;
        int votedPlayers = playerVotes.Count;

        bool allVoted = votedPlayers >= totalPlayers;

        if (allVoted && votedPlayers > 0)
        {
            Debug.Log($"✅ [MinigameVotingManager] Tüm oyuncular oy verdi! ({votedPlayers}/{totalPlayers})");
        }

        return allVoted;
    }

    private void EndVoting()
    {
        if (!IsServer) return;

        isVotingActive.Value = false;

        // Kazanan minigame'i belirle
        string winnerMinigame = GetWinnerMinigame();

        // Kazanan minigame'i oynanmış listesine ekle
        if (!playedMinigames.Contains(winnerMinigame))
        {
            playedMinigames.Add(winnerMinigame);
        }

        // Kazanan minigame'i başlat
        StartWinnerMinigameClientRpc(winnerMinigame);
    }

    private string GetWinnerMinigame()
    {
        if (voteCounts.Count == 0) return availableMinigames[0].minigameName; // Varsayılan

        // En çok oy alan minigame'i bul
        string winner = availableMinigames[0].minigameName;
        int maxVotes = 0;

        foreach (var kvp in voteCounts)
        {
            if (kvp.Value > maxVotes)
            {
                maxVotes = kvp.Value;
                winner = kvp.Key;
            }
        }

        return winner;
    }

    [ClientRpc]
    private void StartWinnerMinigameClientRpc(string winnerMinigame)
    {
        Debug.Log($"🎯 [MinigameVotingManager] Kazanan minigame: {winnerMinigame}, sahne değiştiriliyor...");

        // NetworkManager ile sahneyi değiştir (sadece server'da)
        if (IsServer && NetworkManager.Singleton != null && NetworkManager.Singleton.SceneManager != null)
        {
            Debug.Log($"✅ [MinigameVotingManager] Server sahne değiştiriyor: {winnerMinigame}");
            NetworkManager.Singleton.SceneManager.LoadScene(winnerMinigame, UnityEngine.SceneManagement.LoadSceneMode.Single);
        }
        else if (!IsServer)
        {
            Debug.Log($"ℹ️ [MinigameVotingManager] Client - sahne değişikliği server tarafından yapılacak");
        }
        else
        {
            Debug.LogError("❌ [MinigameVotingManager] NetworkManager veya SceneManager bulunamadı!");
        }
    }

    // Event handlers
    private void OnVotingActiveChanged(bool previousValue, bool newValue)
    {
        Debug.Log($"🔄 [MinigameVotingManager] Voting durumu değişti: {previousValue} -> {newValue}");

        if (newValue)
        {
            Debug.Log("🎯 [MinigameVotingManager] OnVotingStarted event'i tetikleniyor...");
            OnVotingStarted?.Invoke();
        }
        else
        {
            Debug.Log("🏁 [MinigameVotingManager] OnVotingEnded event'i tetikleniyor...");
            OnVotingEnded?.Invoke();
        }
    }

    private void OnVotingTimeChanged(float previousValue, float newValue)
    {
        // Zaman güncellemesi için UI'da kullanılabilir
    }

    private void OnPlayerVotesChanged(NetworkListEvent<VoteData> changeEvent)
    {
        // Oy değişikliği için UI güncellemesi server tarafında yapılıyor
    }

    // Public getters
    public bool IsVotingActive => isVotingActive.Value;
    public float VotingTimeRemaining => votingTimeRemaining.Value;
    public Dictionary<string, int> GetVoteCounts() => new Dictionary<string, int>(voteCounts);
    public Dictionary<string, float> GetVotePercentages() => new Dictionary<string, float>(votePercentages);

    /// <summary>
    /// Oyuncunun mevcut oyunu al
    /// </summary>
    public int GetPlayerVote(ulong clientId)
    {
        foreach (var vote in playerVotes)
        {
            if (vote.clientId == clientId)
            {
                return vote.minigameIndex;
            }
        }
        return -1; // Oy vermemiş
    }

    /// <summary>
    /// Voting UI'ını oluştur
    /// </summary>
    private void CreateVotingUI()
    {
        Debug.Log("🔧 CreateVotingUI başlıyor...");

        try
        {
            // Canvas bul
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                Debug.LogError("❌ MinigameVotingManager: Canvas bulunamadı! UI oluşturulamıyor.");

                // Tüm Canvas'ları listele
                Canvas[] allCanvases = FindObjectsOfType<Canvas>();
                Debug.Log($"🔍 Sahne içindeki Canvas sayısı: {allCanvases.Length}");
                for (int i = 0; i < allCanvases.Length; i++)
                {
                    Debug.Log($"Canvas {i}: {allCanvases[i].name} - Active: {allCanvases[i].gameObject.activeInHierarchy}");
                }
                return;
            }

            Debug.Log($"✅ Canvas bulundu: {canvas.name}");

        // Basit voting UI oluştur
        GameObject votingImage = new GameObject("VotingImage");
        votingImage.transform.SetParent(canvas.transform, false);

        // RectTransform ayarla
        RectTransform imageRect = votingImage.AddComponent<RectTransform>();
        imageRect.anchorMin = Vector2.zero;
        imageRect.anchorMax = Vector2.one;
        imageRect.offsetMin = Vector2.zero;
        imageRect.offsetMax = Vector2.zero;

        // Background image
        UnityEngine.UI.Image backgroundImage = votingImage.AddComponent<UnityEngine.UI.Image>();
        backgroundImage.color = new Color(0, 0, 0, 0.8f);

        // Başlık
        GameObject titleObj = new GameObject("Title");
        titleObj.transform.SetParent(votingImage.transform, false);
        RectTransform titleRect = titleObj.AddComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0, 0.8f);
        titleRect.anchorMax = new Vector2(1, 1f);
        titleRect.offsetMin = Vector2.zero;
        titleRect.offsetMax = Vector2.zero;

        TMPro.TextMeshProUGUI titleText = titleObj.AddComponent<TMPro.TextMeshProUGUI>();
        titleText.text = "Hangi Oyunu Oynamak İstiyorsun?";
        titleText.fontSize = 24;
        titleText.alignment = TMPro.TextAlignmentOptions.Center;
        titleText.color = Color.white;

        // Timer
        GameObject timerObj = new GameObject("Timer");
        timerObj.transform.SetParent(votingImage.transform, false);
        RectTransform timerRect = timerObj.AddComponent<RectTransform>();
        timerRect.anchorMin = new Vector2(0, 0.7f);
        timerRect.anchorMax = new Vector2(1, 0.8f);
        timerRect.offsetMin = Vector2.zero;
        timerRect.offsetMax = Vector2.zero;

        TMPro.TextMeshProUGUI timerText = timerObj.AddComponent<TMPro.TextMeshProUGUI>();
        timerText.text = "Kalan Süre: 30s";
        timerText.fontSize = 18;
        timerText.alignment = TMPro.TextAlignmentOptions.Center;
        timerText.color = Color.yellow;

        // Buton container
        GameObject buttonContainer = new GameObject("ButtonContainer");
        buttonContainer.transform.SetParent(votingImage.transform, false);
        RectTransform containerRect = buttonContainer.AddComponent<RectTransform>();
        containerRect.anchorMin = new Vector2(0.1f, 0.2f);
        containerRect.anchorMax = new Vector2(0.9f, 0.7f);
        containerRect.offsetMin = Vector2.zero;
        containerRect.offsetMax = Vector2.zero;

        UnityEngine.UI.VerticalLayoutGroup layoutGroup = buttonContainer.AddComponent<UnityEngine.UI.VerticalLayoutGroup>();
        layoutGroup.spacing = 10;
        layoutGroup.childAlignment = TextAnchor.MiddleCenter;
        layoutGroup.childControlHeight = true;
        layoutGroup.childControlWidth = true;

            // MinigameVotingUI component ekle
            Debug.Log("🔧 MinigameVotingUI component ekleniyor...");
            MinigameVotingUI votingUI = votingImage.AddComponent<MinigameVotingUI>();
            votingUI.votingPanel = votingImage;
            votingUI.votingTitleText = titleText;
            votingUI.votingTimerText = timerText;
            votingUI.minigameButtonsParent = buttonContainer.transform;

            Debug.Log("🔧 Button prefab oluşturuluyor...");
            votingUI.minigameButtonPrefab = CreateButtonPrefab();

            // Başlangıçta gizle
            votingImage.SetActive(false);

            Debug.Log("✅ CreateVotingUI başarıyla tamamlandı!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ CreateVotingUI hatası: {e.Message}");
            Debug.LogError($"Stack trace: {e.StackTrace}");
        }
    }

    private GameObject CreateButtonPrefab()
    {
        try
        {
            Debug.Log("🔧 Button prefab oluşturuluyor...");
            GameObject buttonObj = new GameObject("MinigameVoteButton");
            RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
            buttonRect.sizeDelta = new Vector2(300, 60);

            UnityEngine.UI.Button button = buttonObj.AddComponent<UnityEngine.UI.Button>();
            UnityEngine.UI.Image buttonImage = buttonObj.AddComponent<UnityEngine.UI.Image>();
            buttonImage.color = Color.white;

        // Button text
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(buttonObj.transform, false);
        RectTransform textRect = textObj.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;

        TMPro.TextMeshProUGUI buttonText = textObj.AddComponent<TMPro.TextMeshProUGUI>();
        buttonText.text = "Minigame";
        buttonText.fontSize = 16;
        buttonText.alignment = TMPro.TextAlignmentOptions.Center;
        buttonText.color = Color.black;

        // Vote info
        GameObject voteInfoObj = new GameObject("VoteInfo");
        voteInfoObj.transform.SetParent(buttonObj.transform, false);
        RectTransform voteInfoRect = voteInfoObj.AddComponent<RectTransform>();
        voteInfoRect.anchorMin = new Vector2(0, 0);
        voteInfoRect.anchorMax = new Vector2(1, 0.3f);
        voteInfoRect.offsetMin = Vector2.zero;
        voteInfoRect.offsetMax = Vector2.zero;

        TMPro.TextMeshProUGUI voteInfoText = voteInfoObj.AddComponent<TMPro.TextMeshProUGUI>();
        voteInfoText.text = "0 oy (0%)";
        voteInfoText.fontSize = 12;
        voteInfoText.alignment = TMPro.TextAlignmentOptions.Center;
        voteInfoText.color = Color.gray;

            MinigameVoteButton voteButton = buttonObj.AddComponent<MinigameVoteButton>();
            voteButton.voteButton = button;
            voteButton.minigameNameText = buttonText;
            // voteInfoText kaldırıldı
            voteButton.backgroundImage = buttonImage;

            Debug.Log("✅ Button prefab başarıyla oluşturuldu!");
            return buttonObj;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ CreateButtonPrefab hatası: {e.Message}");
            Debug.LogError($"Stack trace: {e.StackTrace}");
            return null;
        }
    }

    /// <summary>
    /// Basit voting UI oluştur - karmaşık sistemler yerine
    /// </summary>
    private void CreateSimpleVotingUI()
    {
        try
        {
            Debug.Log("🔧 CreateSimpleVotingUI başlıyor...");

            // Canvas bul
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                Debug.LogError("❌ Canvas bulunamadı!");
                return;
            }

            Debug.Log($"✅ Canvas bulundu: {canvas.name}");

            // Basit panel oluştur
            GameObject panel = new GameObject("VotingPanel");
            panel.transform.SetParent(canvas.transform, false);

            RectTransform rect = panel.AddComponent<RectTransform>();
            rect.anchorMin = Vector2.zero;
            rect.anchorMax = Vector2.one;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            UnityEngine.UI.Image bg = panel.AddComponent<UnityEngine.UI.Image>();
            bg.color = new Color(0, 0, 0, 0.8f);

            // Başlık text
            GameObject titleObj = new GameObject("Title");
            titleObj.transform.SetParent(panel.transform, false);
            RectTransform titleRect = titleObj.AddComponent<RectTransform>();
            titleRect.anchorMin = new Vector2(0, 0.8f);
            titleRect.anchorMax = new Vector2(1, 1f);
            titleRect.offsetMin = Vector2.zero;
            titleRect.offsetMax = Vector2.zero;

            TMPro.TextMeshProUGUI titleText = titleObj.AddComponent<TMPro.TextMeshProUGUI>();
            titleText.text = "Hangi Oyunu Oynamak İstiyorsun?";
            titleText.fontSize = 24;
            titleText.alignment = TMPro.TextAlignmentOptions.Center;
            titleText.color = Color.white;

            // Timer text
            GameObject timerObj = new GameObject("Timer");
            timerObj.transform.SetParent(panel.transform, false);
            RectTransform timerRect = timerObj.AddComponent<RectTransform>();
            timerRect.anchorMin = new Vector2(0, 0.7f);
            timerRect.anchorMax = new Vector2(1, 0.8f);
            timerRect.offsetMin = Vector2.zero;
            timerRect.offsetMax = Vector2.zero;

            TMPro.TextMeshProUGUI timerText = timerObj.AddComponent<TMPro.TextMeshProUGUI>();
            timerText.text = "Kalan Süre: 30s";
            timerText.fontSize = 18;
            timerText.alignment = TMPro.TextAlignmentOptions.Center;
            timerText.color = Color.yellow;

            // Button container
            GameObject buttonContainer = new GameObject("ButtonContainer");
            buttonContainer.transform.SetParent(panel.transform, false);
            RectTransform containerRect = buttonContainer.AddComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0.1f, 0.2f);
            containerRect.anchorMax = new Vector2(0.9f, 0.7f);
            containerRect.offsetMin = Vector2.zero;
            containerRect.offsetMax = Vector2.zero;

            // MinigameVotingUI component ekle
            MinigameVotingUI ui = panel.AddComponent<MinigameVotingUI>();
            ui.votingPanel = panel;
            ui.votingTitleText = titleText;
            ui.votingTimerText = timerText;
            ui.minigameButtonsParent = buttonContainer.transform;

            // Basit buton prefab oluştur
            ui.minigameButtonPrefab = CreateSimpleButtonPrefab();

            // Panel'i başlangıçta gizle ama voting aktifse göster
            if (isVotingActive.Value)
            {
                panel.SetActive(true);
                Debug.Log("🎯 Voting aktif, panel açık olarak ayarlandı!");
            }
            else
            {
                panel.SetActive(false);
                Debug.Log("⏸️ Voting aktif değil, panel kapalı olarak ayarlandı");
            }

            Debug.Log("✅ CreateSimpleVotingUI başarıyla tamamlandı!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ CreateSimpleVotingUI hatası: {e.Message}");
        }
    }

    private GameObject CreateSimpleButtonPrefab()
    {
        GameObject buttonObj = new GameObject("SimpleVoteButton");
        RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
        buttonRect.sizeDelta = new Vector2(300, 60);

        UnityEngine.UI.Button button = buttonObj.AddComponent<UnityEngine.UI.Button>();
        UnityEngine.UI.Image buttonImage = buttonObj.AddComponent<UnityEngine.UI.Image>();
        buttonImage.color = Color.white;

        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(buttonObj.transform, false);
        RectTransform textRect = textObj.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;

        TMPro.TextMeshProUGUI buttonText = textObj.AddComponent<TMPro.TextMeshProUGUI>();
        buttonText.text = "Minigame";
        buttonText.fontSize = 16;
        buttonText.alignment = TMPro.TextAlignmentOptions.Center;
        buttonText.color = Color.black;

        // Basit vote button component
        MinigameVoteButton voteButton = buttonObj.AddComponent<MinigameVoteButton>();
        voteButton.voteButton = button;
        voteButton.minigameNameText = buttonText;
        voteButton.backgroundImage = buttonImage;

        return buttonObj;
    }
}
