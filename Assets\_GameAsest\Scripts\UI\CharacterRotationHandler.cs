using UnityEngine;
using UnityEngine.EventSystems;

public class CharacterRotationHandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, <PERSON>ointer<PERSON><PERSON><PERSON><PERSON><PERSON>, IDragHandler
{
    [Header("Atanacaklar")]
    [SerializeField] private GameObject characterToRotate;

    [Header("Döndürme Ayarları")]
    [SerializeField] private float rotationSpeed = 0.5f;
    [SerializeField] private float damping = 5f;

    [Header("Otomatik Geri Dönme Ayarları")]
    [Tooltip("Dokunulmadıktan kaç saniye sonra başa dönecek?")]
    [SerializeField] private float timeToReset = 3f;
    [Tooltip("Başa dönerkenki dönüş hızı.")]
    [SerializeField] private float resetSpeed = 2f;

    // --- Private Değişkenler ---
    private Quaternion initialRotation;     // Karakterin ilk pozisyonunu saklar
    private float inertiaVelocity = 0f;       // <PERSON><PERSON> hı<PERSON>ı
    private float inactivityTimer = 0f;     // Dokunulmay<PERSON><PERSON><PERSON> sayan sayaç
    private bool isReturning = false;       // Karakter başa dönüyor mu?
    private bool isDragging = false;        // Kullanıcı şu an sürüklüyor mu?

    private void Start()
    {
        // Oyun başladığında karakterin o anki rotasyonunu kaydet
        if (characterToRotate != null)
        {
            initialRotation = characterToRotate.transform.rotation;
        }
    }

    private void Update()
    {
        // Null kontrolü - GameObject destroy edilmişse hiçbir şey yapma
        if (characterToRotate == null)
        {
            return;
        }

        // Eğer kullanıcı sürüklüyorsa, bu frame'de başka bir şey yapma
        if (isDragging)
        {
            return;
        }

        // --- BÖLÜM 1: Kayma (Inertia) ---
        // Eğer bir kayma hızı varsa...
        if (inertiaVelocity != 0)
        {
            float rotationAmount = inertiaVelocity * Time.deltaTime;
            characterToRotate.transform.Rotate(Vector3.up, -rotationAmount, Space.World);
            inertiaVelocity = Mathf.Lerp(inertiaVelocity, 0, damping * Time.deltaTime);

            if (Mathf.Abs(inertiaVelocity) < 0.1f)
            {
                inertiaVelocity = 0;
            }
        }
        // --- BÖLÜM 2: Otomatik Başa Dönme ---
        else // Eğer kayma bittiyse, geri dönme sayacını başlat
        {
            inactivityTimer += Time.deltaTime;

            // Sayaç, belirlediğimiz süreyi aştıysa veya karakter zaten başa dönme modundaysa...
            if (inactivityTimer >= timeToReset || isReturning)
            {
                isReturning = true; // Başa dönme modunu aktif et

                // Karakteri yavaşça başlangıç rotasyonuna döndür
                characterToRotate.transform.rotation = Quaternion.Slerp(
                    characterToRotate.transform.rotation,
                    initialRotation,
                    resetSpeed * Time.deltaTime
                );

                // Eğer başlangıç pozisyonuna çok yaklaşırsa, tam olarak ayarla ve işlemi bitir
                if (Quaternion.Angle(characterToRotate.transform.rotation, initialRotation) < 0.1f)
                {
                    characterToRotate.transform.rotation = initialRotation;
                    inactivityTimer = 0f;
                    isReturning = false;
                }
            }
        }
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        if (characterToRotate == null) return;

        // Kullanıcı tıkladığı an tüm otomatik hareketleri durdur ve sayaçları sıfırla
        inertiaVelocity = 0;
        inactivityTimer = 0f;
        isReturning = false;
        isDragging = true;
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        if (characterToRotate == null) return;

        isDragging = false;
        // Fare bırakıldığında, son hıza göre kayma (inertia) Update içinde başlayacak
    }

    public void OnDrag(PointerEventData eventData)
    {
        if (characterToRotate == null) return;

        // Sürüklerken son hızı sürekli kaydet
        inertiaVelocity = eventData.delta.x * rotationSpeed;

        float rotationAmount = inertiaVelocity * Time.deltaTime;
        characterToRotate.transform.Rotate(Vector3.up, -rotationAmount, Space.World);
    }
} 