using UnityEngine;
using TMPro; // TextMeshPro kullanmak için bu satır gerekli

public class ArayuzYoneticisi : MonoBehaviour
{
    // public TextMeshProUGUI sureText;         // Süre göstergesi
    public TextMeshProUGUI renkText;         // Güvenli renk göstergesi
    public TextMeshProUGUI puanText;         // Puan göstergesi
    public TextMeshProUGUI minigameSureText; // Inspector'dan atanacak

    public static ArayuzYoneticisi Instance { get; private set; }
    private void Awake()
    {
        Instance = this;
    }

    // Süre ve güvenli rengi güncelle
    public void ZamanGuncelle(float toplamSure, float gecenSure)
    {
        // if (sureText != null)
        //     sureText.text = "Süre: " + Mathf.Max(0, toplamSure - gecenSure).ToString("F1");

        if (minigameSureText != null)
            minigameSureText.text = "Süre: " + Mathf.Max(0, toplamSure - gecenSure).ToString("F1");

        if (renkText != null)
            renkText.text = "Güvenli Renk: " + RenkOyunuManager.Instance.GetGuvenliRenk().ToString();
    }

    // Puanı güncelle
    public void PuanGuncelle(int puan)
    {
        if (puanText != null)
            puanText.text = $"Puan: {puan}";
    }

    public void SabitSifirSureGoster()
    {
        if (minigameSureText != null)
            minigameSureText.text = "Süre: 00:00";
    }
}