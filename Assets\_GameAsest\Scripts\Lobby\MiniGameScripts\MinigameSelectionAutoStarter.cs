using UnityEngine;
using Unity.Netcode;
using System.Collections;

/// <summary>
/// MinigameSelection sahnesinde voting sistemini otomatik başlatan script
/// Bu script sahne yüklendiğinde MinigameVotingManager'ı bulup voting'i başlatır
/// </summary>
public class MinigameSelectionAutoStarter : NetworkBehaviour
{
    [Header("Otomatik Başlatma Ayarları")]
    [Tooltip("Sahne yüklendikten sonra kaç saniye bekleyerek voting'i başlat")]
    public float beklemeZamani = 1f;
    
    [Tooltip("Debug logları göster")]
    public bool showDebugLogs = true;

    private bool votingBaslatildi = false;

    public override void OnNetworkSpawn()
    {
        if (showDebugLogs)
            Debug.Log($"🎯 MinigameSelectionAutoStarter: OnNetworkSpawn - IsServer: {IsServer}");

        if (IsServer && !votingBaslatildi)
        {
            if (showDebugLogs)
                Debug.Log("🎯 MinigameSelectionAutoStarter: Server - Voting başlatılıyor...");

            StartCoroutine(VotingBaslat());
        }

        // Client'ler için UI kontrolü
        if (!IsServer)
        {
            if (showDebugLogs)
                Debug.Log("👥 MinigameSelectionAutoStarter: Client - UI kontrolü başlatılıyor...");

            StartCoroutine(ClientUIKontrol());
        }
    }

    private void Start()
    {
        // Network spawn olmadan da çalışması için fallback
        if (!votingBaslatildi)
        {
            if (showDebugLogs)
                Debug.Log("🔄 MinigameSelectionAutoStarter: Start'ta voting kontrolü yapılıyor...");

            if (NetworkManager.Singleton != null && NetworkManager.Singleton.IsServer)
            {
                StartCoroutine(VotingBaslat());
            }
            else
            {
                StartCoroutine(ClientUIKontrol());
            }
        }
    }

    private IEnumerator VotingBaslat()
    {
        if (votingBaslatildi) yield break;
        
        // Kısa bir bekleme (sahne tamamen yüklensin)
        yield return new WaitForSeconds(beklemeZamani);
        
        // Server kontrolü
        if (NetworkManager.Singleton != null && NetworkManager.Singleton.IsServer)
        {
            // MinigameVotingManager'ı bul
            var votingManager = FindFirstObjectByType<MinigameVotingManager>();
            if (votingManager != null)
            {
                if (showDebugLogs)
                    Debug.Log("✅ MinigameSelectionAutoStarter: MinigameVotingManager bulundu, voting başlatılıyor...");
                
                votingManager.StartVotingServerRpc();
                votingBaslatildi = true;
                
                if (showDebugLogs)
                    Debug.Log("🗳️ MinigameSelectionAutoStarter: Voting başarıyla başlatıldı!");
            }
            else
            {
                if (showDebugLogs)
                    Debug.LogWarning("⚠️ MinigameSelectionAutoStarter: MinigameVotingManager bulunamadı!");
                
                // VotingSystemSetup'ı bul ve sistemi kur
                var votingSetup = FindFirstObjectByType<VotingSystemSetup>();
                if (votingSetup != null)
                {
                    if (showDebugLogs)
                        Debug.Log("🔧 MinigameSelectionAutoStarter: VotingSystemSetup bulundu, sistem kuruluyor...");
                    
                    votingSetup.SetupVotingSystem();
                    
                    // Kısa bekleyiş sonra tekrar dene
                    yield return new WaitForSeconds(0.5f);
                    votingManager = FindFirstObjectByType<MinigameVotingManager>();
                    if (votingManager != null)
                    {
                        votingManager.StartVotingServerRpc();
                        votingBaslatildi = true;
                        
                        if (showDebugLogs)
                            Debug.Log("✅ MinigameSelectionAutoStarter: Voting sistemi kuruldu ve başlatıldı!");
                    }
                }
                else
                {
                    if (showDebugLogs)
                        Debug.LogError("❌ MinigameSelectionAutoStarter: VotingSystemSetup de bulunamadı!");
                }
            }
        }
        else
        {
            if (showDebugLogs)
                Debug.Log("ℹ️ MinigameSelectionAutoStarter: Server değil, voting başlatma atlandı");
        }
    }

    /// <summary>
    /// Client'lerde UI'ın açılmasını kontrol eder
    /// </summary>
    private IEnumerator ClientUIKontrol()
    {
        // Kısa bir bekleme (sahne tamamen yüklensin)
        yield return new WaitForSeconds(beklemeZamani);

        if (showDebugLogs)
            Debug.Log("👥 MinigameSelectionAutoStarter: Client UI kontrolü başlıyor...");

        // MinigameVotingUI'ı bul
        var votingUI = FindFirstObjectByType<MinigameVotingUI>();
        if (votingUI != null)
        {
            if (showDebugLogs)
                Debug.Log("✅ MinigameSelectionAutoStarter: Client'te MinigameVotingUI bulundu");

            // Voting aktif olana kadar bekle
            float waitTime = 0f;
            while (waitTime < 15f) // Maksimum 15 saniye bekle (artırıldı)
            {
                var votingManager = FindFirstObjectByType<MinigameVotingManager>();
                if (votingManager != null && votingManager.IsVotingActive)
                {
                    if (showDebugLogs)
                        Debug.Log("🎯 MinigameSelectionAutoStarter: Client'te voting aktif, UI kontrol ediliyor...");

                    // UI panelinin açık olduğundan emin ol
                    if (votingUI.votingPanel != null && !votingUI.votingPanel.activeInHierarchy)
                    {
                        if (showDebugLogs)
                            Debug.Log("🔧 MinigameSelectionAutoStarter: Client'te voting paneli açılıyor...");

                        votingUI.votingPanel.SetActive(true);

                        // Panel'in parent'larının da aktif olduğundan emin ol
                        Transform parent = votingUI.votingPanel.transform.parent;
                        while (parent != null)
                        {
                            if (!parent.gameObject.activeInHierarchy)
                            {
                                if (showDebugLogs)
                                    Debug.Log($"🔧 MinigameSelectionAutoStarter: Parent aktif ediliyor: {parent.name}");
                                parent.gameObject.SetActive(true);
                            }
                            parent = parent.parent;
                        }
                    }
                    break;
                }

                yield return new WaitForSeconds(0.5f);
                waitTime += 0.5f;
            }

            if (waitTime >= 15f)
            {
                Debug.LogWarning("⚠️ MinigameSelectionAutoStarter: 15 saniye bekledik ama voting aktif olmadı!");
            }
        }
        else
        {
            if (showDebugLogs)
                Debug.LogWarning("⚠️ MinigameSelectionAutoStarter: Client'te MinigameVotingUI bulunamadı!");
        }
    }

    /// <summary>
    /// Manuel voting başlatma (test için)
    /// </summary>
    [ContextMenu("Manuel Voting Başlat")]
    public void ManuelVotingBaslat()
    {
        if (NetworkManager.Singleton != null && NetworkManager.Singleton.IsServer)
        {
            votingBaslatildi = false;
            StartCoroutine(VotingBaslat());
        }
        else
        {
            Debug.LogWarning("Manuel voting başlatma sadece server'da çalışır!");
        }
    }
}
