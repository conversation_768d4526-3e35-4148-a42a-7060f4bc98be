using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using TMPro;

public class GrafikAyarlari : MonoBehaviour
{
    // Grafik kalitesi seviyelerini daha okunaklı yapmak için enum kullanıyoruz.
    public enum KaliteSeviyesi
    {
        Yu<PERSON><PERSON>,
        Dusuk,
        Orta
    }

    // Mevcut kalite seviyesini takip edecek değişkenimiz.
    private KaliteSeviyesi mevcutKalite;

    // Butonun üzerindeki yazıyı güncellemek için. (İsteğe bağlı)
    // Unity'de butonu oluşturduktan sonra bu alana TextMeshPro objesini sürükleyeceksin.
    public TextMeshProUGUI kaliteGostergeText;

    // Oyun ilk açıldığında varsayılan ayarları yapmak için Start fonksiyonu.
    void Start()
    {
        // YENİ: Oyuna varsayılan olarak Orta kalitede başla.
        mevcutKalite = KaliteSeviyesi.Orta; 
        AyarlariUygula();

        // Bonus: Ayarı kaydettiysen buradan geri yükleyebilirsin. Varsayılanı Orta yaptık.
        // int kayitliKalite = PlayerPrefs.GetInt("GrafikKalitesi", (int)KaliteSeviyesi.Orta);
        // mevcutKalite = (KaliteSeviyesi)kayitliKalite;
        // AyarlariUygula();
    }

    // Butona her tıklandığında bu fonksiyon çalışacak.
    public void KaliteSeviyesiniDegistir()
    {
        // YENİ: Döngü sırasını Orta -> Yüksek -> Düşük olarak güncelledik.
        switch (mevcutKalite)
        {
            case KaliteSeviyesi.Orta:
                mevcutKalite = KaliteSeviyesi.Yuksek;
                break;
            case KaliteSeviyesi.Yuksek:
                mevcutKalite = KaliteSeviyesi.Dusuk;
                break;
            case KaliteSeviyesi.Dusuk:
                mevcutKalite = KaliteSeviyesi.Orta;
                break;
        }

        // Yeni seçilen ayarı oyuna uygula.
        AyarlariUygula();

        // Bonus: Ayarı kaydetmek istersen
        // PlayerPrefs.SetInt("GrafikKalitesi", (int)mevcutKalite);
    }

    private void AyarlariUygula()
    {
        // URP kullanılıyor mu kontrol et
        var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
        
        if (urpAsset != null)
        {
            // URP kullanılıyorsa render scale ayarla
            switch (mevcutKalite)
            {
                case KaliteSeviyesi.Dusuk:
                    urpAsset.renderScale = 0.75f;
                    QualitySettings.SetQualityLevel(0);
                    break;
                case KaliteSeviyesi.Orta:
                    urpAsset.renderScale = 0.85f;
                    QualitySettings.SetQualityLevel(2);
                    break;
                case KaliteSeviyesi.Yuksek:
                    urpAsset.renderScale = 1.0f;
                    QualitySettings.SetQualityLevel(5);
                    break;
            }
            Debug.Log($"URP - Grafik kalitesi {mevcutKalite}, Render Scale: {urpAsset.renderScale}");
        }
        else
        {
            // Built-in pipeline kullanılıyorsa sadece Quality Settings ayarla
            switch (mevcutKalite)
            {
                case KaliteSeviyesi.Dusuk:
                    QualitySettings.SetQualityLevel(0);
                    break;
                case KaliteSeviyesi.Orta:
                    QualitySettings.SetQualityLevel(2);
                    break;
                case KaliteSeviyesi.Yuksek:
                    QualitySettings.SetQualityLevel(5);
                    break;
            }
            Debug.Log($"Built-in Pipeline - Grafik kalitesi {mevcutKalite}");
        }

        // Text güncelle
        if (kaliteGostergeText != null)
        {
            switch (mevcutKalite)
            {
                case KaliteSeviyesi.Dusuk:
                    kaliteGostergeText.text = "Düşük";
                    break;
                case KaliteSeviyesi.Orta:
                    kaliteGostergeText.text = "Orta";
                    break;
                case KaliteSeviyesi.Yuksek:
                    kaliteGostergeText.text = "Yüksek";
                    break;
            }
        }
    }
}