
using UnityEngine;
using Unity.Netcode;

public class DonenKolController : NetworkBehaviour
{
    [Header("Dönüş Ayarları")]
    [Tooltip("Mekanizmanın saniyedeki dönüş hızı. Pozitif ve negatif değerler yönü değiştirir.")]
    public float donusHizi = 45f;

    // YENİ: Hangi eksen etrafında döneceğini belirleyen değişken.
    [Tooltip("Dönüşün gerçekleşeceği eksen. Örn: (0, 1, 0) dikey döner, (1, 0, 0) yatay döner.")]
    public Vector3 donusEkseni = new Vector3(0, 1, 0);

    // Netcode ile senkronizasyon için NetworkVariable
    private NetworkVariable<float> syncedRotation = new NetworkVariable<float>(0f, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);

    private float localRotation = 0f;
    private Quaternion initialRotation;

    void Update()
    {
        if (IsServer)
        {
            // Sunucu: dönüşü hesaplar ve senkronize eder
            localRotation += donusHizi * Time.deltaTime;
            syncedRotation.Value = localRotation;
            transform.localRotation = initialRotation * Quaternion.AngleAxis(localRotation, donusEkseni.normalized);
        }
        else
        {
            // İstemci: yumuşak geçiş (interpolation) uygular
            localRotation = Mathf.LerpAngle(localRotation, syncedRotation.Value, 10f * Time.deltaTime);
            transform.localRotation = initialRotation * Quaternion.AngleAxis(localRotation, donusEkseni.normalized);
        }
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();
        // Tüm kullanıcılar için başlangıç rotasyonu sabit: (90,0,0)
        initialRotation = Quaternion.Euler(90f, 0f, 0f);
        localRotation = syncedRotation.Value;
        // Başlangıçta da zorunlu olarak uygula (ilk frame beklemeden)
        transform.localRotation = initialRotation * Quaternion.AngleAxis(localRotation, donusEkseni.normalized);
    }
}