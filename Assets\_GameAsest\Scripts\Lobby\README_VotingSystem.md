# 🗳️ Minigame Voting Sistemi

Lobby'de oyun başlatma butonuna basıldığında açılan minigame seçim sistemi.

## 📋 Özellikler

- ✅ Oyuncular minigame'ler arasından seçim yapabilir
- ✅ Her minigame için oy sayısı ve yüzde gösterilir
- ✅ Tüm oyuncular oy verdikten sonra en çok oy alan oyun başlar
- ✅ 30 saniye voting süresi (ayarlanabilir)
- ✅ Network üzerinden senkronize çalışır

## 🔧 Kurulum

### 1. Lobby Sahnesine Ekleme

Lobby sahnesine `VotingSystemSetup` scriptini ekleyin:

1. Lobby sahnesini açın
2. Boş bir GameObject oluşturun (isim: "VotingSystemSetup")
3. `VotingSystemSetup.cs` scriptini ekleyin
4. Script ayarlarını yapın:
   - **Auto Setup**: ✅ (otomatik kurulum)
   - **Voting Duration**: 30 (saniye)
   - **Available Minigames**: ["RenkOyunu", "fakeblock_Scene"]
   - **Minigame Display Names**: ["Renk Oyunu", "Fake Block"]

### 2. Canvas Ayarları

Lobby sahnesinde bir Canvas olduğundan emin olun. Yoksa:

1. Sağ tık → UI → Canvas
2. Canvas Scaler component'ini ayarlayın
3. VotingSystemSetup'ta **UI Parent** olarak Canvas'ı seçin

## 🎮 Nasıl Çalışır

### 1. Oyun Başlatma
- Host, lobby'de "Start Game" butonuna basar
- Voting ekranı tüm oyunculara açılır

### 2. Oy Verme
- Her oyuncu bir minigame seçer
- Seçilen buton yeşil renkte vurgulanır
- Oy sayıları ve yüzdeler gerçek zamanlı güncellenir

### 3. Sonuç
- 30 saniye sonra veya herkes oy verdikten sonra
- En çok oy alan minigame başlatılır
- Eşitlik durumunda ilk minigame seçilir

## 🔧 Özelleştirme

### Yeni Minigame Ekleme

`VotingSystemSetup` scriptinde:

```csharp
public string[] availableMinigames = {
    "RenkOyunu",
    "fakeblock_Scene",
    "YeniOyun"  // Yeni sahne adı
};

public string[] minigameDisplayNames = {
    "Renk Oyunu",
    "Fake Block",
    "Yeni Oyun"  // Görünen isim
};
```

### Voting Süresini Değiştirme

```csharp
public float votingDuration = 45f; // 45 saniye
```

### UI Görünümünü Değiştirme

`MinigameVotingUI.cs` scriptinde:

```csharp
[Header("Görsel Ayarlar")]
public Color normalColor = Color.white;
public Color selectedColor = Color.green;
```

## 🐛 Sorun Giderme

### Voting Ekranı Açılmıyor
- `MinigameVotingManager` sahneye eklenmiş mi?
- `NetworkObject` component'i var mı?
- Console'da hata mesajları var mı?

### Butonlar Çalışmıyor
- `MinigameVotingUI` component'i var mı?
- Button prefab'ı doğru ayarlanmış mı?
- `MinigameVoteButton` component'i butonlarda var mı?

### Network Senkronizasyon Sorunu
- Tüm oyuncular aynı sahneye bağlı mı?
- Host voting'i başlattı mı?
- NetworkManager çalışıyor mu?

## 📁 Dosya Yapısı

```
Lobby/
├── MinigameVotingManager.cs    # Ana voting yöneticisi
├── MinigameVotingUI.cs         # UI yöneticisi ve buton scripti
├── VotingSystemSetup.cs        # Otomatik kurulum scripti
└── README_VotingSystem.md      # Bu dosya
```

## 🔄 Entegrasyon

Sistem şu scriptlerle entegre çalışır:

- **Lobby3DManager**: Start Game butonunu voting'e yönlendirir
- **UniversalSceneManager**: Kazanan minigame'i başlatır
- **NetworkManager**: Oyuncu bağlantılarını yönetir

## ⚡ Performans

- Network trafiği minimal (sadece oy değişikliklerinde)
- UI güncellemeleri optimize edilmiş
- Memory leak koruması var

## 🎯 Gelecek Geliştirmeler

- [ ] Minigame önizleme resimleri
- [ ] Ses efektleri
- [ ] Animasyonlu geçişler
- [ ] Voting geçmişi
- [ ] Admin override seçenekleri
