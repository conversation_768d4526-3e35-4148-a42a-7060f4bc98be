%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1396171337991640634
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3099541228114444593}
  - component: {fileID: 4472423638450179940}
  - component: {fileID: 6563455202761341153}
  m_Layer: 0
  m_Name: '[Network]'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3099541228114444593
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1396171337991640634}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -81.41607, y: 0.78442, z: 79.93034}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4472423638450179940
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1396171337991640634}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6960e84d07fb87f47956e7a81d71c4e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ProtocolType: 0
  m_UseWebSockets: 0
  m_UseEncryption: 0
  m_MaxPacketQueueSize: 128
  m_MaxPayloadSize: 6144
  m_HeartbeatTimeoutMS: 500
  m_ConnectTimeoutMS: 1000
  m_MaxConnectAttempts: 60
  m_DisconnectTimeoutMS: 30000
  ConnectionData:
    Address: 127.0.0.1
    Port: 7777
    ServerListenAddress: 127.0.0.1
  DebugSimulator:
    PacketDelayMS: 0
    PacketJitterMS: 0
    PacketDropRate: 0
--- !u!114 &6563455202761341153
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1396171337991640634}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 593a2fe42fa9d37498c96f9a383b6521, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  NetworkManagerExpanded: 0
  NetworkConfig:
    ProtocolVersion: 0
    NetworkTransport: {fileID: 4472423638450179940}
    PlayerPrefab: {fileID: 4526217875587005210, guid: 787857d2a51824c4b864bc1b478c9fc6, type: 3}
    Prefabs:
      NetworkPrefabsLists:
      - {fileID: 11400000, guid: 949849ab386568b4d9ecb1e60199a8ff, type: 2}
    TickRate: 30
    ClientConnectionBufferTimeout: 10
    ConnectionApproval: 0
    ConnectionData: 
    EnableTimeResync: 0
    TimeResyncInterval: 30
    EnsureNetworkVariableLengthSafety: 0
    EnableSceneManagement: 1
    ForceSamePrefabs: 1
    RecycleNetworkIds: 1
    NetworkIdRecycleDelay: 120
    RpcHashSize: 0
    LoadSceneTimeOut: 120
    SpawnTimeout: 10
    EnableNetworkLogs: 1
    NetworkTopology: 0
    UseCMBService: 0
    AutoSpawnPlayerPrefabClientSide: 1
    NetworkProfilingMetrics: 1
    OldPrefabList: []
  RunInBackground: 1
  LogLevel: 1
