using UnityEngine;
using System.Collections.Generic;
using UnityEngine.Rendering;

[System.Serializable]
public class LavaInstance
{
    public Vector3 position;
    public Vector3 rotation;
    public Vector3 scale = Vector3.one;
    public Vector4 variation; // For shader variation
}

public class LavaBatchOptimizer : MonoBehaviour
{
    [Header("Batch Optimization")]
    public Mesh lavaMesh;
    public Material lavaMaterial;
    public List<LavaInstance> lavaInstances = new List<LavaInstance>();
    
    [Header("Performance")]
    [Range(1, 1023)]
    public int maxInstancesPerBatch = 1023;
    public bool enableFrustumCulling = true;
    public float cullingDistance = 100f;
    
    private Matrix4x4[] matrices;
    private Vector4[] variations;
    private MaterialPropertyBlock propertyBlock;
    private Camera mainCamera;
    
    void Start()
    {
        mainCamera = Camera.main;
        propertyBlock = new MaterialPropertyBlock();
        
        // Convert existing lava objects to instances
        ConvertExistingLavaObjects();
        
        // Prepare batch data
        PrepareBatchData();
    }
    
    void ConvertExistingLavaObjects()
    {
        // Find all objects with lava materials and convert to instances
        MeshRenderer[] renderers = FindObjectsOfType<MeshRenderer>();
        
        foreach (var renderer in renderers)
        {
            if (renderer.material.shader.name.Contains("Lava"))
            {
                LavaInstance instance = new LavaInstance
                {
                    position = renderer.transform.position,
                    rotation = renderer.transform.eulerAngles,
                    scale = renderer.transform.localScale,
                    variation = new Vector4(
                        Random.Range(-1f, 1f), // X offset
                        Random.Range(-1f, 1f), // Y offset
                        Random.Range(0f, 6.28f), // Phase offset
                        Random.Range(0.8f, 1.2f)  // Speed multiplier
                    )
                };
                
                lavaInstances.Add(instance);
                
                // Disable original object
                renderer.gameObject.SetActive(false);
            }
        }
        
        Debug.Log($"Converted {lavaInstances.Count} lava objects to instances");
    }
    
    void PrepareBatchData()
    {
        int instanceCount = lavaInstances.Count;
        matrices = new Matrix4x4[instanceCount];
        variations = new Vector4[instanceCount];
        
        for (int i = 0; i < instanceCount; i++)
        {
            var instance = lavaInstances[i];
            matrices[i] = Matrix4x4.TRS(
                instance.position,
                Quaternion.Euler(instance.rotation),
                instance.scale
            );
            variations[i] = instance.variation;
        }
    }
    
    void Update()
    {
        if (lavaMesh == null || lavaMaterial == null || matrices == null)
            return;
        
        // ULTRA OPTIMIZED - Single batch for all instances
        // Set all variations at once
        propertyBlock.SetVectorArray("_VariationOffset", variations);
        
        // Single draw call for ALL instances - this reduces batches dramatically!
        Graphics.DrawMeshInstanced(
            lavaMesh, 
            0, 
            lavaMaterial, 
            matrices, 
            matrices.Length, 
            propertyBlock
        );
    }
    
    void OnDrawGizmosSelected()
    {
        if (lavaInstances == null) return;
        
        Gizmos.color = Color.red;
        foreach (var instance in lavaInstances)
        {
            Gizmos.DrawWireCube(instance.position, instance.scale);
        }
    }
}