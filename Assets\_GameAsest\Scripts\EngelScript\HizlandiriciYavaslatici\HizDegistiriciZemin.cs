using UnityEngine;

// Bu, bizim kullanmamız gereken DOĞRU ve ESNEK versiyondur.
public class HizDegistirenZemin : MonoBehaviour
{
    [Header("H<PERSON>z Değişimi Ayarı")]
    [Tooltip("Oyuncunun hızını değiştirecek çarpan. 1 = normal hız, 0.5 = yarısı, 2 = iki katı.")]
    public float hizCarpanı = 1.0f;

    private void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player"))
        {
            // "OyuncuKontrol" yerine "IEtkilesimeGirebilir" arıyoruz.
            IEtkilesimeGirebilir etkilesenObje = other.GetComponentInParent<IEtkilesimeGirebilir>();
            
            if (etkilesenObje != null)
            {
                // Komutu, bulduğumuz yeni genel arayüz değişkeni üzerinden veriyoruz.
                etkilesenObje.HiziDegistir(hizCarpanı);
            }
        }
    }

    private void OnTriggerExit(Collider other)
    {
        if (other.CompareTag("Player"))
        {
            // Burada da "OyuncuKontrol" yerine "IEtkilesimeGirebilir" arıyoruz.
            IEtkilesimeGirebilir etkilesenObje = other.GetComponentInParent<IEtkilesimeGirebilir>();

            if (etkilesenObje != null)
            {
                // Hızı sıfırlama komutunu da yine arayüz üzerinden veriyoruz.
                etkilesenObje.HiziSifirla();
            }
        }
    }
}