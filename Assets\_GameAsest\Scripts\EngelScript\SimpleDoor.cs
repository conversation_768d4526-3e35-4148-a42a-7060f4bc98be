using UnityEngine;
using Unity.Netcode;

/// <summary>
/// <PERSON><PERSON>u tarafından kontrol edilen ve tüm istemcilerle senkronize olan basit bir kapı mekanizması.
/// Her kapının hızı ve bekleme süreleri, belirlenen aralıklarda rastgele atanır.
/// Kapıların hareketi zaman tabanlıdır, bu da onların asla ara pozisyonlarda durmamasını garanti eder.
/// </summary>
public class SimpleDoor : NetworkBehaviour
{
    [Header("Kapı Ayarları")]
    [Tooltip("Hareket edecek olan kapının Transform'u. Boş bırakılırsa, bu objenin kendisi kullanılır.")]
    [SerializeField] private Transform doorTransform;

    [Tooltip("Kapının açık pozisyona geldiğinde ne kadar hareket edeceği (yerel koordinat sisteminde).")]
    [SerializeField] private Vector3 openOffset = new Vector3(0, 5f, 0);

    [Header("Rastgelelik Ayarları")]
    [Tooltip("Kapının hareket hızının rastgele seçileceği aralık (Min, Max).")]
    [SerializeField] private Vector2 moveSpeedRange = new Vector2(4f, 8f);

    [Tooltip("Kapının açık kalma süresinin rastgele seçileceği aralık (Min, Max).")]
    [SerializeField] private Vector2 openDurationRange = new Vector2(1f, 3f);

    [Tooltip("Kapının kapalı kalma süresinin rastgele seçileceği aralık (Min, Max).")]
    [SerializeField] private Vector2 closedDurationRange = new Vector2(1f, 3f);

    private readonly NetworkVariable<DoorState> networkState = new NetworkVariable<DoorState>(
        DoorState.Closed,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server
    );

    private readonly NetworkVariable<DoorParameters> networkParams = new NetworkVariable<DoorParameters>(
        default,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server
    );

    private enum DoorState { Closed, Opening, Open, Closing }

    private struct DoorParameters : INetworkSerializable
    {
        public float moveSpeed;
        public float openDuration;
        public float closedDuration;
        public float moveDuration; // Hareketin ne kadar süreceği

        public void NetworkSerialize<T>(BufferSerializer<T> serializer) where T : IReaderWriter
        {
            serializer.SerializeValue(ref moveSpeed);
            serializer.SerializeValue(ref openDuration);
            serializer.SerializeValue(ref closedDuration);
            serializer.SerializeValue(ref moveDuration);
        }
    }

    private Vector3 closedPosition;
    private Vector3 openPosition;
    private float timer;

    private void Awake()
    {
        if (doorTransform == null)
        {
            doorTransform = transform;
        }
        closedPosition = doorTransform.localPosition;
        openPosition = closedPosition + openOffset;
    }

    public override void OnNetworkSpawn()
    {
        if (IsServer)
        {
            float speed = Random.Range(moveSpeedRange.x, moveSpeedRange.y);
            float distance = Vector3.Distance(closedPosition, openPosition);
            // Hız veya mesafe sıfırsa, sonsuz döngüyü önlemek için hareket süresini 0 yap.
            float moveDur = (speed > 0 && distance > 0) ? distance / speed : 0f;

            networkParams.Value = new DoorParameters
            {
                moveSpeed = speed,
                openDuration = Random.Range(openDurationRange.x, openDurationRange.y),
                closedDuration = Random.Range(closedDurationRange.x, closedDurationRange.y),
                moveDuration = moveDur
            };
        }

        networkState.OnValueChanged += (prevState, newState) => UpdateDoorVisuals();
        UpdateDoorVisuals();
    }

    private void Update()
    {
        if (!IsServer) return;

        timer += Time.deltaTime;

        // Parametreler henüz ağ üzerinden gelmediyse bekle.
        if (networkParams.Value.moveSpeed <= 0) return;

        switch (networkState.Value)
        {
            case DoorState.Closed:
                if (timer >= networkParams.Value.closedDuration)
                {
                    networkState.Value = DoorState.Opening;
                    timer = 0f;
                }
                break;

            case DoorState.Opening:
                // Pozisyon yerine zamana göre kontrol et.
                if (timer >= networkParams.Value.moveDuration)
                {
                    networkState.Value = DoorState.Open;
                    timer = 0f;
                }
                break;

            case DoorState.Open:
                if (timer >= networkParams.Value.openDuration)
                {
                    networkState.Value = DoorState.Closing;
                    timer = 0f;
                }
                break;

            case DoorState.Closing:
                // Pozisyon yerine zamana göre kontrol et.
                if (timer >= networkParams.Value.moveDuration)
                {
                    networkState.Value = DoorState.Closed;
                    timer = 0f;
                }
                break;
        }
    }

    private void LateUpdate()
    {
        // Parametreler henüz ağ üzerinden gelmediyse hareket etme.
        if (networkParams.Value.moveSpeed <= 0) return;
        
        if (networkState.Value == DoorState.Opening)
        {
            doorTransform.localPosition = Vector3.MoveTowards(doorTransform.localPosition, openPosition, networkParams.Value.moveSpeed * Time.deltaTime);
        }
        else if (networkState.Value == DoorState.Closing)
        {
            doorTransform.localPosition = Vector3.MoveTowards(doorTransform.localPosition, closedPosition, networkParams.Value.moveSpeed * Time.deltaTime);
        }
    }

    /// <summary>
    /// Ağ durumu değiştiğinde kapının pozisyonunu anında ayarlar.
    /// Bu, kapının her zaman tam açık veya tam kapalı pozisyonda olmasını garanti eder.
    /// </summary>
    private void UpdateDoorVisuals()
    {
        switch (networkState.Value)
        {
            case DoorState.Open:
                doorTransform.localPosition = openPosition;
                break;
            case DoorState.Closed:
                doorTransform.localPosition = closedPosition;
                break;
        }
    }

    private void OnDrawGizmosSelected()
    {
        Transform targetTransform = doorTransform == null ? transform : doorTransform;
        Vector3 startPoint = targetTransform.parent ? targetTransform.parent.TransformPoint(targetTransform.localPosition) : targetTransform.localPosition;
        Vector3 endPoint = targetTransform.parent ? targetTransform.parent.TransformPoint(targetTransform.localPosition + openOffset) : (targetTransform.localPosition + openOffset);

        Gizmos.color = Color.red;
        Gizmos.DrawSphere(startPoint, 0.2f);
        Gizmos.DrawLine(startPoint, endPoint);
        Gizmos.color = Color.green;
        Gizmos.DrawSphere(endPoint, 0.2f);
    }
}
