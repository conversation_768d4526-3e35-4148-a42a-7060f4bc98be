using UnityEngine;
using Unity.Netcode;

/// <summary>
/// Main Menu sahnesinde UniversalSceneManager'ı kurmak için özel script.
/// Ana menüde UniversalSceneManager'ı sıfırlar ve yeni oyun için hazırlar.
/// </summary>
public class MainMenuSceneManagerSetup : MonoBehaviour
{
    [Header("Ana Menü Ayarları")]
    [Tooltip("Ana menüde UniversalSceneManager'ı otomatik sıfırla")]
    public bool otomatikSifirlama = true;
    
    [Header("Sahne Listesi")]
    [Tooltip("Oynanacak mini oyun sahneleri")]
    public string[] miniOyunSahneleri = {
        "RenkOyunu",
        "fakeblock_Scene"
    };
    
    [Tooltip("Ana menü sahne adı")]
    public string anaMenuSahnesi = "MainMenu";
    
    [Tooltip("Sahne geçiş bekleme süresi")]
    public float sahneGecisiBeklemeSuresi = 3f;

    private void Start()
    {
        if (otomatikSifirlama)
        {
            // Ana menüde oynanan sahneleri sıfırla
            SifirlaOynananSahneler();
        }
    }

    public void SifirlaOynananSahneler()
    {
        // UniversalSceneManager varsa oynanan sahneleri sıfırla
        if (UniversalSceneManager.Instance != null)
        {
            UniversalSceneManager.Instance.DebugOynananSahneleriSifirla();
            Debug.Log("🔄 MainMenuSceneManagerSetup: Oynanan sahneler sıfırlandı, yeni oyun için hazır.");
        }
        else
        {
            Debug.Log("ℹ️ MainMenuSceneManagerSetup: UniversalSceneManager bulunamadı, yeni oyun başladığında oluşturulacak.");
        }
    }

    /// <summary>
    /// Yeni oyun başlatıldığında UniversalSceneManager'ı hazırla
    /// </summary>
    public void YeniOyunIcinHazirla()
    {
        // Eğer UniversalSceneManager yoksa oluştur
        if (UniversalSceneManager.Instance == null)
        {
            KurulumYap();
        }
        else
        {
            // Varsa sadece sıfırla
            SifirlaOynananSahneler();
        }
    }

    public void KurulumYap()
    {
        // Zaten var mı kontrol et
        if (UniversalSceneManager.Instance != null)
        {
            Debug.Log("✅ MainMenuSceneManagerSetup: UniversalSceneManager zaten mevcut.");
            return;
        }

        // NetworkManager var mı kontrol et (ana menüde olmayabilir)
        if (NetworkManager.Singleton == null)
        {
            Debug.Log("ℹ️ MainMenuSceneManagerSetup: NetworkManager yok, offline modda UniversalSceneManager oluşturuluyor.");
            OlusturOfflineUniversalSceneManager();
            return;
        }

        // Online modda oluştur
        OlusturOnlineUniversalSceneManager();
    }

    private void OlusturOfflineUniversalSceneManager()
    {
        // Offline mod için basit GameObject oluştur
        GameObject managerObj = new GameObject("UniversalSceneManager");
        
        // UniversalSceneManager ekle (NetworkObject olmadan)
        UniversalSceneManager manager = managerObj.AddComponent<UniversalSceneManager>();
        
        // Ayarları yap
        manager.miniOyunSahneleri = miniOyunSahneleri;
        manager.anaMenuSahnesi = anaMenuSahnesi;
        manager.sahneGecisiBeklemeSuresi = sahneGecisiBeklemeSuresi;
        
        // DontDestroyOnLoad yap
        DontDestroyOnLoad(managerObj);
        
        Debug.Log("✅ MainMenuSceneManagerSetup: UniversalSceneManager offline modda oluşturuldu.");
    }

    private void OlusturOnlineUniversalSceneManager()
    {
        // Online mod için NetworkObject ile oluştur
        GameObject managerObj = new GameObject("UniversalSceneManager");
        
        // NetworkObject ekle
        NetworkObject networkObject = managerObj.AddComponent<NetworkObject>();
        networkObject.DontDestroyWithOwner = true;
        
        // UniversalSceneManager ekle
        UniversalSceneManager manager = managerObj.AddComponent<UniversalSceneManager>();
        
        // Ayarları yap
        manager.miniOyunSahneleri = miniOyunSahneleri;
        manager.anaMenuSahnesi = anaMenuSahnesi;
        manager.sahneGecisiBeklemeSuresi = sahneGecisiBeklemeSuresi;
        
        // NetworkObject'i spawn et (sadece server'da)
        if (NetworkManager.Singleton.IsServer)
        {
            networkObject.Spawn(true);
            Debug.Log("✅ MainMenuSceneManagerSetup: UniversalSceneManager online modda oluşturuldu ve spawn edildi.");
        }
        else
        {
            Debug.Log("✅ MainMenuSceneManagerSetup: UniversalSceneManager online modda oluşturuldu (client).");
        }
    }

    [ContextMenu("Manuel Kurulum")]
    public void ManuelKurulum()
    {
        KurulumYap();
    }

    [ContextMenu("Oynanan Sahneleri Sıfırla")]
    public void ManuelSifirlama()
    {
        SifirlaOynananSahneler();
    }

    [ContextMenu("UniversalSceneManager Durumunu Kontrol Et")]
    public void KontrolEt()
    {
        Debug.Log("=== MAIN MENU SCENE MANAGER DURUM KONTROLÜ ===");

        if (UniversalSceneManager.Instance != null)
        {
            Debug.Log("✅ UniversalSceneManager: Mevcut");
        }
        else
        {
            Debug.LogWarning("❌ UniversalSceneManager: Bulunamadı!");
        }
    }
}
