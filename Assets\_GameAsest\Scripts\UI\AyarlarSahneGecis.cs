using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

/// <summary>
/// Ana menüden ayarlar sahnesine geçiş için kull<PERSON>lan script
/// </summary>
public class AyarlarSahneGecis : MonoBehaviour
{
    [Header("Sahne Ayarları")]
    [Tooltip("Ayarlar sahnesinin adı")]
    public string ayarlarSahneAdi = "Settings";
    
    [Tooltip("Ayarlar butonu")]
    public Button ayarlarButonu;

    void Start()
    {
        // Buton event'ini bağla
        if (ayarlarButonu != null)
        {
            ayarlarButonu.onClick.AddListener(AyarlarSahnesineGec);
        }
        else
        {
            // Eğer buton atanmamışsa, bu GameObject'in butonunu kullan
            Button buton = GetComponent<Button>();
            if (buton != null)
            {
                buton.onClick.AddListener(AyarlarSahnesineGec);
            }
        }
    }

    /// <summary>
    /// Ayarlar sahnesine geçiş yapar
    /// </summary>
    public void AyarlarSahnesineGec()
    {
        if (!string.IsNullOrEmpty(ayarlarSahneAdi))
        {
            SceneManager.LoadScene(ayarlarSahneAdi);
        }
        else
        {
            Debug.LogError("AyarlarSahneGecis: Ayarlar sahne adı belirtilmemiş!");
        }
    }

    /// <summary>
    /// Component destroy edildiğinde event listener'ı temizle
    /// </summary>
    private void OnDestroy()
    {
        if (ayarlarButonu != null)
        {
            ayarlarButonu.onClick.RemoveListener(AyarlarSahnesineGec);
        }
    }
}
