using UnityEngine;
using UnityEngine.EventSystems;

public class CameraLookPanelHandler : <PERSON>o<PERSON><PERSON><PERSON>our, IPointerDownHandler, IDragHandler
{
    // Bu script başka bir ayara ihtiyaç duymaz.

    public void OnPointerDown(PointerEventData eventData)
    {
        // Bu metot, dokunma başladığında tetiklenir.
        // Gerekirse burada işlem yapabilirsiniz ama şu an için boş kalabilir.
    }

    public void OnDrag(PointerEventData eventData)
    {
        // Panel üzerinde parmak sürüklendiğinde her frame bu metot çalışır.
        
        // Singleton instance'ı kullanarak ana kamera script'imize ulaşıyoruz.
        if (InstantHybridCameraController.instance != null)
        {
            // Ana script'teki yeni public metodumuzu çağırarak
            // parmağın ne kadar hareket ettiğini (eventData.delta) iletiyoruz.
            InstantHybridCameraController.instance.RotateCameraFromTouch(eventData.delta);
        }
    }
}