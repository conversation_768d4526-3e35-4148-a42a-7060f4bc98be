fileFormatVersion: 2
guid: f6bad70f304752040b4a6c1b506a168a
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable: []
  externalObjects: {}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: mixamo.com
      takeName: mixamo.com
      internalID: -203655887218126122
      firstFrame: 0
      lastFrame: 27
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: mixamorig:Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Kral(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: temp
      parentName: Kral(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.00000003774895, y: 0, z: -0, w: 1}
      scale: {x: 100, y: 100, z: 100}
    - name: mixamorig:Hips
      parentName: Kral(Clone)
      position: {x: -0.0000019283132, y: 0.70382154, z: 0.011322194}
      rotation: {x: 0.000000007762468, y: -0.0000020380044, z: -0.0000021814044, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine
      parentName: mixamorig:Hips
      position: {x: -0, y: 0.11950489, z: -0.0010714197}
      rotation: {x: -0.0044826195, y: 0.0000020282048, z: 0.0000021905182, w: 0.99999}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine1
      parentName: mixamorig:Spine
      position: {x: -0, y: 0.13942797, z: 5.178089e-12}
      rotation: {x: 3.3231037e-26, y: -4.8746495e-14, z: 6.817113e-13, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Spine2
      parentName: mixamorig:Spine1
      position: {x: -0, y: 0.15934633, z: -5.604077e-12}
      rotation: {x: 0, y: 0, z: 3.899931e-29, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Neck
      parentName: mixamorig:Spine2
      position: {x: -0, y: 0.17926455, z: -0.0000000017018824}
      rotation: {x: 0.0044826036, y: 4.161327e-14, z: 2.275625e-13, w: 0.99999}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Head
      parentName: mixamorig:Neck
      position: {x: -0, y: 0.14495176, z: 0.010371685}
      rotation: {x: 0, y: 0, z: 2.3216012e-36, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:HeadTop_End
      parentName: mixamorig:Head
      position: {x: -0, y: 0.5402693, z: 0.038657706}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftShoulder
      parentName: mixamorig:Spine2
      position: {x: -0.09041189, y: 0.15170942, z: 0.0017939344}
      rotation: {x: 0.57440907, y: -0.41590706, z: 0.5630808, w: 0.42428246}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftArm
      parentName: mixamorig:LeftShoulder
      position: {x: -1.2453952e-10, y: 0.18827666, z: -0.000000002981062}
      rotation: {x: -0.14855523, y: -0.0076036416, z: 0.011216029, w: 0.9888113}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftForeArm
      parentName: mixamorig:LeftArm
      position: {x: 5.186316e-10, y: 0.20994693, z: 6.5587e-12}
      rotation: {x: 0.00000023162444, y: 0.0004537093, z: -0.000000102679934, w: 0.99999994}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHand
      parentName: mixamorig:LeftForeArm
      position: {x: -1.0776044e-10, y: 0.23778643, z: -6.2637183e-12}
      rotation: {x: 0.0066213217, y: 0.015093349, z: -0.024264978, w: 0.9995697}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle1
      parentName: mixamorig:LeftHand
      position: {x: -0.006225412, y: 0.1286959, z: -0.001800192}
      rotation: {x: -0.007060787, y: 0.003088093, z: 0.024347244, w: 0.9996739}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle2
      parentName: mixamorig:LeftHandMiddle1
      position: {x: 0.000019325676, y: 0.04588324, z: 1.4915198e-10}
      rotation: {x: 0.00000020801079, y: 0.0000073260226, z: 0.00033043337, w: 0.99999994}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle3
      parentName: mixamorig:LeftHandMiddle2
      position: {x: 0.00004698007, y: 0.043719176, z: -1.3028703e-10}
      rotation: {x: -0.0000002873609, y: -0.000032033055, z: -0.001493037, w: 0.9999989}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle4
      parentName: mixamorig:LeftHandMiddle3
      position: {x: -0.00006630556, y: 0.034536384, z: -4.4935597e-10}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing1
      parentName: mixamorig:LeftHand
      position: {x: -0.04456451, y: 0.124357305, z: -0.00033841206}
      rotation: {x: -0.0076248394, y: 0.022681447, z: 0.019993592, w: 0.99951375}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing2
      parentName: mixamorig:LeftHandRing1
      position: {x: -0.00020031315, y: 0.024966551, z: 5.7022476e-10}
      rotation: {x: -0.00000031010288, y: 0.00009847788, z: 0.0037492702, w: 0.99999297}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing3
      parentName: mixamorig:LeftHandRing2
      position: {x: -0.000010540971, y: 0.021495914, z: -5.894151e-10}
      rotation: {x: -0.0000039746988, y: -0.0005256857, z: 0.008669545, w: 0.9999623}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing4
      parentName: mixamorig:LeftHandRing3
      position: {x: 0.00021085578, y: 0.012537707, z: -6.051024e-11}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb1
      parentName: mixamorig:LeftHand
      position: {x: 0.04045648, y: 0.04190194, z: 0.006489223}
      rotation: {x: 0.05938193, y: -0.03908235, z: -0.40574378, w: 0.91121805}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb2
      parentName: mixamorig:LeftHandThumb1
      position: {x: -0.007904108, y: 0.04621337, z: -1.634935e-10}
      rotation: {x: 0.00015184977, y: 0.0044843927, z: 0.20187546, w: 0.97940093}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb3
      parentName: mixamorig:LeftHandThumb2
      position: {x: 0.009604683, y: 0.03973853, z: -1.6690556e-10}
      rotation: {x: -0.00026048147, y: -0.0028226846, z: -0.14467543, w: 0.98947513}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandThumb4
      parentName: mixamorig:LeftHandThumb3
      position: {x: -0.0017005733, y: 0.031933423, z: -6.0120964e-10}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex1
      parentName: mixamorig:LeftHand
      position: {x: 0.050789922, y: 0.10859663, z: 0.001635087}
      rotation: {x: -0.007023584, y: 0.0015890177, z: 0.024896922, w: 0.9996641}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex2
      parentName: mixamorig:LeftHandIndex1
      position: {x: 0.000076078504, y: 0.05089497, z: -1.3054834e-10}
      rotation: {x: -0.00000026184995, y: 0.000007306646, z: -0.00088468206, w: 0.99999964}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex3
      parentName: mixamorig:LeftHandIndex2
      position: {x: -0.000014117441, y: 0.050675213, z: 3.8144952e-11}
      rotation: {x: -0.00000007927127, y: -0.000021852478, z: -0.0006218555, w: 0.9999998}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex4
      parentName: mixamorig:LeftHandIndex3
      position: {x: -0.0000619604, y: 0.04079102, z: 6.243205e-11}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightShoulder
      parentName: mixamorig:Spine2
      position: {x: 0.09041189, y: 0.15170108, z: 0.002725453}
      rotation: {x: 0.57731444, y: 0.4136996, z: -0.5601077, w: 0.42642713}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightArm
      parentName: mixamorig:RightShoulder
      position: {x: 2.4780453e-10, y: 0.18832628, z: 0.0000000025862363}
      rotation: {x: -0.14837885, y: 0.014323206, z: -0.017455151, w: 0.9886728}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightForeArm
      parentName: mixamorig:RightArm
      position: {x: 7.468582e-10, y: 0.21048763, z: 8.030817e-11}
      rotation: {x: -0.00000018808754, y: -0.0010347943, z: 0.0000003084263, w: 0.99999946}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHand
      parentName: mixamorig:RightForeArm
      position: {x: 4.3916443e-10, y: 0.2381049, z: 1.050104e-11}
      rotation: {x: 0.010608093, y: -0.011213089, z: 0.013393795, w: 0.99979115}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle1
      parentName: mixamorig:RightHand
      position: {x: 0.0039085513, y: 0.14717571, z: -0.0031676458}
      rotation: {x: -0.010749645, y: 0.0005823181, z: -0.012155318, w: 0.9998682}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle2
      parentName: mixamorig:RightHandMiddle1
      position: {x: 0.0000852316, y: 0.03776499, z: -1.2042634e-12}
      rotation: {x: -0.0000005200079, y: -0.000019432324, z: -0.00047811941, w: 0.9999999}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle3
      parentName: mixamorig:RightHandMiddle2
      position: {x: 0.000048209753, y: 0.03708331, z: 1.2344543e-11}
      rotation: {x: 0.00000006479841, y: 0.00010601412, z: -0.0028893535, w: 0.9999958}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle4
      parentName: mixamorig:RightHandMiddle3
      position: {x: -0.00013344153, y: 0.029660089, z: 1.1695768e-10}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex1
      parentName: mixamorig:RightHand
      position: {x: -0.050692957, y: 0.116602235, z: 0.0021042733}
      rotation: {x: -0.01074604, y: 0.000874411, z: -0.013444091, w: 0.9998515}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex2
      parentName: mixamorig:RightHandIndex1
      position: {x: -0.000016332513, y: 0.049954344, z: 8.438057e-11}
      rotation: {x: 0.00000016350388, y: 0.0000013113366, z: 0.000076264136, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex3
      parentName: mixamorig:RightHandIndex2
      position: {x: -0.000008308441, y: 0.047733568, z: 1.4912327e-10}
      rotation: {x: 0.00000010641744, y: 0.00000019374154, z: 0.00043830276, w: 0.99999994}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex4
      parentName: mixamorig:RightHandIndex3
      position: {x: 0.00002464097, y: 0.035261977, z: 4.1679017e-12}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing1
      parentName: mixamorig:RightHand
      position: {x: 0.04678442, y: 0.12821198, z: 0.0022112231}
      rotation: {x: -0.010809808, y: -0.0038102802, z: -0.01284863, w: 0.9998518}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing2
      parentName: mixamorig:RightHandRing1
      position: {x: 0.000017300401, y: 0.022128617, z: 3.1467262e-10}
      rotation: {x: 0.00000019258357, y: -0.000005080142, z: -0.00048704445, w: 0.9999999}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing3
      parentName: mixamorig:RightHandRing2
      position: {x: -0.0000054763077, y: 0.024265328, z: -2.3816485e-10}
      rotation: {x: 0.00000015833977, y: 0.00001686861, z: -0.00021681185, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing4
      parentName: mixamorig:RightHandRing3
      position: {x: -0.0000118250355, y: 0.018758425, z: 4.1056224e-11}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb1
      parentName: mixamorig:RightHand
      position: {x: -0.03706971, y: 0.04392287, z: 0.0051951576}
      rotation: {x: 0.05319728, y: 0.023362113, z: 0.4328175, w: 0.89960724}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb2
      parentName: mixamorig:RightHandThumb1
      position: {x: 0.009480308, y: 0.045183614, z: 2.1819097e-11}
      rotation: {x: 0.000065422464, y: 0.0033625462, z: -0.18618281, w: 0.9825094}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb3
      parentName: mixamorig:RightHandThumb2
      position: {x: -0.007518871, y: 0.044403337, z: 3.4758607e-10}
      rotation: {x: -0.00009015396, y: 0.00083511055, z: 0.060521718, w: 0.99816656}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb4
      parentName: mixamorig:RightHandThumb3
      position: {x: -0.0019614322, y: 0.042027242, z: 0.0000000024475273}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftUpLeg
      parentName: mixamorig:Hips
      position: {x: -0.14543502, y: -0.06638861, z: -0.013612945}
      rotation: {x: -0.0005409987, y: 0.02915979, z: 0.9995181, w: 0.010633985}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftLeg
      parentName: mixamorig:LeftUpLeg
      position: {x: -2.1768222e-10, y: 0.26906115, z: 4.7964323e-11}
      rotation: {x: -0.12130158, y: -0.0024951552, z: 0.026895145, w: 0.9922482}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftFoot
      parentName: mixamorig:LeftLeg
      position: {x: -7.014721e-11, y: 0.17778113, z: 7.3321585e-11}
      rotation: {x: 0.50118387, y: -0.022892186, z: -0.0150548825, w: 0.864907}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftToeBase
      parentName: mixamorig:LeftFoot
      position: {x: 1.2270515e-10, y: 0.2979107, z: -0.0000000052661275}
      rotation: {x: 0.34109628, y: -0.010698332, z: 0.0038822547, w: 0.93995947}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftToe_End
      parentName: mixamorig:LeftToeBase
      position: {x: 1.08811e-10, y: 0.12899046, z: -1.9730209e-11}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightUpLeg
      parentName: mixamorig:Hips
      position: {x: 0.14543502, y: -0.06638861, z: -0.012578432}
      rotation: {x: 0.0005942105, y: 0.02902988, z: 0.99952143, w: -0.010669784}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightLeg
      parentName: mixamorig:RightUpLeg
      position: {x: 1.2130683e-10, y: 0.2690687, z: 2.9525627e-12}
      rotation: {x: -0.123816796, y: 0.0026032575, z: -0.026952129, w: 0.9919356}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightFoot
      parentName: mixamorig:RightLeg
      position: {x: 1.210493e-10, y: 0.17794807, z: -3.574243e-11}
      rotation: {x: 0.50212187, y: 0.02249993, z: 0.014878723, w: 0.8643761}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightToeBase
      parentName: mixamorig:RightFoot
      position: {x: 4.2889545e-10, y: 0.29682633, z: -0.000000006881188}
      rotation: {x: 0.34253484, y: 0.011175628, z: -0.004074856, w: 0.9394299}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightToe_End
      parentName: mixamorig:RightToeBase
      position: {x: 2.0194703e-10, y: 0.12833335, z: 3.805894e-11}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
