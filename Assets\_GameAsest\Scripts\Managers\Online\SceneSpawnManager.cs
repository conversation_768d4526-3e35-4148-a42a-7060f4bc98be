using UnityEngine;
using Unity.Netcode;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Her sahnede bulunan spawn yöneticisi
/// Sahne yüklendiğinde otomatik olarak oyuncuları spawn point'lere teleport eder
/// </summary>
public class SceneSpawnManager : NetworkBehaviour
{
    public static SceneSpawnManager Instance { get; private set; }

    [Header("Spawn Ayarları")]
    [Tooltip("Sahne yüklendikten sonra kaç saniye beklensin")]
    public float spawnDelay = 1f;

    [Tooltip("Spawn point'ler otomatik bulunacak mı?")]
    public bool autoFindSpawnPoints = true;

    [Tooltip("Manuel spawn point listesi (autoFindSpawnPoints false ise)")]
    public Transform[] manualSpawnPoints;

    [Header("Debug")]
    public bool showDebugLogs = true;

    private List<Transform> availableSpawnPoints = new List<Transform>();
    private bool hasSpawnedPlayers = false;

    private void Awake()
    {
        // Singleton pattern
        if (Instance != null && Instance != this)
        {
            if (NetworkManager.Singleton != null && NetworkManager.Singleton.IsListening)
            {
                if (IsServer)
                {
                    Destroy(gameObject);
                }
                else
                {
                    Instance = this;
                }
            }
            else
            {
                Destroy(gameObject);
            }
        }
        else
        {
            Instance = this;
        }
    }

    public override void OnNetworkSpawn()
    {


        if (IsServer)
        {
            // Spawn point'leri bul
            FindSpawnPoints();

            // Kısa bir gecikme sonrası oyuncuları spawn et
            StartCoroutine(DelayedPlayerSpawn());
        }
    }

    private void FindSpawnPoints()
    {
        availableSpawnPoints.Clear();

        if (autoFindSpawnPoints)
        {
            // SpawnPoint tag'li objeleri bul
            GameObject[] spawnPointObjects = GameObject.FindGameObjectsWithTag("SpawnPoint");
            
            foreach (GameObject obj in spawnPointObjects)
            {
                SpawnPoint spawnPoint = obj.GetComponent<SpawnPoint>();
                if (spawnPoint != null && spawnPoint.CanSpawn())
                {
                    availableSpawnPoints.Add(obj.transform);
                }
                else
                {
                    // SpawnPoint component'i yoksa da ekle
                    availableSpawnPoints.Add(obj.transform);
                }
            }

            if (showDebugLogs)
                Debug.Log($"🎯 SceneSpawnManager: {availableSpawnPoints.Count} otomatik spawn point bulundu");
        }
        else
        {
            // Manuel spawn point'leri kullan
            if (manualSpawnPoints != null)
            {
                availableSpawnPoints.AddRange(manualSpawnPoints.Where(sp => sp != null));
            }

            if (showDebugLogs)
                Debug.Log($"🎯 SceneSpawnManager: {availableSpawnPoints.Count} manuel spawn point kullanılıyor");
        }

        // Spawn point'leri önceliğe göre sırala (varsa)
        availableSpawnPoints = availableSpawnPoints.OrderByDescending(sp => {
            SpawnPoint spawnPoint = sp.GetComponent<SpawnPoint>();
            return spawnPoint != null ? spawnPoint.priority : 0;
        }).ToList();
    }

    private IEnumerator DelayedPlayerSpawn()
    {
        // Sahne tamamen yüklenene kadar bekle
        yield return new WaitForSeconds(spawnDelay);

        if (availableSpawnPoints.Count == 0)
        {
            Debug.LogWarning("⚠️ SceneSpawnManager: Hiç spawn point bulunamadı!");
            yield break;
        }

        if (hasSpawnedPlayers)
        {
            if (showDebugLogs)
                Debug.Log("🎯 SceneSpawnManager: Oyuncular zaten spawn edilmiş, tekrar spawn atlanıyor");
            yield break;
        }

        hasSpawnedPlayers = true;

        if (showDebugLogs)
            Debug.Log($"🎯 SceneSpawnManager: {availableSpawnPoints.Count} spawn point ile oyuncu teleport başlıyor...");

        // Tüm oyuncuları spawn point'lere teleport et
        TeleportAllPlayersToSpawnPoints();
    }

    private void TeleportAllPlayersToSpawnPoints()
    {
        if (!IsServer) return;

        int spawnIndex = 0;
        var processedClients = new HashSet<ulong>();

        foreach (var client in NetworkManager.Singleton.ConnectedClients)
        {
            // Duplicate client kontrolü
            if (processedClients.Contains(client.Key))
            {
                if (showDebugLogs)
                    Debug.LogWarning($"⚠️ SceneSpawnManager: Client {client.Key} zaten işlendi, atlanıyor");
                continue;
            }

            if (client.Value.PlayerObject != null)
            {
                var oyuncu = client.Value.PlayerObject.GetComponent<OyuncuKontrol>();
                if (oyuncu != null)
                {
                    Transform spawnPoint = availableSpawnPoints[spawnIndex % availableSpawnPoints.Count];
                    oyuncu.Teleport(spawnPoint.position, spawnPoint.rotation);

                    // SpawnPoint component'i varsa bilgilendir
                    SpawnPoint spawnPointComponent = spawnPoint.GetComponent<SpawnPoint>();
                    if (spawnPointComponent != null)
                    {
                        spawnPointComponent.OnPlayerSpawned();
                    }

                    if (showDebugLogs)
                        Debug.Log($"🎯 SceneSpawnManager: Client {client.Key} teleport edildi - {spawnPoint.name} ({spawnPoint.position})");

                    processedClients.Add(client.Key);
                    spawnIndex++;
                }
            }
        }

        if (showDebugLogs)
            Debug.Log($"✅ SceneSpawnManager: {spawnIndex} oyuncu spawn point'lere teleport edildi!");
    }

    /// <summary>
    /// Manuel olarak oyuncuları yeniden spawn etmek için
    /// </summary>
    [ContextMenu("Manuel Oyuncu Spawn")]
    public void ManualRespawnPlayers()
    {
        if (!IsServer)
        {
            Debug.LogWarning("⚠️ SceneSpawnManager: Manuel spawn sadece server'da çalışır!");
            return;
        }

        hasSpawnedPlayers = false;
        FindSpawnPoints();
        StartCoroutine(DelayedPlayerSpawn());
    }

    /// <summary>
    /// Spawn point'leri yeniden bulmak için
    /// </summary>
    [ContextMenu("Spawn Point'leri Yenile")]
    public void RefreshSpawnPoints()
    {
        FindSpawnPoints();
        
        Debug.Log("=== SPAWN POINT LİSTESİ ===");
        for (int i = 0; i < availableSpawnPoints.Count; i++)
        {
            Transform sp = availableSpawnPoints[i];
            SpawnPoint component = sp.GetComponent<SpawnPoint>();
            string info = component != null ? $"Priority: {component.priority}" : "No SpawnPoint component";
            Debug.Log($"  {i}: {sp.name} - {info} - Pos: {sp.position}");
        }
    }

    public override void OnNetworkDespawn()
    {
        if (Instance == this)
        {
            Instance = null;
        }
    }
}
