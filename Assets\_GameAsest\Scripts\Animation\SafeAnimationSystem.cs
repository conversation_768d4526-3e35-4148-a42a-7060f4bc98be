using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Güvenli animasyon sistemi - LeanTween'e alternatif
/// Coroutine tabanlı, proper cleanup ile
/// </summary>
public class SafeAnimationSystem : MonoBehaviour
{
    private static SafeAnimationSystem instance;
    private static bool isQuitting = false;

    public static SafeAnimationSystem Instance
    {
        get
        {
            if (instance == null && !isQuitting)
            {
                GameObject go = new GameObject("SafeAnimationSystem");
                instance = go.AddComponent<SafeAnimationSystem>();
                DontDestroyOnLoad(go);
            }
            return instance;
        }
    }

    // Aktif animasyonları takip et
    private Dictionary<GameObject, List<Coroutine>> activeAnimations = new Dictionary<GameObject, List<Coroutine>>();

    private void Awake()
    {
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);

            // Application quitting event'ine abone ol
            Application.quitting += OnApplicationQuitting;
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    private void OnApplicationQuitting()
    {
        isQuitting = true;
        CancelAllAnimations();
    }

    /// <summary>
    /// Scale animasyonu - LeanTween.scale yerine
    /// </summary>
    public Coroutine AnimateScale(GameObject target, Vector3 targetScale, float duration, System.Action onComplete = null, EaseType easeType = EaseType.Linear)
    {
        if (target == null) return null;

        var coroutine = StartCoroutine(ScaleCoroutine(target, targetScale, duration, onComplete, easeType));
        AddActiveAnimation(target, coroutine);
        return coroutine;
    }

    /// <summary>
    /// Pulse animasyonu - Scale up sonra down
    /// </summary>
    public Coroutine AnimatePulse(GameObject target, float pulseScale = 1.2f, float duration = 0.1f, System.Action onComplete = null)
    {
        if (target == null) return null;

        var coroutine = StartCoroutine(PulseCoroutine(target, pulseScale, duration, onComplete));
        AddActiveAnimation(target, coroutine);
        return coroutine;
    }

    /// <summary>
    /// Shake animasyonu - Countdown text için
    /// </summary>
    public Coroutine AnimateShake(GameObject target, float intensity = 0.1f, float duration = 0.2f, System.Action onComplete = null)
    {
        if (target == null) return null;

        var coroutine = StartCoroutine(ShakeCoroutine(target, intensity, duration, onComplete));
        AddActiveAnimation(target, coroutine);
        return coroutine;
    }

    /// <summary>
    /// Belirli bir GameObject'in tüm animasyonlarını durdur
    /// </summary>
    public void CancelAnimations(GameObject target)
    {
        if (target == null || !activeAnimations.ContainsKey(target)) return;

        foreach (var coroutine in activeAnimations[target])
        {
            if (coroutine != null)
                StopCoroutine(coroutine);
        }

        activeAnimations[target].Clear();
    }

    /// <summary>
    /// Tüm animasyonları durdur
    /// </summary>
    public void CancelAllAnimations()
    {
        StopAllCoroutines();
        activeAnimations.Clear();
    }

    /// <summary>
    /// Güvenli Instance erişimi - OnDestroy sırasında kullanım için
    /// </summary>
    public static bool TryCancelAnimations(GameObject target)
    {
        if (instance != null && !isQuitting && target != null)
        {
            instance.CancelAnimations(target);
            return true;
        }
        return false;
    }

    /// <summary>
    /// Güvenli Instance erişimi - Tüm animasyonları iptal etmek için
    /// </summary>
    public static bool TryCancelAllAnimations()
    {
        if (instance != null && !isQuitting)
        {
            instance.CancelAllAnimations();
            return true;
        }
        return false;
    }

    private void AddActiveAnimation(GameObject target, Coroutine coroutine)
    {
        if (!activeAnimations.ContainsKey(target))
            activeAnimations[target] = new List<Coroutine>();

        activeAnimations[target].Add(coroutine);
    }

    private void RemoveActiveAnimation(GameObject target, Coroutine coroutine)
    {
        if (activeAnimations.ContainsKey(target))
        {
            activeAnimations[target].Remove(coroutine);
            if (activeAnimations[target].Count == 0)
                activeAnimations.Remove(target);
        }
    }

    private IEnumerator ScaleCoroutine(GameObject target, Vector3 targetScale, float duration, System.Action onComplete, EaseType easeType)
    {
        if (target == null) yield break;

        Vector3 startScale = target.transform.localScale;
        float elapsed = 0f;

        while (elapsed < duration && target != null)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            
            // Easing uygula
            t = ApplyEasing(t, easeType);
            
            target.transform.localScale = Vector3.Lerp(startScale, targetScale, t);
            yield return null;
        }

        if (target != null)
        {
            target.transform.localScale = targetScale;
            onComplete?.Invoke();
        }

        // Animasyonu listeden çıkar
        RemoveActiveAnimation(target, null);
    }

    private IEnumerator PulseCoroutine(GameObject target, float pulseScale, float duration, System.Action onComplete)
    {
        if (target == null) yield break;

        Vector3 originalScale = target.transform.localScale;
        Vector3 targetScale = originalScale * pulseScale;

        // Scale up
        yield return StartCoroutine(ScaleCoroutine(target, targetScale, duration, null, EaseType.EaseOutBack));
        
        // Scale down
        if (target != null)
            yield return StartCoroutine(ScaleCoroutine(target, originalScale, duration, null, EaseType.EaseInBack));

        onComplete?.Invoke();
        RemoveActiveAnimation(target, null);
    }

    private IEnumerator ShakeCoroutine(GameObject target, float intensity, float duration, System.Action onComplete)
    {
        if (target == null) yield break;

        Vector3 originalPosition = target.transform.localPosition;
        float elapsed = 0f;

        while (elapsed < duration && target != null)
        {
            elapsed += Time.deltaTime;
            float strength = intensity * (1f - elapsed / duration); // Azalan şiddet
            
            Vector3 randomOffset = new Vector3(
                Random.Range(-strength, strength),
                Random.Range(-strength, strength),
                0f
            );
            
            target.transform.localPosition = originalPosition + randomOffset;
            yield return null;
        }

        if (target != null)
        {
            target.transform.localPosition = originalPosition;
            onComplete?.Invoke();
        }

        RemoveActiveAnimation(target, null);
    }

    private float ApplyEasing(float t, EaseType easeType)
    {
        switch (easeType)
        {
            case EaseType.EaseInBack:
                return t * t * (2.7f * t - 1.7f);
            case EaseType.EaseOutBack:
                t -= 1f;
                return 1f + t * t * (2.7f * t + 1.7f);
            case EaseType.EaseInQuad:
                return t * t;
            case EaseType.EaseOutQuad:
                return 1f - (1f - t) * (1f - t);
            default:
                return t; // Linear
        }
    }

    private void OnDestroy()
    {
        isQuitting = true;
        CancelAllAnimations();

        // Event'ten abone olmayı kaldır
        Application.quitting -= OnApplicationQuitting;

        if (instance == this)
            instance = null;
    }
}

/// <summary>
/// Easing türleri
/// </summary>
public enum EaseType
{
    Linear,
    EaseInBack,
    EaseOutBack,
    EaseInQuad,
    EaseOutQuad
}
