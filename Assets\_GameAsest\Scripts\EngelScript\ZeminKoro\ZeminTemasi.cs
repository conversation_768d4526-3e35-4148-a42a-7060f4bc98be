// ZeminTemasi.cs - YENİ HALİ

using UnityEngine;

[System.Serializable]
public class ZeminTemasi
{
    public string temaAdi = "Yeni Tema";
    
    // --- DEĞİŞİKLİK BURADA ---
    // Artık 3 ayrı materyal yerine TEK BİR ana materyal tutuyoruz.
    public Material anaMateryal; 

    // Ve 3 ayrı renk değeri tutuyoruz.
    [<PERSON>er("<PERSON><PERSON> Renkleri")]
    [Tooltip("Henüz açılmamış karoların rengi")]
    public Color gizliRenk = new Color(0.2f, 0.5f, 1f, 1f); // Mavi

    [Tooltip("Güvenli karoların rengi")]
    public Color guvenliRenk = new Color(0f, 1f, 0f, 1f); // Yeşil

    [Tooltip("Tuzak karoların rengi")]
    public Color tuzakRenk = new Color(1f, 0f, 0f, 1f); // Kırmızı
}