using UnityEngine;
using Unity.Netcode;
using System.Collections.Generic;

/// <summary>
/// Doğru/Yanlış blok sistemini yöneten manager
/// Blokları otomatik olarak rastgele doğru/yanl<PERSON>ş yapar
/// </summary>
public class DogruYanlisBlokManager : NetworkBehaviour
{
    [Header("Blok Yönetimi")]
    [SerializeField] private DogruYanlisBlok[] tumBloklar; // Sahnedeki tüm bloklar
    [SerializeField] private float dogruBlokOrani = 0.5f; // %50 doğru blok
    [SerializeField] private bool otomatikKaristir = true; // Oyun başında otomatik karıştır
    
    [Header("Oyun Ayarları")]
    [SerializeField] private int minimumDogruBlokSayisi = 1; // En az kaç doğru blok olmalı
    [SerializeField] private bool oyunBasindaKaristir = true;
    
    private NetworkVariable<int> aktifOyuncuSayisi = new NetworkVariable<int>(0, 
        NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);
    
    public override void OnNetworkSpawn()
    {
        if (IsServer)
        {
            // Sahnedeki tüm blokları bul
            if (tumBloklar == null || tumBloklar.Length == 0)
            {
                tumBloklar = FindObjectsByType<DogruYanlisBlok>(FindObjectsSortMode.None);
            }
            
            if (oyunBasindaKaristir)
            {
                // Oyun başında blokları karıştır
                Invoke(nameof(BloklariKaristir), 1f); // 1 saniye gecikme ile
            }
        }
    }
    
    /// <summary>
    /// Blokları rastgele doğru/yanlış yapar
    /// </summary>
    [ContextMenu("Blokları Karıştır")]
    public void BloklariKaristir()
    {
        if (!IsServer) return;
        
        if (tumBloklar == null || tumBloklar.Length == 0)
        {
            Debug.LogWarning("Karıştırılacak blok bulunamadı!");
            return;
        }
        
        // Önce tüm blokları yanlış yap
        List<DogruYanlisBlok> blokListesi = new List<DogruYanlisBlok>(tumBloklar);
        
        // Doğru blok sayısını hesapla
        int dogruBlokSayisi = Mathf.Max(minimumDogruBlokSayisi, 
            Mathf.RoundToInt(tumBloklar.Length * dogruBlokOrani));
        
        // Rastgele doğru blokları seç
        for (int i = 0; i < dogruBlokSayisi && blokListesi.Count > 0; i++)
        {
            int rastgeleIndex = Random.Range(0, blokListesi.Count);
            DogruYanlisBlok secilenBlok = blokListesi[rastgeleIndex];
            
            // Bu bloğu doğru yap
            BlokTipiniAyarlaClientRpc(secilenBlok.GetComponent<NetworkObject>().NetworkObjectId, true);
            
            // Listeden çıkar
            blokListesi.RemoveAt(rastgeleIndex);
        }
        
        // Kalan blokları yanlış yap
        foreach (var blok in blokListesi)
        {
            BlokTipiniAyarlaClientRpc(blok.GetComponent<NetworkObject>().NetworkObjectId, false);
        }
        
        Debug.Log($"🎲 Bloklar karıştırıldı! {dogruBlokSayisi} doğru, {tumBloklar.Length - dogruBlokSayisi} yanlış blok.");
    }
    
    [ClientRpc]
    private void BlokTipiniAyarlaClientRpc(ulong networkObjectId, bool dogruBlok)
    {
        // NetworkObject ID'si ile bloğu bul
        if (NetworkManager.Singleton.SpawnManager.SpawnedObjects.TryGetValue(networkObjectId, out NetworkObject networkObject))
        {
            DogruYanlisBlok blok = networkObject.GetComponent<DogruYanlisBlok>();
            if (blok != null)
            {
                // Reflection kullanarak private field'ı değiştir
                var field = typeof(DogruYanlisBlok).GetField("dogruBlok", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (field != null)
                {
                    field.SetValue(blok, dogruBlok);
                    
                    // Materyali güncelle
                    if (dogruBlok)
                    {
                        blok.DogruBlokYap();
                    }
                    else
                    {
                        blok.YanlisBlokYap();
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// Tüm blokları sıfırlar (yeniden kullanım için)
    /// </summary>
    [ContextMenu("Blokları Sıfırla")]
    public void BloklariSifirla()
    {
        if (!IsServer) return;
        
        BloklariSifirlaClientRpc();
    }
    
    [ClientRpc]
    private void BloklariSifirlaClientRpc()
    {
        foreach (var blok in tumBloklar)
        {
            if (blok != null)
            {
                // Bloğu sıfırla (private method'u çağırmak için reflection)
                var method = typeof(DogruYanlisBlok).GetMethod("BlokuSifirla", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (method != null)
                {
                    method.Invoke(blok, null);
                }
            }
        }
        
        Debug.Log("🔄 Tüm bloklar sıfırlandı!");
    }
    
    /// <summary>
    /// Yeni oyun turu başlatır
    /// </summary>
    [ContextMenu("Yeni Tur Başlat")]
    public void YeniTurBaslat()
    {
        if (!IsServer) return;
        
        BloklariSifirla();
        Invoke(nameof(BloklariKaristir), 0.5f); // Kısa gecikme ile karıştır
    }
    
    /// <summary>
    /// Doğru blok oranını değiştirir
    /// </summary>
    public void DogruBlokOraniniAyarla(float yeniOran)
    {
        if (!IsServer) return;
        
        dogruBlokOrani = Mathf.Clamp01(yeniOran);
        Debug.Log($"📊 Doğru blok oranı {dogruBlokOrani * 100}% olarak ayarlandı.");
    }
    
    /// <summary>
    /// Oyuncu sayısını günceller
    /// </summary>
    public void OyuncuSayisiniGuncelle(int yeniSayi)
    {
        if (!IsServer) return;
        
        aktifOyuncuSayisi.Value = yeniSayi;
    }
    
    private void OnValidate()
    {
        // Inspector'da değerler değiştiğinde kontrol et
        dogruBlokOrani = Mathf.Clamp01(dogruBlokOrani);
        minimumDogruBlokSayisi = Mathf.Max(1, minimumDogruBlokSayisi);
    }
}