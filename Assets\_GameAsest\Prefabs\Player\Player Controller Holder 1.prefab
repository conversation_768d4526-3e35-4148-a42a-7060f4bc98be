%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2943773699578578080
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7985318602145355242}
  - component: {fileID: 1190160672940178343}
  - component: {fileID: 7703891435398250179}
  m_Layer: 7
  m_Name: Orientation
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7985318602145355242
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2943773699578578080}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.821, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5686595672387628804}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1190160672940178343
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2943773699578578080}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f42b1569c1c39aa429bfd2353b49efbb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &7703891435398250179
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2943773699578578080}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 463feba67bd840d42813f814b649ddb7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  targetTransform: {fileID: 2633389276928578265}
--- !u!1 &3119684559637352912
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3078387143154779611}
  - component: {fileID: 405513274226526041}
  - component: {fileID: 5380418185610260806}
  - component: {fileID: 5231201091629069160}
  - component: {fileID: 5808849364520002704}
  m_Layer: 0
  m_Name: PlayerNameCanvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3078387143154779611
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3119684559637352912}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2726549935353816473}
  m_Father: {fileID: 5686595672387628804}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 3.65}
  m_SizeDelta: {x: 1.39, y: 0.3}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!223 &405513274226526041
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3119684559637352912}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &5380418185610260806
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3119684559637352912}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!114 &5231201091629069160
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3119684559637352912}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &5808849364520002704
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3119684559637352912}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a932670afa4cac249bc6bd52f76b030f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  lockY: 0
  invertForward: 1
--- !u!1 &3258368510948809669
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2726549935353816473}
  - component: {fileID: 761832624219619864}
  - component: {fileID: 5807831368200467580}
  m_Layer: 0
  m_Name: PlayerNameText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2726549935353816473
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3258368510948809669}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3078387143154779611}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &761832624219619864
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3258368510948809669}
  m_CullTransparentMesh: 1
--- !u!114 &5807831368200467580
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3258368510948809669}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: SerkanBaba
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 0.2
  m_fontSizeBase: 0.2
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &4526217875587005210
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5686595672387628804}
  - component: {fileID: 2685559506508878781}
  - component: {fileID: 854230849660870887}
  - component: {fileID: 6923576083770368202}
  - component: {fileID: 8501861342371641249}
  - component: {fileID: 5922035821329474827}
  - component: {fileID: 746437121087326747}
  - component: {fileID: 8627215301301571683}
  m_Layer: 0
  m_Name: Player Controller Holder 1
  m_TagString: Player
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5686595672387628804
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526217875587005210}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -4.429, y: 3.3, z: -18.02}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8279656187086209114}
  - {fileID: 7985318602145355242}
  - {fileID: 3078387143154779611}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!143 &2685559506508878781
CharacterController:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526217875587005210}
  m_Material: {fileID: 13400000, guid: 21e9c2053e8a41141a32d45972da0872, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Height: 2.82
  m_Radius: 0.61
  m_SlopeLimit: 45
  m_StepOffset: 0.3
  m_SkinWidth: 0.0001
  m_MinMoveDistance: 0.001
  m_Center: {x: 0.01, y: 1.52, z: 0}
--- !u!114 &854230849660870887
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526217875587005210}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d5a57f767e5e46a458fc5d3c628d0cbb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  GlobalObjectIdHash: 3030000645
  InScenePlacedSourceGlobalObjectIdHash: 3352740688
  DeferredDespawnTick: 0
  Ownership: 1
  AlwaysReplicateAsRoot: 0
  SynchronizeTransform: 1
  ActiveSceneSynchronization: 0
  SceneMigrationSynchronization: 0
  SpawnWithObservers: 1
  DontDestroyWithOwner: 0
  AutoObjectParentSync: 1
  SyncOwnerTransformWhenParented: 1
  AllowOwnerToParent: 0
--- !u!114 &6923576083770368202
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526217875587005210}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2edd07ae3149ffc47b77370003cb5d01, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ShowTopMostFoldoutHeaderGroup: 1
  playerSpeed:
    m_InternalValue: 6
  lastCheckpointID:
    m_InternalValue: -1
  puan:
    m_InternalValue: 0
  playerName:
    m_InternalValue:
      utf8LengthInBytes: 6
      bytes:
        offset0000:
          byte0000: 80
          byte0001: 108
          byte0002: 97
          byte0003: 121
          byte0004: 101
          byte0005: 114
          byte0006: 0
          byte0007: 0
          byte0008: 0
          byte0009: 0
          byte0010: 0
          byte0011: 0
          byte0012: 0
          byte0013: 0
          byte0014: 0
          byte0015: 0
        offset0016:
          byte0000: 0
          byte0001: 0
          byte0002: 0
          byte0003: 0
          byte0004: 0
          byte0005: 0
          byte0006: 0
          byte0007: 0
          byte0008: 0
          byte0009: 0
          byte0010: 0
          byte0011: 0
          byte0012: 0
          byte0013: 0
          byte0014: 0
          byte0015: 0
        offset0032:
          byte0000: 0
          byte0001: 0
          byte0002: 0
          byte0003: 0
          byte0004: 0
          byte0005: 0
          byte0006: 0
          byte0007: 0
          byte0008: 0
          byte0009: 0
          byte0010: 0
          byte0011: 0
          byte0012: 0
          byte0013: 0
          byte0014: 0
          byte0015: 0
        byte0048: 0
        byte0049: 0
        byte0050: 0
        byte0051: 0
        byte0052: 0
        byte0053: 0
        byte0054: 0
        byte0055: 0
        byte0056: 0
        byte0057: 0
        byte0058: 0
        byte0059: 0
        byte0060: 0
        byte0061: 0
  orientation: {fileID: 7985318602145355242}
  playerBody: {fileID: 5686595672387628804}
  playerAnimator: {fileID: 1237999001470895224}
  cameraFollowTarget: {fileID: 7985318602145355242}
  playerNameText: {fileID: 5807831368200467580}
  rotationSpeed: 15
  acceleration: 10
  deceleration: 20
  jumpHeight: 3
  jumpCooldown: 0.01
  gravityValue: -20
  elendi:
    m_InternalValue: 0
--- !u!54 &8501861342371641249
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526217875587005210}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 112
  m_CollisionDetection: 2
--- !u!114 &5922035821329474827
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526217875587005210}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3545367a373189549b0f57d9c5131706, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ShowTopMostFoldoutHeaderGroup: 1
  characterAnimator: {fileID: 2596065407317989920}
  hipsBone: {fileID: 6153004854795880000}
  cameraTransform: {fileID: 0}
  syncRate: 15
  interpolationSpeed: 15
  maxTotalRagdollTime: 0.2
--- !u!114 &746437121087326747
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526217875587005210}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e96cb6065543e43c4a752faaa1468eb1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ShowTopMostFoldoutHeaderGroup: 1
  NetworkTransformExpanded: 0
  AutoOwnerAuthorityTickOffset: 1
  PositionInterpolationType: 0
  RotationInterpolationType: 0
  ScaleInterpolationType: 0
  PositionLerpSmoothing: 1
  PositionMaxInterpolationTime: 0.1
  RotationLerpSmoothing: 1
  RotationMaxInterpolationTime: 0.1
  ScaleLerpSmoothing: 1
  ScaleMaxInterpolationTime: 0.1
  AuthorityMode: 1
  TickSyncChildren: 0
  UseUnreliableDeltas: 0
  SyncPositionX: 1
  SyncPositionY: 1
  SyncPositionZ: 1
  SyncRotAngleX: 1
  SyncRotAngleY: 1
  SyncRotAngleZ: 1
  SyncScaleX: 1
  SyncScaleY: 1
  SyncScaleZ: 1
  PositionThreshold: 0.001
  RotAngleThreshold: 0.01
  ScaleThreshold: 0.01
  UseQuaternionSynchronization: 0
  UseQuaternionCompression: 0
  UseHalfFloatPrecision: 0
  InLocalSpace: 0
  SwitchTransformSpaceWhenParented: 0
  Interpolate: 1
  SlerpPosition: 0
--- !u!114 &8627215301301571683
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4526217875587005210}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e8d0727d5ae3244e3b569694d3912374, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ShowTopMostFoldoutHeaderGroup: 1
  TransitionStateInfoList: []
  m_Animator: {fileID: 2596065407317989920}
--- !u!1001 &8461566687758444465
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 5686595672387628804}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalScale.z
      value: 1.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -2207278367603736832, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 585e04169676646449b3b7a52e8a3ba2, type: 2}
    - target: {fileID: 919132149155446097, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_Name
      value: PlayerBody
      objectReference: {fileID: 0}
    - target: {fileID: 5866666021909216657, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_Controller
      value: 
      objectReference: {fileID: 9100000, guid: f7d42506c31d568428d9a8bb25885526, type: 2}
    - target: {fileID: 5866666021909216657, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      propertyPath: m_ApplyRootMotion
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1237999001470895224}
    - targetCorrespondingSourceObject: {fileID: 1082271958561512970, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4659313489362479117}
    - targetCorrespondingSourceObject: {fileID: 1082271958561512970, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6153004854795880000}
    - targetCorrespondingSourceObject: {fileID: 3135921509060577837, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3952923529088401239}
    - targetCorrespondingSourceObject: {fileID: 3135921509060577837, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6654694799644134052}
    - targetCorrespondingSourceObject: {fileID: 3135921509060577837, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8035559297040917115}
    - targetCorrespondingSourceObject: {fileID: -6170625038081293824, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3615479374402115358}
    - targetCorrespondingSourceObject: {fileID: -6170625038081293824, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1468822362622853925}
    - targetCorrespondingSourceObject: {fileID: -6170625038081293824, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 5632698367862617646}
    - targetCorrespondingSourceObject: {fileID: -3086436065929644917, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8776008121631424724}
    - targetCorrespondingSourceObject: {fileID: -3086436065929644917, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4548030008516535865}
    - targetCorrespondingSourceObject: {fileID: -3086436065929644917, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1238684272648457306}
    - targetCorrespondingSourceObject: {fileID: -4332706925441422008, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3284987403804407046}
    - targetCorrespondingSourceObject: {fileID: -4332706925441422008, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 848542405477211180}
    - targetCorrespondingSourceObject: {fileID: -4332706925441422008, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8550634624657515509}
    - targetCorrespondingSourceObject: {fileID: -203856693384778745, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8116360417059981960}
    - targetCorrespondingSourceObject: {fileID: -203856693384778745, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4490414812734600719}
    - targetCorrespondingSourceObject: {fileID: -203856693384778745, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8624316731947261096}
    - targetCorrespondingSourceObject: {fileID: 8817898888258712963, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8145154541168440326}
    - targetCorrespondingSourceObject: {fileID: 8817898888258712963, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8211755898286968625}
    - targetCorrespondingSourceObject: {fileID: 8817898888258712963, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7573205137163474387}
    - targetCorrespondingSourceObject: {fileID: 3948933247560734027, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6511869767593342960}
    - targetCorrespondingSourceObject: {fileID: 3948933247560734027, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4851837910296301867}
    - targetCorrespondingSourceObject: {fileID: 3948933247560734027, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2519343285745753287}
    - targetCorrespondingSourceObject: {fileID: -1781356600409242780, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2602269232180111093}
    - targetCorrespondingSourceObject: {fileID: -1781356600409242780, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6695905531300696036}
    - targetCorrespondingSourceObject: {fileID: -1781356600409242780, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 9142599808239516337}
    - targetCorrespondingSourceObject: {fileID: -9070493210130169274, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4353345782332909546}
    - targetCorrespondingSourceObject: {fileID: -9070493210130169274, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8836904465606159901}
    - targetCorrespondingSourceObject: {fileID: -9070493210130169274, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 5693684562400244556}
    - targetCorrespondingSourceObject: {fileID: -290750445479886849, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 97988149953674692}
    - targetCorrespondingSourceObject: {fileID: -290750445479886849, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7636708035982778527}
    - targetCorrespondingSourceObject: {fileID: -290750445479886849, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
      insertIndex: -1
      addedObject: {fileID: 855670560852913909}
  m_SourcePrefab: {fileID: 100100000, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
--- !u!1 &596242585264589750 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -203856693384778745, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &8116360417059981960
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 596242585264589750}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.5406618, y: 0.25570023, z: 0.29045582}
  m_Center: {x: 0, y: 0.12785007, z: -0.003017366}
--- !u!54 &4490414812734600719
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 596242585264589750}
  serializedVersion: 5
  m_Mass: 3.125
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!144 &8624316731947261096
CharacterJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 596242585264589750}
  serializedVersion: 3
  m_ConnectedBody: {fileID: 6153004854795880000}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SwingAxis: {x: 0, y: 0, z: 1}
  m_TwistLimitSpring:
    spring: 0
    damper: 0
  m_LowTwistLimit:
    limit: -20
    bounciness: 0
    contactDistance: 0
  m_HighTwistLimit:
    limit: 20
    bounciness: 0
    contactDistance: 0
  m_SwingLimitSpring:
    spring: 0
    damper: 0
  m_Swing1Limit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_Swing2Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_EnableProjection: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 0
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &1052305266852010062 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -290750445479886849, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!136 &97988149953674692
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1052305266852010062}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09797504
  m_Height: 0.48987517
  m_Direction: 1
  m_Center: {x: 0, y: 0.24493758, z: 0}
--- !u!54 &7636708035982778527
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1052305266852010062}
  serializedVersion: 5
  m_Mass: 1.25
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!144 &855670560852913909
CharacterJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1052305266852010062}
  serializedVersion: 3
  m_ConnectedBody: {fileID: 8836904465606159901}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SwingAxis: {x: 0, y: 0, z: -1}
  m_TwistLimitSpring:
    spring: 0
    damper: 0
  m_LowTwistLimit:
    limit: -90
    bounciness: 0
    contactDistance: 0
  m_HighTwistLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_SwingLimitSpring:
    spring: 0
    damper: 0
  m_Swing1Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_Swing2Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_EnableProjection: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 0
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &1095217227028107826 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8817898888258712963, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!136 &8145154541168440326
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1095217227028107826}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.052486792
  m_Height: 0.20994717
  m_Direction: 1
  m_Center: {x: 0, y: 0.104973584, z: 0}
--- !u!54 &8211755898286968625
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1095217227028107826}
  serializedVersion: 5
  m_Mass: 1.25
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!144 &7573205137163474387
CharacterJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1095217227028107826}
  serializedVersion: 3
  m_ConnectedBody: {fileID: 4490414812734600719}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SwingAxis: {x: 1, y: 0, z: 0}
  m_TwistLimitSpring:
    spring: 0
    damper: 0
  m_LowTwistLimit:
    limit: -70
    bounciness: 0
    contactDistance: 0
  m_HighTwistLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_SwingLimitSpring:
    spring: 0
    damper: 0
  m_Swing1Limit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_Swing2Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_EnableProjection: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 0
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &1309104443174790357 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -1781356600409242780, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!135 &2602269232180111093
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309104443174790357}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.13516726
  m_Center: {x: 0, y: 0.13516726, z: 0}
--- !u!54 &6695905531300696036
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309104443174790357}
  serializedVersion: 5
  m_Mass: 1.25
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!144 &9142599808239516337
CharacterJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309104443174790357}
  serializedVersion: 3
  m_ConnectedBody: {fileID: 4490414812734600719}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SwingAxis: {x: 0, y: 0, z: 1}
  m_TwistLimitSpring:
    spring: 0
    damper: 0
  m_LowTwistLimit:
    limit: -40
    bounciness: 0
    contactDistance: 0
  m_HighTwistLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_SwingLimitSpring:
    spring: 0
    damper: 0
  m_Swing1Limit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_Swing2Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_EnableProjection: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 0
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &2325899003638698810 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -3086436065929644917, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!136 &8776008121631424724
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325899003638698810}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08072061
  m_Height: 0.2690687
  m_Direction: 1
  m_Center: {x: 0, y: 0.13453434, z: 0}
--- !u!54 &4548030008516535865
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325899003638698810}
  serializedVersion: 5
  m_Mass: 1.875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!144 &1238684272648457306
CharacterJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325899003638698810}
  serializedVersion: 3
  m_ConnectedBody: {fileID: 6153004854795880000}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SwingAxis: {x: 0, y: 0, z: 1}
  m_TwistLimitSpring:
    spring: 0
    damper: 0
  m_LowTwistLimit:
    limit: -20
    bounciness: 0
    contactDistance: 0
  m_HighTwistLimit:
    limit: 70
    bounciness: 0
    contactDistance: 0
  m_SwingLimitSpring:
    spring: 0
    damper: 0
  m_Swing1Limit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_Swing2Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_EnableProjection: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 0
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!95 &2596065407317989920 stripped
Animator:
  m_CorrespondingSourceObject: {fileID: 5866666021909216657, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &2633389276928578265 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -3321919801592875672, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &3941393029477075705 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -4332706925441422008, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!136 &3284987403804407046
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3941393029477075705}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.081241906
  m_Height: 0.32496762
  m_Direction: 1
  m_Center: {x: 0, y: 0.16248381, z: 0}
--- !u!54 &848542405477211180
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3941393029477075705}
  serializedVersion: 5
  m_Mass: 1.875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!144 &8550634624657515509
CharacterJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3941393029477075705}
  serializedVersion: 3
  m_ConnectedBody: {fileID: 4548030008516535865}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SwingAxis: {x: 0, y: 0, z: 1}
  m_TwistLimitSpring:
    spring: 0
    damper: 0
  m_LowTwistLimit:
    limit: -80
    bounciness: 0
    contactDistance: 0
  m_HighTwistLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_SwingLimitSpring:
    spring: 0
    damper: 0
  m_Swing1Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_Swing2Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_EnableProjection: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 0
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &4873154075359228666 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3948933247560734027, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!136 &6511869767593342960
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4873154075359228666}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.0981568
  m_Height: 0.490784
  m_Direction: 1
  m_Center: {x: 0, y: 0.245392, z: 0}
--- !u!54 &4851837910296301867
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4873154075359228666}
  serializedVersion: 5
  m_Mass: 1.25
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!144 &2519343285745753287
CharacterJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4873154075359228666}
  serializedVersion: 3
  m_ConnectedBody: {fileID: 8211755898286968625}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SwingAxis: {x: 0, y: 0, z: -1}
  m_TwistLimitSpring:
    spring: 0
    damper: 0
  m_LowTwistLimit:
    limit: -90
    bounciness: 0
    contactDistance: 0
  m_HighTwistLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_SwingLimitSpring:
    spring: 0
    damper: 0
  m_Swing1Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_Swing2Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_EnableProjection: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 0
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &6838858890613812636 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3135921509060577837, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!136 &3952923529088401239
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6838858890613812636}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08071838
  m_Height: 0.26906127
  m_Direction: 1
  m_Center: {x: 0, y: 0.13453063, z: 0}
--- !u!54 &6654694799644134052
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6838858890613812636}
  serializedVersion: 5
  m_Mass: 1.875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!144 &8035559297040917115
CharacterJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6838858890613812636}
  serializedVersion: 3
  m_ConnectedBody: {fileID: 6153004854795880000}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SwingAxis: {x: 0, y: 0, z: 1}
  m_TwistLimitSpring:
    spring: 0
    damper: 0
  m_LowTwistLimit:
    limit: -20
    bounciness: 0
    contactDistance: 0
  m_HighTwistLimit:
    limit: 70
    bounciness: 0
    contactDistance: 0
  m_SwingLimitSpring:
    spring: 0
    damper: 0
  m_Swing1Limit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_Swing2Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_EnableProjection: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 0
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &6858998908099913137 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -6170625038081293824, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!136 &3615479374402115358
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6858998908099913137}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08147386
  m_Height: 0.32589543
  m_Direction: 1
  m_Center: {x: 0, y: 0.16294771, z: 0}
--- !u!54 &1468822362622853925
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6858998908099913137}
  serializedVersion: 5
  m_Mass: 1.875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!144 &5632698367862617646
CharacterJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6858998908099913137}
  serializedVersion: 3
  m_ConnectedBody: {fileID: 6654694799644134052}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SwingAxis: {x: 0, y: 0, z: 1}
  m_TwistLimitSpring:
    spring: 0
    damper: 0
  m_LowTwistLimit:
    limit: -80
    bounciness: 0
    contactDistance: 0
  m_HighTwistLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_SwingLimitSpring:
    spring: 0
    damper: 0
  m_Swing1Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_Swing2Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_EnableProjection: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 0
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &8279656187086209114 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &8607122611346318839 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -9070493210130169274, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!136 &4353345782332909546
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8607122611346318839}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.052622
  m_Height: 0.210488
  m_Direction: 1
  m_Center: {x: 0, y: 0.105244, z: 0}
--- !u!54 &8836904465606159901
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8607122611346318839}
  serializedVersion: 5
  m_Mass: 1.25
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!144 &5693684562400244556
CharacterJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8607122611346318839}
  serializedVersion: 3
  m_ConnectedBody: {fileID: 4490414812734600719}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SwingAxis: {x: -1, y: 0, z: 0}
  m_TwistLimitSpring:
    spring: 0
    damper: 0
  m_LowTwistLimit:
    limit: -70
    bounciness: 0
    contactDistance: 0
  m_HighTwistLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_SwingLimitSpring:
    spring: 0
    damper: 0
  m_Swing1Limit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_Swing2Limit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_EnableProjection: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 0
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &8767646199123794656 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1237999001470895224
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8767646199123794656}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0ecc427693da4484c94f71b89bd1ae05, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &8820447816853895611 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1082271958561512970, guid: abb473f62a1bbb247a86e216b2383ffe, type: 3}
  m_PrefabInstance: {fileID: 8461566687758444465}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &4659313489362479117
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8820447816853895611}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.5406618, y: 0.32531586, z: 0.2905274}
  m_Center: {x: -0.0000017285347, y: 0.096269295, z: -0.0050272495}
--- !u!54 &6153004854795880000
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8820447816853895611}
  serializedVersion: 5
  m_Mass: 3.125
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
