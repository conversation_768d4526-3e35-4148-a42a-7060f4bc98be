using UnityEngine;
using Unity.Netcode;
using System.Collections;

// Yeni renklerinle güncellenmiş enum tanımı. Bu kısım sende zaten doğru.
public enum PlatformRenkleri { <PERSON>rm<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> }

public class RenkPlatformu : NetworkBehaviour
{
    [Header("Platform Ayarları")]
    [Tooltip("Bu platform'un rengi (Inspector'da ayarlanır)")]
    public PlatformRenkleri buPlatformunRengi;
    
    [Header("Platform Pozisyon")]
    [Tooltip("Platformları birbirine yapıştır")]
    public bool yapistirPlatformlari = true;
    [Tooltip("Platform arası mesafe (0 = tam yapışık)")]
    public float platformAraMesafe = 0f;
    
    [Header("Network Senkronizasyon")]
    [Tooltip("Network'te senkronize edilen renk")]
    private NetworkVariable<PlatformRenkleri> networkRenk = new NetworkVariable<PlatformRenkleri>(
        PlatformRenkleri.Kirmizi,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);
    
    private ParticleSystem _particleSystem;
    private Vector3 orijinalScale;
    private bool animasyonlaKayboldu = false;
    
    [Header("Renk Ayarları")]
    [Tooltip("Platform'un Renderer component'i (otomatik bulunur)")]
    private Renderer platformRenderer;
    
    [Header("Batch Optimization")]
    private MaterialPropertyBlock propertyBlock;

    private void Awake()
    {
        _particleSystem = GetComponent<ParticleSystem>();
        orijinalScale = transform.localScale;
        
        // Renderer'ı bul
        platformRenderer = GetComponent<Renderer>();
        if (platformRenderer == null)
        {
            platformRenderer = GetComponentInChildren<Renderer>();
        }
        
        // PropertyBlock oluştur (batch optimization için)
        propertyBlock = new MaterialPropertyBlock();
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();

        // Network renk değişikliklerini dinle
        networkRenk.OnValueChanged += OnNetworkRenkChanged;

        // Server'da Inspector'dan gelen rengi network'e aktar
        if (IsServer)
        {
            networkRenk.Value = buPlatformunRengi;
            Debug.Log($"🎨 Platform {gameObject.name} spawn oldu, renk: {buPlatformunRengi}");
        }

        // İlk rengi uygula (network değeri gelene kadar Inspector değerini kullan)
        RengiUygula();
    }

    public override void OnNetworkDespawn()
    {
        // Event listener'ı temizle
        networkRenk.OnValueChanged -= OnNetworkRenkChanged;
        base.OnNetworkDespawn();
    }

    private void Start()
    {
        // Start'ta da rengi uygula (güvenlik için)
        RengiUygula();
    }

    /// <summary>
    /// Network renk değiştiğinde çağrılır
    /// </summary>
    private void OnNetworkRenkChanged(PlatformRenkleri eskiRenk, PlatformRenkleri yeniRenk)
    {
        Debug.Log($"🎨 Platform {gameObject.name}: Renk değişti {eskiRenk} → {yeniRenk}");
        buPlatformunRengi = yeniRenk;
        RengiUygula();
    }

    /// <summary>
    /// Platform rengini görsel olarak uygular
    /// </summary>
    private void RengiUygula()
    {
        if (platformRenderer == null || propertyBlock == null) return;

        // Network'ten gelen rengi kullan (eğer network aktifse)
        PlatformRenkleri kullanilacakRenk = IsSpawned ? networkRenk.Value : buPlatformunRengi;
        Color platformRengi = RenkEnumToColor(kullanilacakRenk);

        // ULTRA BATCH OPTIMIZATION: PropertyBlock kullan!
        // Bu sayede tüm platformlar aynı materialı paylaşır ama farklı renklere sahip olur
        propertyBlock.SetColor("_Color", platformRengi);
        propertyBlock.SetColor("_BaseColor", platformRengi); // URP için
        
        // PropertyBlock'u renderer'a uygula
        platformRenderer.SetPropertyBlock(propertyBlock);

        Debug.Log($"🎨 Platform {gameObject.name} rengi PropertyBlock ile uygulandı: {kullanilacakRenk} -> {platformRengi}");
    }

    /// <summary>
    /// Server'dan platform rengini değiştirmek için RPC
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    public void SetPlatformRenkServerRpc(PlatformRenkleri yeniRenk)
    {
        if (!IsServer)
        {
            Debug.LogError("SetPlatformRenkServerRpc sadece server'da çalışabilir!");
            return;
        }

        // Network variable'ı güncelle - bu otomatik olarak tüm client'lara gönderilir
        networkRenk.Value = yeniRenk;
        buPlatformunRengi = yeniRenk;

        Debug.Log($"🎨 Server platform {gameObject.name} rengini değiştirdi: {yeniRenk}");
    }

    /// <summary>
    /// Enum rengini Unity Color'a çevirir
    /// SEN KENDI RENKLERİNİ BURAYA EKLE!
    /// </summary>
    private Color RenkEnumToColor(PlatformRenkleri renk)
    {
        switch (renk)
        {
            case PlatformRenkleri.Kirmizi: 
                // SENİN KIRMIZI RENGİN BURAYA
                return Color.red; // BUNU DEĞİŞTİR
            case PlatformRenkleri.Yesil: 
                // SENİN YEŞİL RENGİN BURAYA
                return Color.green; // BUNU DEĞİŞTİR
            case PlatformRenkleri.Mavi: 
                // SENİN MAVİ RENGİN BURAYA
                return Color.blue; // BUNU DEĞİŞTİR
            case PlatformRenkleri.Sari: 
                // SENİN SARI RENGİN BURAYA
                return Color.yellow; // BUNU DEĞİŞTİR
            case PlatformRenkleri.Pembe: 
                // SENİN PEMBE RENGİN BURAYA
                return Color.magenta; // BUNU DEĞİŞTİR
            case PlatformRenkleri.Mor: 
                // SENİN MOR RENGİN BURAYA
                return new Color(0.5f, 0f, 1f); // BUNU DEĞİŞTİR
            case PlatformRenkleri.Turuncu: 
                // SENİN TURUNCU RENGİN BURAYA
                return new Color(1f, 0.5f, 0f); // BUNU DEĞİŞTİR
            case PlatformRenkleri.Siyah: 
                // SENİN SİYAH RENGİN BURAYA
                return Color.black; // BUNU DEĞİŞTİR
            case PlatformRenkleri.Gri: 
                // SENİN GRİ RENGİN BURAYA
                return Color.gray; // BUNU DEĞİŞTİR
            default: 
                return Color.white;
        }
    }

    private void StartBreakEffect()
    {
        if (_particleSystem != null)
        {
            _particleSystem.Play();
        }
    }

    [ClientRpc]
    public void KaybolClientRpc()
    {
        StartCoroutine(KaybolVeGecikmeliGizle());
    }

    private System.Collections.IEnumerator KaybolVeGecikmeliGizle()
    {
        animasyonlaKayboldu = true;
        float sure = 0.2f;
        float zaman = 0f;
        while (zaman < sure)
        {
            float t = zaman / sure;
            transform.localScale = Vector3.Lerp(orijinalScale, Vector3.zero, t);
            zaman += Time.deltaTime;
            yield return null;
        }
        transform.localScale = Vector3.zero;
        gameObject.SetActive(false);
        transform.localScale = orijinalScale; // Sonraki turda geri gelirse eski haline dönsün
    }

    [ClientRpc]
    public void GeriGelClientRpc()
    {
        gameObject.SetActive(true);
        if (animasyonlaKayboldu)
        {
            StartCoroutine(KucuktenBuyuAnimasyonu());
            animasyonlaKayboldu = false;
        }
    }

    private System.Collections.IEnumerator KucuktenBuyuAnimasyonu()
    {
        float sure = 0.2f;
        float zaman = 0f;
        transform.localScale = Vector3.zero;
        while (zaman < sure)
        {
            float t = zaman / sure;
            transform.localScale = Vector3.Lerp(Vector3.zero, orijinalScale, t);
            zaman += Time.deltaTime;
            yield return null;
        }
        transform.localScale = orijinalScale;
    }
}