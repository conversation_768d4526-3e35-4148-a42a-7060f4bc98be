using UnityEngine;
using Unity.Netcode;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Spawn point ayarlama modları
/// </summary>
public enum SpawnPointMode
{
    ManualPositions,    // Inspector'da manuel Vector3 pozisyonları
    SceneTransforms,    // Sahne içindeki Transform referansları
    AutoDetectTags      // "SpawnPoint" tag'li objeleri otomatik bul
}

/// <summary>
/// Gelişmiş spawn point verisi - tam 3D kontrol
/// </summary>
[System.Serializable]
public class SpawnPointData
{
    [Tooltip("Spawn pozisyonu (X, Y, Z tam kontrol)")]
    public Vector3 position = Vector3.zero;

    [Tooltip("Spawn rotasyonu (Euler açıları)")]
    public Vector3 rotation = Vector3.zero;

    [Tooltip("Bu spawn point'in adı (debug için)")]
    public string name = "Spawn Point";

    [Tooltip("Bu spawn point aktif mi?")]
    public bool isActive = true;

    [Tooltip("Minimum oyuncu sayısı (bu spawn point'i kullanmak için)")]
    public int minPlayersRequired = 1;
}

/// <summary>
/// RenkOyunu sahnesindeki eksik setup'ları otomatik olarak düzelten manager
/// </summary>
public class RenkOyunuSetupManager : NetworkBehaviour
{
    [Header("Sahne Setup Ayarları")]
    public bool autoSetupOnStart = true;
    public bool createMissingComponents = true;
    
    [Header("Kamera Ayarları")]
    public GameObject cameraPrefab; // Inspector'dan atanacak
    public Vector3 defaultCameraPosition = new Vector3(0, 10, -10);
    public Vector3 defaultCameraRotation = new Vector3(20, 0, 0);
    
    [Header("UI Ayarları")]
    public GameObject joystickPrefab; // Inspector'dan atanacak
    public Canvas uiCanvas; // Inspector'dan atanacak

    [Header("Spawn Ayarları")]
    [Tooltip("Spawn pozisyonu ayarlama yöntemi")]
    public SpawnPointMode spawnMode = SpawnPointMode.ManualPositions;

    [Tooltip("Manuel pozisyonlar - X, Y, Z tam kontrol")]
    public SpawnPointData[] manualSpawnPoints = new SpawnPointData[]
    {
        new SpawnPointData { position = new Vector3(0, 2, 0), rotation = Vector3.zero, name = "Merkez" },
        new SpawnPointData { position = new Vector3(-2, 2, 0), rotation = Vector3.zero, name = "Sol" },
        new SpawnPointData { position = new Vector3(2, 2, 0), rotation = Vector3.zero, name = "Sağ" },
        new SpawnPointData { position = new Vector3(-4, 2, 0), rotation = Vector3.zero, name = "Sol Uzak" },
        new SpawnPointData { position = new Vector3(4, 2, 0), rotation = Vector3.zero, name = "Sağ Uzak" }
    };

    [Tooltip("Platform yüksekliğini otomatik algıla ve Y değerini ayarla (sadece ManualPositions modunda)")]
    public bool autoDetectPlatformHeight = false;

    [Tooltip("Sahne içindeki Transform referansları (SceneTransforms modunda)")]
    public Transform[] sceneSpawnTransforms = new Transform[0];

    [Header("Debug")]
    public bool showDebugLogs = true;

    [Header("Görselleştirme")]
    [Tooltip("Scene view'da spawn pozisyonlarını göster")]
    public bool showSpawnPositionsInScene = true;
    public Color spawnPositionGizmoColor = Color.green;
    
    private static RenkOyunuSetupManager _instance;
    public static RenkOyunuSetupManager Instance => _instance;
    
    void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this;
    }
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            StartCoroutine(DelayedSetup());
        }
    }
    
    private IEnumerator DelayedSetup()
    {
        // Network spawn'ların tamamlanmasını bekle
        yield return new WaitForSeconds(0.5f);
        
        SetupSpawnPoints();
        SetupCamera();
        SetupJoystick();
        SetupUI();
        SetupGameStarter();
        SetupRenkOyunuManager();
        CheckNetworkComponents();

        // Setup tamamlandıktan sonra oyun başlatma sinyali gönder
        NotifySetupComplete();
    }

    // --- YENİ VE GELİŞMİŞ SPAWN NOKTASI AYARLAMA METODU ---
    private void SetupSpawnPoints()
    {
        var networkManager = NetworkManager.Singleton;
        if (networkManager == null)
        {
            return;
        }

        List<Transform> spawnPointTransforms = new List<Transform>();

        // Seçilen moda göre spawn point'leri oluştur
        switch (spawnMode)
        {
            case SpawnPointMode.ManualPositions:
                CreateSpawnPointsFromManualData(spawnPointTransforms);
                break;

            case SpawnPointMode.SceneTransforms:
                CreateSpawnPointsFromSceneTransforms(spawnPointTransforms);
                break;

            case SpawnPointMode.AutoDetectTags:
                CreateSpawnPointsFromTags(spawnPointTransforms);
                break;
        }

        // Spawn point bulunamadıysa fallback
        if (spawnPointTransforms.Count == 0)
        {
            CreateFallbackSpawnPoint(spawnPointTransforms);
        }

        // CustomNetworkManager'a ata
        CustomNetworkManager customNetworkManager = FindFirstObjectByType<CustomNetworkManager>();
        if (customNetworkManager != null)
        {
            SetCustomNetworkManagerSpawnPoints(customNetworkManager, spawnPointTransforms.ToArray());
        }
        else
        {
        }
    }

    // --- YENİ SPAWN POINT OLUŞTURMA METODLARİ ---

    /// <summary>
    /// Manuel pozisyonlardan spawn point'leri oluştur - TAM 3D KONTROL
    /// </summary>
    private void CreateSpawnPointsFromManualData(List<Transform> spawnPointList)
    {
        if (manualSpawnPoints == null || manualSpawnPoints.Length == 0)
        {
            return;
        }

        for (int i = 0; i < manualSpawnPoints.Length; i++)
        {
            var spawnData = manualSpawnPoints[i];

            // Aktif olmayan spawn point'leri atla
            if (!spawnData.isActive)
            {
                continue;
            }

            // Spawn point objesini oluştur
            GameObject spawnPointObj = new GameObject($"SpawnPoint_{i}_{spawnData.name}");

            // Pozisyon ayarla (tam 3D kontrol)
            Vector3 finalPosition = spawnData.position;

            // Platform yüksekliği otomatik algılama (opsiyonel)
            if (autoDetectPlatformHeight)
            {
                float platformHeight = DetectPlatformHeight();
                finalPosition.y = platformHeight + 1f;

            }
            else
            {
                // Platform algılama kapalıysa, manuel Y değerini kullan
            }

            spawnPointObj.transform.position = finalPosition;
            spawnPointObj.transform.rotation = Quaternion.Euler(spawnData.rotation);

            // Tag ekle (debug için)
            spawnPointObj.tag = "SpawnPoint";

            spawnPointList.Add(spawnPointObj.transform);


        }
    }

    /// <summary>
    /// Sahne içindeki Transform referanslarından spawn point'leri oluştur
    /// </summary>
    private void CreateSpawnPointsFromSceneTransforms(List<Transform> spawnPointList)
    {
        if (sceneSpawnTransforms == null || sceneSpawnTransforms.Length == 0)
        {
            return;
        }

        for (int i = 0; i < sceneSpawnTransforms.Length; i++)
        {
            if (sceneSpawnTransforms[i] != null)
            {
                spawnPointList.Add(sceneSpawnTransforms[i]);
            }
            else
            {
                Debug.LogWarning($"RenkOyunuSetupManager: sceneSpawnTransforms[{i}] null!");
            }
        }
    }

    /// <summary>
    /// Tag'li objeleri otomatik bularak spawn point'leri oluştur
    /// </summary>
    private void CreateSpawnPointsFromTags(List<Transform> spawnPointList)
    {
        // "SpawnPoint" tag'li objeleri bul
        GameObject[] taggedSpawnPoints = GameObject.FindGameObjectsWithTag("SpawnPoint");

        if (taggedSpawnPoints.Length == 0)
        {
            Debug.LogWarning("RenkOyunuSetupManager: 'SpawnPoint' tag'li obje bulunamadı!");
            Debug.LogWarning("RenkOyunuSetupManager: Sahnenizde 'SpawnPoint' tag'li objeler oluşturun veya başka mod kullanın!");
            return;
        }

        if (showDebugLogs)
            ; // Debug.Log($"=== AUTO DETECT TAGS DEBUG ===");
        if (showDebugLogs)
            ; // Debug.Log($"Tag'li objelerden {taggedSpawnPoints.Length} adet spawn noktası bulundu:");

        for (int i = 0; i < taggedSpawnPoints.Length; i++)
        {
            var obj = taggedSpawnPoints[i];
            spawnPointList.Add(obj.transform);

            if (showDebugLogs)
                ; // Debug.Log($"  {i}: {obj.name} - Pos: {obj.transform.position}, Rot: {obj.transform.eulerAngles}");
        }

        if (showDebugLogs)
            ; // Debug.Log($"=== TOPLAM {spawnPointList.Count} SPAWN POINT EKLENDI ===");
    }

    /// <summary>
    /// Hiç spawn point bulunamazsa varsayılan oluştur
    /// </summary>
    private void CreateFallbackSpawnPoint(List<Transform> spawnPointList)
    {
        GameObject fallbackSpawn = new GameObject("FallbackSpawnPoint");
        fallbackSpawn.transform.position = new Vector3(0, 2, 0);
        fallbackSpawn.transform.rotation = Quaternion.identity;
        fallbackSpawn.tag = "SpawnPoint";

        spawnPointList.Add(fallbackSpawn.transform);

        if (showDebugLogs)
            ; // Debug.Log("Fallback spawn point oluşturuldu: (0, 2, 0)");
    }

    // CustomNetworkManager'a spawn noktalarını ata (temiz yöntem)
    private void SetCustomNetworkManagerSpawnPoints(CustomNetworkManager customNetworkManager, Transform[] spawnPoints)
    {
        // Public SetSpawnPoints metodunu kullan (reflection gerekmez)
        customNetworkManager.SetSpawnPoints(spawnPoints);

    }

    private float DetectPlatformHeight()
    {
        float detectedHeight = 2f; // Default

        // RenkPlatformu'ları ara
        RenkPlatformu[] platforms = FindObjectsByType<RenkPlatformu>(FindObjectsSortMode.None);
        if (platforms.Length > 0)
        {
            // En yüksek platform'u bul
            float maxHeight = detectedHeight;
            foreach (var platform in platforms)
            {
                float platformTop = platform.transform.position.y + (platform.transform.localScale.y * 0.5f);
                if (platformTop > maxHeight)
                {
                    maxHeight = platformTop;
                }
            }
            detectedHeight = maxHeight;


        }
        else
        {
            // Genel platform arama
            GameObject[] allObjects = FindObjectsByType<GameObject>(FindObjectsSortMode.None);
            foreach (var obj in allObjects)
            {
                if (obj.name.ToLower().Contains("platform") || obj.name.ToLower().Contains("zemin") || obj.name.ToLower().Contains("ground"))
                {
                    float objHeight = obj.transform.position.y + (obj.transform.localScale.y * 0.5f);
                    if (objHeight > detectedHeight)
                    {
                        detectedHeight = objHeight;
                    }
                }
            }

            if (showDebugLogs)
                ; // Debug.Log($"RenkOyunuSetupManager: Genel platform arama sonucu - {detectedHeight}");
        }

        return detectedHeight;
    }

    private Transform[] GetSceneSpawnPoints()
    {
        List<Transform> spawnPoints = new List<Transform>();

        // 1. "SpawnPoint" tag'li objeleri ara
        GameObject[] taggedSpawnPoints = GameObject.FindGameObjectsWithTag("SpawnPoint");
        foreach (var obj in taggedSpawnPoints)
        {
            spawnPoints.Add(obj.transform);
        }

        // 2. SpawnPoint component'li objeleri ara
        SpawnPoint[] componentSpawnPoints = FindObjectsByType<SpawnPoint>(FindObjectsSortMode.None);
        foreach (var sp in componentSpawnPoints)
        {
            if (!spawnPoints.Contains(sp.transform))
            {
                spawnPoints.Add(sp.transform);
            }
        }

        // 3. İsimde "spawn" geçen objeleri ara
        if (spawnPoints.Count == 0)
        {
            GameObject[] allObjects = FindObjectsByType<GameObject>(FindObjectsSortMode.None);
            foreach (var obj in allObjects)
            {
                if (obj.name.ToLower().Contains("spawn"))
                {
                    spawnPoints.Add(obj.transform);
                }
            }
        }

        if (showDebugLogs && spawnPoints.Count > 0)
            ; // Debug.Log($"RenkOyunuSetupManager: Sahnede {spawnPoints.Count} spawn noktası bulundu");

        return spawnPoints.ToArray();
    }

    private void SetupCamera()
    {
        // InstantHybridCameraController var mı kontrol et
        InstantHybridCameraController existingCamera = FindFirstObjectByType<InstantHybridCameraController>();

        if (existingCamera == null)
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: InstantHybridCameraController bulunamadı, oluşturuluyor...");

            GameObject cameraObj = null;

            // Prefab varsa kullan
            if (cameraPrefab != null)
            {
                cameraObj = Instantiate(cameraPrefab);
            }
            else
            {
                // Manuel kamera oluştur
                cameraObj = new GameObject("Main Camera");
                Camera cam = cameraObj.AddComponent<Camera>();
                cameraObj.AddComponent<AudioListener>();
                cameraObj.AddComponent<InstantHybridCameraController>();

                // Kamera ayarları
                cam.tag = "MainCamera";
                cameraObj.transform.position = defaultCameraPosition;
                cameraObj.transform.eulerAngles = defaultCameraRotation;
            }

            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: Kamera oluşturuldu!");
        }
        else
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: InstantHybridCameraController mevcut.");
        }

        // Kamera oluşturulduktan sonra local player'ı bul ve target'ı ayarla
        StartCoroutine(SetupCameraTargetWithDelay());
    }

    private System.Collections.IEnumerator SetupCameraTargetWithDelay()
    {
        // Oyuncuların spawn olmasını bekle
        yield return new WaitForSeconds(1f);

        if (showDebugLogs)
            ; // Debug.Log("RenkOyunuSetupManager: Local player aranıyor...");

        // Local player'ı bul
        OyuncuKontrol localPlayer = OyuncuKontrol.LocalInstance;
        if (localPlayer == null)
        {
            // LocalInstance yoksa tüm oyuncular arasında ara
            OyuncuKontrol[] allPlayers = FindObjectsByType<OyuncuKontrol>(FindObjectsSortMode.None);
            foreach (var player in allPlayers)
            {
                if (player.IsOwner)
                {
                    localPlayer = player;
                    break;
                }
            }
        }

        if (localPlayer != null)
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: Local player bulundu, kamera target'ı ayarlanıyor...");

            SetupPlayerOnSpawn(localPlayer);
        }
        else
        {
            if (showDebugLogs)
                ; // Debug.LogWarning("RenkOyunuSetupManager: Local player bulunamadı!");
        }
    }
    
    private void SetupJoystick()
    {
        // FloatingJoystick var mı kontrol et
        FloatingJoystick existingJoystick = FindFirstObjectByType<FloatingJoystick>();
        
        if (existingJoystick == null)
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: FloatingJoystick bulunamadı...");
            
            // Canvas var mı kontrol et
            Canvas canvas = uiCanvas;
            if (canvas == null)
            {
                canvas = FindFirstObjectByType<Canvas>();
            }
            
            if (canvas != null && joystickPrefab != null)
            {
                GameObject joystickObj = Instantiate(joystickPrefab, canvas.transform);
                if (showDebugLogs)
                    ; // Debug.Log("RenkOyunuSetupManager: FloatingJoystick oluşturuldu!");
            }
            else
            {
                if (showDebugLogs)
                    ; // Debug.LogWarning("RenkOyunuSetupManager: Canvas veya Joystick prefab bulunamadı!");
            }
        }
        else
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: FloatingJoystick mevcut.");
        }
    }
    
    private void SetupUI()
    {
        // ArayuzYoneticisi var mı kontrol et
        ArayuzYoneticisi existingUI = FindFirstObjectByType<ArayuzYoneticisi>();
        
        if (existingUI == null)
        {
            if (showDebugLogs)
                ; // Debug.LogWarning("RenkOyunuSetupManager: ArayuzYoneticisi bulunamadı!");
            
            // Canvas var mı kontrol et
            Canvas canvas = uiCanvas;
            if (canvas == null)
            {
                canvas = FindFirstObjectByType<Canvas>();
            }
            
            if (canvas != null)
            {
                // Basit UI oluştur
                GameObject uiObj = new GameObject("ArayuzYoneticisi");
                uiObj.transform.SetParent(canvas.transform);
                uiObj.AddComponent<ArayuzYoneticisi>();
                
                if (showDebugLogs)
                    ; // Debug.Log("RenkOyunuSetupManager: ArayuzYoneticisi oluşturuldu!");
            }
        }
        else
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: ArayuzYoneticisi mevcut.");
        }
    }

    private void SetupGameStarter()
    {
        // RenkOyunuGameStarter var mı kontrol et
        RenkOyunuGameStarter existingGameStarter = FindFirstObjectByType<RenkOyunuGameStarter>();

        if (existingGameStarter == null)
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: RenkOyunuGameStarter bulunamadı, oluşturuluyor...");

            // Yeni GameObject oluştur
            GameObject gameStarterObj = new GameObject("RenkOyunuGameStarter");

            // NetworkObject ekle
            NetworkObject networkObject = gameStarterObj.AddComponent<NetworkObject>();

            // RenkOyunuGameStarter component'ini ekle
            RenkOyunuGameStarter gameStarter = gameStarterObj.AddComponent<RenkOyunuGameStarter>();

            // Ayarları yap
            gameStarter.minimumOyuncuSayisi = 1;
            gameStarter.oyuncuBeklemeZamani = 2f;
            gameStarter.otomatikBaslat = true;
            gameStarter.lobbySystemActive = true;
            gameStarter.showDebugLogs = showDebugLogs;

            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: RenkOyunuGameStarter oluşturuldu ve ayarlandı!");
        }
        else
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: RenkOyunuGameStarter zaten mevcut.");

            // Mevcut GameStarter'ın ayarlarını güncelle
            existingGameStarter.lobbySystemActive = true;
            existingGameStarter.otomatikBaslat = true;

            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: Mevcut RenkOyunuGameStarter ayarları güncellendi.");
        }
    }

    private void SetupRenkOyunuManager()
    {
        // RenkOyunuManager var mı kontrol et
        RenkOyunuManager existingRenkManager = FindFirstObjectByType<RenkOyunuManager>();

        if (existingRenkManager == null)
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: RenkOyunuManager bulunamadı, oluşturuluyor...");

            // Yeni GameObject oluştur
            GameObject renkManagerObj = new GameObject("RenkOyunuManager");

            // NetworkObject ekle
            NetworkObject networkObject = renkManagerObj.AddComponent<NetworkObject>();

            // RenkOyunuManager component'ini ekle
            RenkOyunuManager renkManager = renkManagerObj.AddComponent<RenkOyunuManager>();

            // Varsayılan ayarları yap
            renkManager.turBeklemeSuresi = 5f;
            renkManager.renkSecimSuresi = 10f;
            renkManager.sureAzalmaMiktari = 0.5f;
            renkManager.minimumSure = 4f;

            // UI Manager'ı bul ve ata
            ArayuzYoneticisi uiManager = FindFirstObjectByType<ArayuzYoneticisi>();
            if (uiManager != null)
            {
                renkManager.uiManager = uiManager;
                if (showDebugLogs)
                    ; // Debug.Log("RenkOyunuSetupManager: RenkOyunuManager'a UI Manager atandı.");
            }

            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: RenkOyunuManager oluşturuldu ve ayarlandı!");
        }
        else
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: RenkOyunuManager zaten mevcut.");

            // UI Manager referansını kontrol et ve gerekirse ata
            if (existingRenkManager.uiManager == null)
            {
                ArayuzYoneticisi uiManager = FindFirstObjectByType<ArayuzYoneticisi>();
                if (uiManager != null)
                {
                    existingRenkManager.uiManager = uiManager;
                    if (showDebugLogs)
                        ; // Debug.Log("RenkOyunuSetupManager: Mevcut RenkOyunuManager'a UI Manager atandı.");
                }
            }
        }
    }

    private void CheckNetworkComponents()
    {
        // RenkOyunuManager var mı kontrol et
        RenkOyunuManager renkManager = FindFirstObjectByType<RenkOyunuManager>();
        if (renkManager == null)
        {
            if (showDebugLogs)
                ; // Debug.LogError("RenkOyunuSetupManager: RenkOyunuManager bulunamadı!");
        }
        
        // CustomNetworkManager var mı kontrol et
        CustomNetworkManager customNetManager = FindFirstObjectByType<CustomNetworkManager>();
        if (customNetManager == null)
        {
            if (showDebugLogs)
                ; // Debug.LogWarning("RenkOyunuSetupManager: CustomNetworkManager bulunamadı!");
        }
        
        // SpawnManager var mı kontrol et
        SpawnManager spawnManager = FindFirstObjectByType<SpawnManager>();
        if (spawnManager == null)
        {
            if (showDebugLogs)
                ; // Debug.LogWarning("RenkOyunuSetupManager: SpawnManager bulunamadı!");
        }
    }
    
    /// <summary>
    /// Oyuncu spawn olduğunda çağrılacak setup
    /// </summary>
    public void SetupPlayerOnSpawn(OyuncuKontrol player)
    {
        if (player == null) return;

        if (showDebugLogs)
            ; // Debug.Log($"RenkOyunuSetupManager: Oyuncu setup'ı yapılıyor - {player.name} (IsOwner: {player.IsOwner})");

        // Kamera target'ını ayarla (sadece local player için)
        if (player.IsOwner)
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: Local player için kamera setup'ı başlatılıyor...");

            StartCoroutine(SetupPlayerCamera(player));
        }
        else
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: Remote player, kamera setup'ı atlanıyor.");
        }
    }
    
    private IEnumerator SetupPlayerCamera(OyuncuKontrol player)
    {
        if (showDebugLogs)
            ; // Debug.Log("RenkOyunuSetupManager: Kamera setup coroutine başladı...");

        // Kameranın hazır olmasını bekle
        yield return new WaitForSeconds(0.2f);

        int attempts = 0;
        while (attempts < 20) // Maksimum 20 deneme (2 saniye)
        {
            InstantHybridCameraController camera = InstantHybridCameraController.instance;

            if (camera != null && player != null && player.cameraFollowTarget != null)
            {
                camera.target = player.cameraFollowTarget;

                if (showDebugLogs)
                    ; // Debug.Log($"RenkOyunuSetupManager: Kamera target'ı ayarlandı! Target: {player.cameraFollowTarget.name}");

                // Kamerayı oyuncunun pozisyonuna yakın bir yere yerleştir
                Vector3 playerPos = player.transform.position;
                camera.transform.position = playerPos + new Vector3(0, 5, -7);
                camera.transform.LookAt(playerPos + Vector3.up * 2);

                if (showDebugLogs)
                    ; // Debug.Log($"RenkOyunuSetupManager: Kamera pozisyonu ayarlandı! Pos: {camera.transform.position}");

                break;
            }
            else
            {
                attempts++;
                if (showDebugLogs && attempts % 5 == 0)
                    Debug.LogWarning($"RenkOyunuSetupManager: Kamera setup denemesi {attempts}/20 - Camera: {camera != null}, Player: {player != null}, Target: {(player?.cameraFollowTarget != null)}");

                yield return new WaitForSeconds(0.1f);
            }
        }

        if (attempts >= 20)
        {
            Debug.LogError("RenkOyunuSetupManager: Kamera setup başarısız! 20 deneme sonrası vazgeçildi.");
        }
    }
    
    [ContextMenu("Manuel Setup Çalıştır")]
    public void ManualSetup()
    {
        StartCoroutine(DelayedSetup());
    }

    [ContextMenu("Debug: Tüm Manager'ları Kontrol Et")]
    public void DebugCheckAllManagers()
    {
        Debug.Log("=== MANAGER KONTROL RAPORU ===");

        // RenkOyunuSetupManager
        Debug.Log($"RenkOyunuSetupManager: {(this != null ? "✓ MEVCUT" : "✗ YOK")}");

        // RenkOyunuGameStarter
        RenkOyunuGameStarter gameStarter = FindFirstObjectByType<RenkOyunuGameStarter>();
        Debug.Log($"RenkOyunuGameStarter: {(gameStarter != null ? "✓ MEVCUT" : "✗ YOK")}");
        if (gameStarter != null)
        {
            Debug.Log($"  - Otomatik Başlat: {gameStarter.otomatikBaslat}");
            Debug.Log($"  - Lobi Sistemi: {gameStarter.lobbySystemActive}");
            Debug.Log($"  - Minimum Oyuncu: {gameStarter.minimumOyuncuSayisi}");
        }

        // RenkOyunuManager
        RenkOyunuManager renkManager = FindFirstObjectByType<RenkOyunuManager>();
        Debug.Log($"RenkOyunuManager: {(renkManager != null ? "✓ MEVCUT" : "✗ YOK")}");
        if (renkManager != null)
        {
            Debug.Log($"  - UI Manager: {(renkManager.uiManager != null ? "✓ ATANMIŞ" : "✗ YOK")}");
            Debug.Log($"  - Tur Süresi: {renkManager.turBeklemeSuresi}s");
            Debug.Log($"  - Renk Seçim Süresi: {renkManager.renkSecimSuresi}s");
        }

        // ArayuzYoneticisi
        ArayuzYoneticisi uiManager = FindFirstObjectByType<ArayuzYoneticisi>();
        Debug.Log($"ArayuzYoneticisi: {(uiManager != null ? "✓ MEVCUT" : "✗ YOK")}");

        // CustomNetworkManager
        CustomNetworkManager customNetworkManager = FindFirstObjectByType<CustomNetworkManager>();
        Debug.Log($"CustomNetworkManager: {(customNetworkManager != null ? "✓ MEVCUT" : "✗ YOK")}");
        if (customNetworkManager != null)
        {
            var spawnPoints = customNetworkManager.GetSpawnPoints();
            Debug.Log($"  - Spawn Points: {(spawnPoints != null ? spawnPoints.Length : 0)} adet");
        }

        // Kamera
        InstantHybridCameraController camera = FindFirstObjectByType<InstantHybridCameraController>();
        Debug.Log($"InstantHybridCameraController: {(camera != null ? "✓ MEVCUT" : "✗ YOK")}");

        // Joystick
        FloatingJoystick joystick = FindFirstObjectByType<FloatingJoystick>();
        Debug.Log($"FloatingJoystick: {(joystick != null ? "✓ MEVCUT" : "✗ YOK")}");

        Debug.Log("=== RAPOR SONU ===");
    }

    [ContextMenu("ACIL: Eksik Manager'ları Oluştur")]
    public void EmergencyCreateMissingManagers()
    {
        Debug.Log("=== ACİL MANAGER OLUŞTURMA ===");

        SetupGameStarter();
        SetupRenkOyunuManager();
        SetupUI();
        SetupCamera();
        SetupJoystick();

        Debug.Log("Tüm eksik manager'lar oluşturuldu!");
        Debug.Log("'Debug: Tüm Manager'ları Kontrol Et' ile doğrulayın.");
    }

    [ContextMenu("Debug: Spawn Point Bilgilerini Göster")]
    public void DebugShowSpawnPointInfo()
    {
        Debug.Log($"=== SPAWN POINT BİLGİLERİ ===");
        Debug.Log($"Mod: {spawnMode}");

        switch (spawnMode)
        {
            case SpawnPointMode.ManualPositions:
                Debug.Log($"Manuel Pozisyonlar ({manualSpawnPoints?.Length ?? 0} adet):");
                if (manualSpawnPoints != null)
                {
                    for (int i = 0; i < manualSpawnPoints.Length; i++)
                    {
                        var spawn = manualSpawnPoints[i];
                        Debug.Log($"  {i}: {spawn.name} - Pos: {spawn.position}, Rot: {spawn.rotation}, Aktif: {spawn.isActive}");
                    }
                }
                break;

            case SpawnPointMode.SceneTransforms:
                Debug.Log($"Sahne Transform'ları ({sceneSpawnTransforms?.Length ?? 0} adet):");
                if (sceneSpawnTransforms != null)
                {
                    for (int i = 0; i < sceneSpawnTransforms.Length; i++)
                    {
                        var t = sceneSpawnTransforms[i];
                        if (t != null)
                            Debug.Log($"  {i}: {t.name} - Pos: {t.position}, Rot: {t.eulerAngles}");
                        else
                            Debug.Log($"  {i}: NULL");
                    }
                }
                break;

            case SpawnPointMode.AutoDetectTags:
                GameObject[] tagged = GameObject.FindGameObjectsWithTag("SpawnPoint");
                Debug.Log($"=== AUTO DETECT TAGS DETAYLI RAPOR ===");
                Debug.Log($"Tag'li Objeler ({tagged.Length} adet):");

                if (tagged.Length == 0)
                {
                    Debug.LogWarning("HİÇ 'SpawnPoint' TAG'Lİ OBJE BULUNAMADI!");
                    Debug.LogWarning("Çözüm 1: Sahnenizde istediğiniz spawn pozisyonlarına boş GameObject ekleyin");
                    Debug.LogWarning("Çözüm 2: Bu objelere 'SpawnPoint' tag'ini verin");
                    Debug.LogWarning("Çözüm 3: Veya ManualPositions modunu kullanın");
                }
                else
                {
                    for (int i = 0; i < tagged.Length; i++)
                    {
                        var obj = tagged[i];
                        Debug.Log($"  {i}: {obj.name}");
                        Debug.Log($"      Pozisyon: {obj.transform.position}");
                        Debug.Log($"      Rotasyon: {obj.transform.eulerAngles}");
                        Debug.Log($"      Parent: {(obj.transform.parent ? obj.transform.parent.name : "YOK")}");
                        Debug.Log($"      Aktif: {obj.activeInHierarchy}");

                        // SpawnPoint component'i var mı kontrol et
                        SpawnPoint spawnComponent = obj.GetComponent<SpawnPoint>();
                        if (spawnComponent != null)
                        {
                            Debug.Log($"      SpawnPoint Component: Aktif={spawnComponent.isActive}, Öncelik={spawnComponent.priority}");
                        }
                        else
                        {
                            Debug.Log($"      SpawnPoint Component: YOK (sadece tag var)");
                        }
                        Debug.Log($"      ---");
                    }
                }
                Debug.Log($"=== RAPOR SONU ===");
                break;
        }

        Debug.Log($"Platform Yükseklik Algılama: {autoDetectPlatformHeight}");
        if (autoDetectPlatformHeight)
        {
            float height = DetectPlatformHeight();
            Debug.Log($"Algılanan Platform Yüksekliği: {height}");
        }
    }

    [ContextMenu("Debug: Test Spawn Point Oluşturma")]
    public void DebugTestSpawnPointCreation()
    {
        List<Transform> testList = new List<Transform>();

        switch (spawnMode)
        {
            case SpawnPointMode.ManualPositions:
                CreateSpawnPointsFromManualData(testList);
                break;
            case SpawnPointMode.SceneTransforms:
                CreateSpawnPointsFromSceneTransforms(testList);
                break;
            case SpawnPointMode.AutoDetectTags:
                CreateSpawnPointsFromTags(testList);
                break;
        }

        Debug.Log($"Test: {testList.Count} adet spawn point oluşturuldu.");

        // Test objelerini temizle
        foreach (var t in testList)
        {
            if (t != null && t.gameObject.name.StartsWith("SpawnPoint_"))
            {
                DestroyImmediate(t.gameObject);
            }
        }
    }

    [ContextMenu("ACIL: Manuel Moda Geç ve Varsayılan Pozisyonlar Oluştur")]
    public void EmergencySwitchToManualMode()
    {
        Debug.Log("=== ACİL ÇÖZÜM: MANUEL MODA GEÇİLİYOR ===");

        // Modu değiştir
        spawnMode = SpawnPointMode.ManualPositions;

        // ÖNEMLİ: Platform yükseklik algılamayı KAPAT (tam Y kontrolü için)
        autoDetectPlatformHeight = false;

        // Varsayılan pozisyonları oluştur
        manualSpawnPoints = new SpawnPointData[]
        {
            new SpawnPointData { position = new Vector3(0, 5, 0), rotation = Vector3.zero, name = "Merkez", isActive = true },
            new SpawnPointData { position = new Vector3(-5, 5, 0), rotation = Vector3.zero, name = "Sol", isActive = true },
            new SpawnPointData { position = new Vector3(5, 5, 0), rotation = Vector3.zero, name = "Sağ", isActive = true },
            new SpawnPointData { position = new Vector3(0, 5, -5), rotation = Vector3.zero, name = "Arka", isActive = true },
            new SpawnPointData { position = new Vector3(0, 5, 5), rotation = Vector3.zero, name = "Ön", isActive = true }
        };

        Debug.Log("Manuel mod aktif edildi ve 5 varsayılan spawn point oluşturuldu!");
        Debug.Log("Platform yükseklik algılama KAPATILDI - Artık Y eksenini tam kontrol edebilirsiniz!");
        Debug.Log("Inspector'da pozisyonları istediğiniz gibi ayarlayabilirsiniz.");

        // Hemen setup'ı yeniden çalıştır
        SetupSpawnPoints();
    }

    [ContextMenu("Y Ekseni Kontrolü: Platform Algılamayı KAPAT")]
    public void DisablePlatformHeightDetection()
    {
        autoDetectPlatformHeight = false;
        Debug.Log("=== Y EKSENİ TAM KONTROL AKTİF ===");
        Debug.Log("Platform yükseklik algılama KAPATILDI!");
        Debug.Log("Artık Inspector'da Y değerlerini tam olarak kontrol edebilirsiniz.");
        Debug.Log("Spawn point'lerinizin Y değerleri aynen kullanılacak.");

        // Setup'ı yeniden çalıştır
        SetupSpawnPoints();
    }

    [ContextMenu("Y Ekseni Kontrolü: Platform Algılamayı AÇ")]
    public void EnablePlatformHeightDetection()
    {
        autoDetectPlatformHeight = true;
        Debug.Log("=== PLATFORM YÜKSEKLIK ALGILAMA AKTİF ===");
        Debug.Log("Platform yükseklik algılama AÇILDI!");
        Debug.Log("Y değerleri otomatik olarak platform yüksekliğine ayarlanacak.");

        float detectedHeight = DetectPlatformHeight();
        Debug.Log($"Algılanan platform yüksekliği: {detectedHeight}");

        // Setup'ı yeniden çalıştır
        SetupSpawnPoints();
    }

    [ContextMenu("Debug: Sahnedeki Tüm SpawnPoint Tag'li Objeleri Listele")]
    public void DebugListAllSpawnPointTags()
    {
        GameObject[] allObjects = FindObjectsByType<GameObject>(FindObjectsSortMode.None);
        GameObject[] spawnTagged = GameObject.FindGameObjectsWithTag("SpawnPoint");

        Debug.Log($"=== SAHNE SPAWN POINT TARAMASI ===");
        Debug.Log($"Toplam obje sayısı: {allObjects.Length}");
        Debug.Log($"'SpawnPoint' tag'li obje sayısı: {spawnTagged.Length}");

        if (spawnTagged.Length > 0)
        {
            Debug.Log("SpawnPoint tag'li objeler:");
            for (int i = 0; i < spawnTagged.Length; i++)
            {
                var obj = spawnTagged[i];
                Debug.Log($"  {i}: {obj.name} - {obj.transform.position}");
            }
        }
        else
        {
            Debug.LogWarning("HİÇ SpawnPoint tag'li obje bulunamadı!");

            // İsimde "spawn" geçen objeleri ara
            List<GameObject> spawnNamedObjects = new List<GameObject>();
            foreach (var obj in allObjects)
            {
                if (obj.name.ToLower().Contains("spawn"))
                {
                    spawnNamedObjects.Add(obj);
                }
            }

            if (spawnNamedObjects.Count > 0)
            {
                Debug.Log($"İsimde 'spawn' geçen objeler bulundu ({spawnNamedObjects.Count} adet):");
                for (int i = 0; i < spawnNamedObjects.Count; i++)
                {
                    Debug.Log($"  {i}: {spawnNamedObjects[i].name} - {spawnNamedObjects[i].transform.position}");
                }
                Debug.Log("Bu objelere 'SpawnPoint' tag'i verebilirsiniz!");
            }
            else
            {
                Debug.LogWarning("İsimde 'spawn' geçen obje de bulunamadı.");
                Debug.LogWarning("Manuel mod kullanmanızı öneriyoruz!");
            }
        }
    }
    
    [ContextMenu("Sahne Durumunu Kontrol Et")]
    public void CheckSceneStatus()
    {
        Debug.Log("=== SAHNE DURUM RAPORU ===");
        
        // Kamera kontrolü
        InstantHybridCameraController camera = FindFirstObjectByType<InstantHybridCameraController>();
        Debug.Log($"InstantHybridCameraController: {(camera != null ? "✓ Mevcut" : "✗ Eksik")}");
        
        // Joystick kontrolü
        FloatingJoystick joystick = FindFirstObjectByType<FloatingJoystick>();
        Debug.Log($"FloatingJoystick: {(joystick != null ? "✓ Mevcut" : "✗ Eksik")}");
        
        // UI kontrolü
        ArayuzYoneticisi ui = FindFirstObjectByType<ArayuzYoneticisi>();
        Debug.Log($"ArayuzYoneticisi: {(ui != null ? "✓ Mevcut" : "✗ Eksik")}");
        
        // Network kontrolü
        RenkOyunuManager renkManager = FindFirstObjectByType<RenkOyunuManager>();
        Debug.Log($"RenkOyunuManager: {(renkManager != null ? "✓ Mevcut" : "✗ Eksik")}");
        
        // Oyuncu kontrolü
        OyuncuKontrol[] players = FindObjectsByType<OyuncuKontrol>(FindObjectsSortMode.None);
        Debug.Log($"Oyuncu sayısı: {players.Length}");
        
        Debug.Log("=== RAPOR SONU ===");
    }

    private void NotifySetupComplete()
    {
        if (showDebugLogs)
            ; // Debug.Log("RenkOyunuSetupManager: Setup tamamlandı, oyun başlatma bildirimi gönderiliyor...");

        // GameManager'a setup tamamlandığını bildir
        GameManager gameManager = FindFirstObjectByType<GameManager>();
        if (gameManager != null)
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: GameManager bulundu, setup tamamlandı bildirimi gönderildi.");
        }

        // RenkOyunuGameStarter'a da bildir
        RenkOyunuGameStarter gameStarter = FindFirstObjectByType<RenkOyunuGameStarter>();
        if (gameStarter != null)
        {
            if (showDebugLogs)
                ; // Debug.Log("RenkOyunuSetupManager: RenkOyunuGameStarter bulundu.");
        }
    }

    // Scene view'da spawn pozisyonlarını görselleştir - YENİ SİSTEM
    private void OnDrawGizmos()
    {
        if (!showSpawnPositionsInScene) return;

        // Seçilen moda göre farklı görselleştirme
        switch (spawnMode)
        {
            case SpawnPointMode.ManualPositions:
                DrawManualSpawnPointGizmos();
                break;

            case SpawnPointMode.SceneTransforms:
                DrawSceneTransformGizmos();
                break;

            case SpawnPointMode.AutoDetectTags:
                DrawAutoDetectGizmos();
                break;
        }
    }

    private void DrawManualSpawnPointGizmos()
    {
        if (manualSpawnPoints == null) return;

        float platformHeight = autoDetectPlatformHeight ? DetectPlatformHeight() : 2f;

        for (int i = 0; i < manualSpawnPoints.Length; i++)
        {
            var spawnData = manualSpawnPoints[i];

            // Aktif/pasif duruma göre renk
            Gizmos.color = spawnData.isActive ? spawnPositionGizmoColor : Color.gray;

            Vector3 finalPos = spawnData.position;
            Vector3 originalPos = spawnData.position;

            if (autoDetectPlatformHeight)
            {
                finalPos.y = platformHeight + 1f;

                // Platform algılama aktifse, orijinal pozisyonu da göster
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(originalPos, 0.3f);
                Gizmos.DrawLine(originalPos, finalPos);
            }

            // Final spawn pozisyonu göster
            Gizmos.color = spawnData.isActive ? spawnPositionGizmoColor : Color.gray;
            Gizmos.DrawWireSphere(finalPos, 0.5f);
            Gizmos.DrawLine(finalPos, finalPos + Vector3.up * 2f);

            // Rotasyon göster
            Gizmos.color = Color.yellow;
            Vector3 forward = Quaternion.Euler(spawnData.rotation) * Vector3.forward;
            Gizmos.DrawRay(finalPos, forward * 1.5f);

            #if UNITY_EDITOR
            if (Application.isEditor)
            {
                string label = $"{i}: {spawnData.name}";
                if (!spawnData.isActive) label += " (KAPALI)";

                if (autoDetectPlatformHeight)
                {
                    label += $"\nY: {originalPos.y} → {finalPos.y}";
                    label += "\n(Platform Algılama AKTİF)";
                }
                else
                {
                    label += $"\nY: {finalPos.y} (Manuel)";
                }

                UnityEditor.Handles.Label(finalPos + Vector3.up * 2.5f, label);
            }
            #endif
        }
    }

    private void DrawSceneTransformGizmos()
    {
        if (sceneSpawnTransforms == null) return;

        Gizmos.color = Color.blue;

        for (int i = 0; i < sceneSpawnTransforms.Length; i++)
        {
            if (sceneSpawnTransforms[i] != null)
            {
                Vector3 pos = sceneSpawnTransforms[i].position;
                Gizmos.DrawWireCube(pos, Vector3.one);
                Gizmos.DrawLine(pos, pos + Vector3.up * 2f);

                #if UNITY_EDITOR
                if (Application.isEditor)
                {
                    UnityEditor.Handles.Label(pos + Vector3.up * 2.5f, $"Transform {i}: {sceneSpawnTransforms[i].name}");
                }
                #endif
            }
        }
    }

    private void DrawAutoDetectGizmos()
    {
        GameObject[] taggedSpawns = GameObject.FindGameObjectsWithTag("SpawnPoint");
        Gizmos.color = Color.cyan;

        for (int i = 0; i < taggedSpawns.Length; i++)
        {
            Vector3 pos = taggedSpawns[i].transform.position;
            Gizmos.DrawWireCube(pos, Vector3.one * 0.8f);
            Gizmos.DrawLine(pos, pos + Vector3.up * 2f);

            #if UNITY_EDITOR
            if (Application.isEditor)
            {
                UnityEditor.Handles.Label(pos + Vector3.up * 2.5f, $"Auto {i}: {taggedSpawns[i].name}");
            }
            #endif
        }
    }
}
