using UnityEngine;
using Unity.Netcode;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Oyunun genel durumunu (süre, bitirenler, tur durumu) yöneten ve tüm oyuncular arasında senkronize eden ana yönetici.
/// Bu script, sunucu otoriterli bir yapı kullanır.
/// </summary>
public class GameManager : NetworkBehaviour
{
    // Singleton deseni, BitisCizgisi gibi diğer script'lerin bu yöneticiye kolayca erişmesini sağlar.
    public static GameManager Instance { get; private set; }

    [Header("Tur Kuralları")]
    public float turSuresi = 240f;

    [Header("Bitiş Ayarları")]
    public float agirCekimGucu = 0.1f;
    public float agirCekimSuresi = 2f;

    // Sahne yönetimi artık UniversalSceneManager tarafından yapılıyor

    [Header("Referanslar")]
    public UIManager uiManager;

    // --- AĞ DEĞİŞKENLERİ (NETWORK VARIABLES) ---
    private NetworkVariable<float> _kalanSure = new NetworkVariable<float>(0f, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);
    private NetworkVariable<int> _bitirenOyuncuSayisi = new NetworkVariable<int>(0, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);
    private NetworkVariable<bool> _turDevamEdiyor = new NetworkVariable<bool>(true, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);
    private NetworkVariable<int> _gecmesiGerekenOyuncuSayisi = new NetworkVariable<int>(2, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);

    // KAZANANLAR LİSTESİ: NetworkList sayesinde bu liste sunucuda her değiştiğinde tüm client'lara senkronize edilir.
    private NetworkList<ulong> _bitirenClientIDs;

    // --- EVENT TABANLI SPECTATE SİSTEMİ ---
    public static event System.Action OnPlayerListChanged;

    private void NotifyPlayerListChanged()
    {
        OnPlayerListChanged?.Invoke();
    }

    private void Awake()
    {
        // ⚠️ KRİTİK DÜZELTME: NetworkObject'leri client'ta asla destroy etme! ⚠️
        if (Instance != null && Instance != this)
        {
            if (NetworkManager.Singleton != null && NetworkManager.Singleton.IsListening)
            {
                // Network aktifse, sadece server destroy edebilir
                if (IsServer)
                {
                    Destroy(gameObject);
                }
                else
                {
                    // Client'ta ise sadece Instance'ı güncelle, destroy etme
                    Instance = this;
                }
            }
            else
            {
                // Network aktif değilse (offline mod) normal destroy
                Destroy(gameObject);
            }
        }
        else
        {
            Instance = this;
        }

        // NetworkList'i burada oluşturuyoruz.
        _bitirenClientIDs = new NetworkList<ulong>();

        // Zamanı başlangıçta normale ayarla.
        Time.timeScale = 1f;
    }

    public override void OnNetworkSpawn()
    {
        // Bu blok hem client'lar hem de Host (sunucu+client) için çalışır.
        if (IsClient)
        {
            // 1. ADIM: GELECEKTEKİ DEĞİŞİMLERİ DİNLE
            _kalanSure.OnValueChanged += HandleKalanSureChanged;
            _bitirenOyuncuSayisi.OnValueChanged += HandleBitirenSayisiChanged;
            _gecmesiGerekenOyuncuSayisi.OnValueChanged += HandleGecmesiGerekenSayisiChanged;

            // 2. ADIM: MEVCUT DEĞERLERLE UI'ı ANINDA GÜNCELLE
            // Bu, oyuna sonradan katılanların o anki doğru bilgiyi görmesini sağlar.
            HandleKalanSureChanged(0, _kalanSure.Value);
            HandleBitirenSayisiChanged(0, _bitirenOyuncuSayisi.Value);
            HandleGecmesiGerekenSayisiChanged(0, _gecmesiGerekenOyuncuSayisi.Value);
        }

        // Bu blok sadece sunucu ilk defa spawn olduğunda çalışır ve başlangıç değerlerini ayarlar.
        if (IsServer)
        {
            // Dinamik oyuncu sayısını ayarla
            UpdateDynamicPlayerCount();

            // Client connection event'lerini dinle
            NetworkManager.Singleton.OnClientConnectedCallback += OnClientConnected;
            NetworkManager.Singleton.OnClientDisconnectCallback += OnClientDisconnected;

            _kalanSure.Value = turSuresi;
            _bitirenOyuncuSayisi.Value = 0;
            _turDevamEdiyor.Value = true;
            _bitirenClientIDs.Clear(); // Tur başladığında listeyi temizle.

            // Tüm oyuncuların sahne yüklenmesini bekle, sonra oyunu başlat
            StartCoroutine(TumOyuncularSahneYuklenmesiniBekle());
        }
    }

    public override void OnNetworkDespawn()
    {
        // Obje yok olduğunda event aboneliklerini ve NetworkList'i hafızadan temizlemek iyi bir pratiktir.
        if (IsClient)
        {
            _kalanSure.OnValueChanged -= HandleKalanSureChanged;
            _bitirenOyuncuSayisi.OnValueChanged -= HandleBitirenSayisiChanged;
            _gecmesiGerekenOyuncuSayisi.OnValueChanged -= HandleGecmesiGerekenSayisiChanged;
        }

        // Server'da client connection event'lerini temizle
        if (IsServer && NetworkManager.Singleton != null)
        {
            NetworkManager.Singleton.OnClientConnectedCallback -= OnClientConnected;
            NetworkManager.Singleton.OnClientDisconnectCallback -= OnClientDisconnected;
        }

        _bitirenClientIDs?.Dispose();
    }

    private IEnumerator DelayedGameStart()
    {
        // Network spawn'ların ve setup'ların tamamlanmasını bekle
        yield return new WaitForSeconds(1f);

        // Coroutine ile biraz bekleyip RenkOyunuGameStarter'ı bul
        StartCoroutine(DelayedGameStarterSignal());
    }

    private System.Collections.IEnumerator DelayedGameStarterSignal()
    {
        // Sahne tam yüklenmesini bekle
        yield return new WaitForSeconds(1f);

        // RenkOyunuManager'ı direkt başlat (RenkOyunuGameStarter olmadan)
        RenkOyunuManager renkManager = RenkOyunuManager.Instance;
        if (renkManager != null)
        {
            // RenkOyunuManager otomatik başlıyor, ek bir şey yapmaya gerek yok
        }
    }

    private void HandleKalanSureChanged(float previousValue, float newValue)
    {
        // Null check to prevent crashes during network initialization
        if (uiManager != null)
        {
            uiManager.ZamanGuncelle(turSuresi, turSuresi - newValue);
        }
    }

    private void HandleBitirenSayisiChanged(int previousValue, int newValue)
    {
        // Null check to prevent crashes during network initialization
        if (uiManager != null)
        {
            uiManager.OyuncuSayisiGuncelle(newValue, _gecmesiGerekenOyuncuSayisi.Value);
        }
    }

    private void HandleGecmesiGerekenSayisiChanged(int previousValue, int newValue)
    {
        // Geçmesi gereken oyuncu sayısı değiştiğinde UI'ı güncelle
        // Null check to prevent crashes during network initialization
        if (uiManager != null)
        {
            uiManager.OyuncuSayisiGuncelle(_bitirenOyuncuSayisi.Value, newValue);
        }
    }
    
    private void Update()
    {
        // Zamanı sadece sunucu ileri sarar.
        if (!IsServer || !_turDevamEdiyor.Value) return;

        _kalanSure.Value -= Time.deltaTime;

        if (_kalanSure.Value <= 0)
        {
            _kalanSure.Value = 0;
            TuruBitir();
        }
    }

    [ServerRpc(RequireOwnership = false)]
    public void PlayerCrossedFinishLineServerRpc(ServerRpcParams rpcParams = default)
    {
        ulong clientID = rpcParams.Receive.SenderClientId;
        if (!_turDevamEdiyor.Value || _bitirenClientIDs.Contains(clientID)) return;

        _bitirenClientIDs.Add(clientID);
        _bitirenOyuncuSayisi.Value = _bitirenClientIDs.Count;

        // --- OYUNCU LİSTESİ DEĞİŞTİ ---
        NotifyPlayerListChanged();

        // Sıralamaya göre ödül ver (finish line bonus)
        int sirasi = _bitirenClientIDs.Count; // 1'den başlar
        int odul = 0;
        if (sirasi == 1) odul = 200;      // İlk bitiren
        else if (sirasi == 2) odul = 150; // İkinci bitiren
        else if (sirasi == 3) odul = 130; // Üçüncü bitiren
        else if (sirasi == 4) odul = 120;
        else if (sirasi == 5) odul = 110;
        else if (sirasi == 6) odul = 100;
        else if (sirasi == 7) odul = 90;
        else if (sirasi == 8) odul = 80;
        else if (sirasi == 9) odul = 70;
        else if (sirasi == 10) odul = 60;
        else if (sirasi == 11) odul = 50;
        else if (sirasi == 12) odul = 40;
        else if (sirasi == 13) odul = 30;
        else if (sirasi == 14) odul = 20;
        else if (sirasi == 15) odul = 10;
        else odul = 5;

        CoinEkleServerRpc(odul, new ServerRpcParams { Receive = new ServerRpcReceiveParams { SenderClientId = clientID } });

        // ✅ YENİ: Tüm oyuncular bitirince tur bitsin
        if (_bitirenOyuncuSayisi.Value >= _gecmesiGerekenOyuncuSayisi.Value)
        {
            TuruBitir();
        }
    }

    // ❌ KALDIRILDI: BaslatSpectateClientRpc metodu
    // Artık tüm oyuncular sonuna kadar oynuyor, spectate gerekli değil
    
    private void TuruBitir()
    {
        if (!_turDevamEdiyor.Value) return;
        _turDevamEdiyor.Value = false;
        TuruBitirClientRpc();
    }

    [ClientRpc]
    private void TuruBitirClientRpc()
    {
        StartCoroutine(TuruBitirSequence());
    }
    
    private IEnumerator TuruBitirSequence()
    {
        // Pürüzsüz yavaşlatma efekti
        yield return StartCoroutine(PurussuzYavaslatma(1f, agirCekimGucu, agirCekimSuresi * 0.5f));

        // Yavaş modda biraz bekle
        yield return new WaitForSecondsRealtime(agirCekimSuresi * 0.5f);

        // Pürüzsüz durdurma efekti
        yield return StartCoroutine(PurussuzYavaslatma(agirCekimGucu, 0f, 0.5f));

        // Her client, senkronize edilmiş olan _bitirenClientIDs listesine bakarak kendi durumunu kontrol eder.
        ulong localPlayerId = NetworkManager.Singleton.LocalClientId;
        // --- PUAN SİSTEMİ: EN ÇOK COIN TOPLAYAN KAZANIR ---
        ulong kazananId = KazananClientId();
        bool oyuncuKazandiMi = (localPlayerId == kazananId);

        uiManager.SonucEkraniniGoster(oyuncuKazandiMi);

        // Sonuç ekranını göster ve sonraki tura geçmek için bekle
        yield return new WaitForSecondsRealtime(3f); // 3 saniye sonuç ekranı göster

        // Time scale'i normale döndür
        Time.timeScale = 1f;

        // Sadece server sonraki tura geçiş yapabilir
        if (IsServer)
        {
            // UniversalSceneManager'a oyun bitişini bildir
            if (UniversalSceneManager.Instance != null)
            {
                UniversalSceneManager.Instance.OyunBitti();
            }
        }
    }

    /// <summary>
    /// Time.timeScale'i pürüzsüz bir şekilde değiştiren coroutine
    /// </summary>
    /// <param name="baslangicHizi">Başlangıç hızı</param>
    /// <param name="hedefHiz">Hedef hız</param>
    /// <param name="gecissuresi">Geçiş süresi (saniye)</param>
    private IEnumerator PurussuzYavaslatma(float baslangicHizi, float hedefHiz, float gecisSuresi)
    {
        float gecenSure = 0f;

        while (gecenSure < gecisSuresi)
        {
            gecenSure += Time.unscaledDeltaTime; // unscaledDeltaTime kullanıyoruz çünkü timeScale değişiyor
            float t = gecenSure / gecisSuresi;

            // Smooth interpolation (ease-in-out curve)
            t = t * t * (3f - 2f * t); // Smoothstep formula

            Time.timeScale = Mathf.Lerp(baslangicHizi, hedefHiz, t);
            yield return null;
        }

        // Son değeri kesin olarak ayarla
        Time.timeScale = hedefHiz;
    }

    // Her oyuncunun puanını server tarafında artırmak için fonksiyon
    [ServerRpc(RequireOwnership = false)]
    public void CoinEkleServerRpc(int miktar, ServerRpcParams rpcParams = default)
    {
        ulong clientID = rpcParams.Receive.SenderClientId;
        if (NetworkManager.Singleton.ConnectedClients.TryGetValue(clientID, out var client))
        {
            var oyuncu = client.PlayerObject.GetComponent<OyuncuKontrol>();
            if (oyuncu != null)
            {
                oyuncu.puan.Value += miktar;
                // --- COIN UI GÜNCELLEME ---
                // Artık gerek yok: CoinGuncelleClientRpc(oyuncu.puan.Value, clientID);
            }
        }
    }



    // Oyun sonunda en çok coin/puan toplayanı bul
    private ulong KazananClientId()
    {
        ulong kazanan = 0;
        int maxPuan = int.MinValue;
        foreach (var clientId in NetworkManager.Singleton.ConnectedClientsIds)
        {
            if (NetworkManager.Singleton.ConnectedClients.TryGetValue(clientId, out var client))
            {
                var oyuncu = client.PlayerObject.GetComponent<OyuncuKontrol>();
                if (oyuncu != null && oyuncu.puan.Value > maxPuan)
                {
                    maxPuan = oyuncu.puan.Value;
                    kazanan = clientId;
                }
            }
        }
        return kazanan;
    }

    /// <summary>
    /// İzleme modu için geçerli olan tüm hedefleri (elenmemiş oyuncuları) döndürür.
    /// </summary>
    /// <returns>İzlenebilecek oyuncuların OyuncuKontrol component'lerinin bir listesi.</returns>
    public List<OyuncuKontrol> GetValidSpectateTargets()
    {
        var targets = new List<OyuncuKontrol>();
        if (NetworkManager.Singleton == null) return targets;

        // Bağlı olan tüm client'ları döngüye al
        foreach (var client in NetworkManager.Singleton.ConnectedClients.Values)
        {
            if (client.PlayerObject == null) continue;

            var oyuncu = client.PlayerObject.GetComponent<OyuncuKontrol>();
            // Oyuncu bulunuyorsa ve elenmemişse listeye ekle
            if (oyuncu != null && !oyuncu.elendi.Value)
            {
                targets.Add(oyuncu);
            }
        }
        return targets;
    }

    // --- DİNAMİK OYUNCU SAYISI YÖNETİMİ ---

    /// <summary>
    /// Dinamik oyuncu sayısını günceller - toplam bağlı oyuncu sayısına eşitler
    /// </summary>
    private void UpdateDynamicPlayerCount()
    {
        if (!IsServer) return;

        int toplamOyuncuSayisi = NetworkManager.Singleton.ConnectedClients.Count;
        _gecmesiGerekenOyuncuSayisi.Value = toplamOyuncuSayisi;

        // UI'ı güncelle
        if (uiManager != null)
        {
            uiManager.UpdatePlayerCountDisplay(toplamOyuncuSayisi);
        }
    }

    /// <summary>
    /// Yeni oyuncu bağlandığında çağrılır
    /// </summary>
    private void OnClientConnected(ulong clientId)
    {
        if (!IsServer) return;

        UpdateDynamicPlayerCount();
    }

    /// <summary>
    /// Oyuncu ayrıldığında çağrılır
    /// </summary>
    private void OnClientDisconnected(ulong clientId)
    {
        if (!IsServer) return;

        // Eğer oyuncu bitiren listesindeyse çıkar
        if (_bitirenClientIDs.Contains(clientId))
        {
            for (int i = _bitirenClientIDs.Count - 1; i >= 0; i--)
            {
                if (_bitirenClientIDs[i] == clientId)
                {
                    _bitirenClientIDs.RemoveAt(i);
                    _bitirenOyuncuSayisi.Value = _bitirenClientIDs.Count;
                    break;
                }
            }
        }

        UpdateDynamicPlayerCount();
    }

    // Sahne yönetimi artık UniversalSceneManager tarafından yapılıyor

    // Sahne yönetimi metodları UniversalSceneManager'a taşındı

    /// <summary>
    /// Tüm oyuncuların sahne yüklenmesini bekle
    /// </summary>
    private System.Collections.IEnumerator TumOyuncularSahneYuklenmesiniBekle()
    {
        if (!IsServer) yield break;

        float beklemeSuresi = 0f;
        float maksimumBekleme = 20f; // 20 saniye maksimum bekleme
        int sonOyuncuSayisi = 0;
        float sabitSayiSuresi = 0f;

        while (beklemeSuresi < maksimumBekleme)
        {
            int mevcutOyuncuSayisi = NetworkManager.Singleton.ConnectedClientsIds.Count;

            // Oyuncu sayısı değişti mi?
            if (mevcutOyuncuSayisi != sonOyuncuSayisi)
            {
                sonOyuncuSayisi = mevcutOyuncuSayisi;
                sabitSayiSuresi = 0f;
            }
            else
            {
                sabitSayiSuresi += 0.5f;
            }

            // Oyuncu sayısı 3 saniye boyunca sabit kaldıysa başlat
            if (sabitSayiSuresi >= 3f)
            {
                break;
            }

            // Tek oyuncuysa direkt başlat
            if (mevcutOyuncuSayisi <= 1)
            {
                break;
            }

            yield return new WaitForSeconds(0.5f);
            beklemeSuresi += 0.5f;
        }

        // Oyunu başlat
        StartCoroutine(DelayedGameStart());
    }

    /// <summary>
    /// Mini oyunlar tarafından tur bitirme için çağrılabilir (sadece server'da)
    /// </summary>
    public void ManuelTurBitir()
    {
        if (!IsServer)
        {
            return;
        }

        if (!_turDevamEdiyor.Value)
        {
            return;
        }

        TuruBitir();
    }
}