using UnityEngine;
using Unity.Netcode;
using System.Collections.Generic;

/// <summary>
/// Doğru/Yanlış blok sistemi - Squid Game tarzı - MOBİL OPTİMİZE
/// Yanlış bloğa basıldığında oyuncu düşer ve ragdoll aktif olur
/// Çift ID sistemi ile doğru/yanlış dağılımı
/// Mobil cihazlar için optimize edilmiş versiyon
/// </summary>
public class DogruYanlisBlok : NetworkBehaviour
{
    [Header("Blok Ayarları")]
    [SerializeField] private bool dogruBlok = true; // True = doğru blok, False = yanlış blok
    [SerializeField] private Material dogruMaterial;
    [SerializeField] private Material yanlisMaterial;
    [SerializeField] private float ragdollSuresi = 3f; // Ragdoll aktif kalma süresi
    
    [Header("ID Sistemi")]
    [SerializeField] private int blokID = 0; // Bu bloğun ID'si (0,1,2,3,4,5...)
    
    [Header("Düşme Ayarları")]
    [SerializeField] private float dusmeHizi = 10f; // Bloğun düşme hızı
    [SerializeField] private float dusmeMesafesi = 20f; // Bloğun ne kadar aşağı düşeceği
    
    [Header("Efektler")]
    [SerializeField] private GameObject kirilmaEfekti; // Kırılma partikül efekti
    [SerializeField] private AudioClip kirilmaSesi; // Kırılma sesi
    [SerializeField] private AudioClip dogruSesi; // Doğru blok sesi
    
    [Header("Mobil Optimizasyon")]
    [SerializeField] private bool mobilOptimizasyon = true; // Mobil optimizasyonları aktif et
    [SerializeField] private int maxEfektSayisi = 3; // Aynı anda maksimum efekt sayısı
    [SerializeField] private float efektYokEtmeSuresi = 2f; // Efektleri daha erken yok et
    
    // Çift ID sistemi için static değişkenler - Mobil için optimize
    private static Dictionary<int, List<DogruYanlisBlok>> ciftBloklar = new Dictionary<int, List<DogruYanlisBlok>>(8); // Başlangıç kapasitesi
    private static Dictionary<int, bool> ciftDagitildi = new Dictionary<int, bool>(8);
    
    // Mobil optimizasyon için static değişkenler
    private static int aktifEfektSayisi = 0;
    private static readonly Queue<GameObject> efektHavuzu = new Queue<GameObject>(5); // Object pooling
    
    private Renderer blokRenderer;
    private Collider blokCollider;
    private AudioSource audioSource;
    private bool kullanildi = false; // Bloğun daha önce kullanılıp kullanılmadığı
    private Vector3 baslangicPozisyonu; // Bloğun başlangıç pozisyonu
    
    private void Awake()
    {
        blokRenderer = GetComponent<Renderer>();
        blokCollider = GetComponent<Collider>();
        audioSource = GetComponent<AudioSource>();
        
        // AudioSource yoksa ekle
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }
    }
    
    private void Start()
    {
        // Başlangıç pozisyonunu kaydet
        baslangicPozisyonu = transform.position;
        
        // Bu bloğu çift grubuna ekle
        CiftGrubunaEkle();
        
        // Çift için doğru/yanlış dağılımını yap
        CiftDogruYanlisDagit();
        
        // Blok tipine göre materyali ayarla
        MateryaliGuncelle();
    }
    
    private void OnTriggerEnter(Collider other)
    {
        // Daha önce kullanıldıysa işlem yapma
        if (kullanildi) return;
        
        // Oyuncu kontrolü olan objeyi kontrol et
        OyuncuKontrol oyuncu = other.GetComponent<OyuncuKontrol>();
        if (oyuncu == null) return;
        
        // Sadece kendi oyuncumuz için işlem yap (client tabanlı)
        if (!oyuncu.IsOwner) return;
        
        kullanildi = true;
        
        if (dogruBlok)
        {
            // Doğru blok - ses çal ve server'a bildir
            DogruBlokEtkisiUygula();
            if (IsServer)
            {
                DogruBlokEtkisiClientRpc();
            }
            else
            {
                DogruBlokServerRpc();
            }
        }
        else
        {
            // Yanlış blok - HEMEN ragdoll aktif et, sonra server'a bildir
            YanlisBlokEtkisiUygula(oyuncu);
            if (IsServer)
            {
                YanlisBlokEtkisiClientRpc(oyuncu.OwnerClientId);
            }
            else
            {
                YanlisBlokServerRpc(oyuncu.OwnerClientId);
            }
        }
    }
    
    // CLIENT TABANLI SENKRONIZASYON - Doğru blok için local fonksiyon
    private void DogruBlokEtkisiUygula()
    {
        // Doğru blok sesi çal (hemen)
        if (audioSource != null && dogruSesi != null)
        {
            audioSource.PlayOneShot(dogruSesi);
        }
        
        Debug.Log("✅ Doğru blok! Devam et. (Client tabanlı)");
    }
    
    // CLIENT TABANLI SENKRONIZASYON - Yanlış blok için local fonksiyon
    private void YanlisBlokEtkisiUygula(OyuncuKontrol oyuncu)
    {
        // HEMEN ragdoll aktif et (gecikme yok!)
        RagdollManager ragdollManager = oyuncu.GetComponent<RagdollManager>();
        if (ragdollManager != null)
        {
            Vector3 dusmekKuvveti = Vector3.down * 10f + Vector3.forward * 2f;
            Vector3 donmeTorku = new Vector3(Random.Range(-5f, 5f), 0, Random.Range(-5f, 5f));
            
            ragdollManager.DarbelereKarsiRagdollAktifEt(dusmekKuvveti, donmeTorku, ragdollSuresi);
            Debug.Log("❌ Yanlış blok! Ragdoll HEMEN aktif edildi! (Client tabanlı)");
        }
        
        // Blok kırılma efektini başlat
        StartCoroutine(YanlisBlokKirilmaCoroutine(oyuncu.OwnerClientId));
    }
    
    // Server'a doğru blok bilgisini gönder
    [ServerRpc(RequireOwnership = false)]
    private void DogruBlokServerRpc()
    {
        // Diğer clientlara bildir
        DogruBlokEtkisiClientRpc();
    }
    
    // Server'a yanlış blok bilgisini gönder
    [ServerRpc(RequireOwnership = false)]
    private void YanlisBlokServerRpc(ulong oyuncuClientId)
    {
        // Diğer clientlara bildir
        YanlisBlokEtkisiClientRpc(oyuncuClientId);
    }

    [ClientRpc]
    private void DogruBlokEtkisiClientRpc()
    {
        // Doğru blok sesi çal
        if (audioSource != null && dogruSesi != null)
        {
            audioSource.PlayOneShot(dogruSesi);
        }
        
        Debug.Log("✅ Doğru blok! Devam et.");
    }
    
    [ClientRpc]
    private void YanlisBlokEtkisiClientRpc(ulong oyuncuClientId)
    {
        // Ragdoll aktivasyonunu ayrı ClientRpc ile gönder
        if (IsServer)
        {
            OyuncuRagdollAktifEtClientRpc(oyuncuClientId);
        }
        
        StartCoroutine(YanlisBlokKirilmaCoroutine(oyuncuClientId));
    }
    
    [ClientRpc]
    private void OyuncuRagdollAktifEtClientRpc(ulong oyuncuClientId)
    {
        // Her client kendi oyuncusunu kontrol etsin
        if (NetworkManager.Singleton.LocalClientId == oyuncuClientId)
        {
            // Kendi oyuncumuzsa ragdoll aktif et
            OyuncuKontrol oyuncu = NetworkManager.Singleton.LocalClient.PlayerObject.GetComponent<OyuncuKontrol>();
            if (oyuncu != null)
            {
                RagdollManager ragdollManager = oyuncu.GetComponent<RagdollManager>();
                if (ragdollManager != null)
                {
                    Vector3 dusmekKuvveti = Vector3.down * 10f + Vector3.forward * 2f;
                    Vector3 donmeTorku = new Vector3(Random.Range(-5f, 5f), 0, Random.Range(-5f, 5f));
                    
                    ragdollManager.DarbelereKarsiRagdollAktifEt(dusmekKuvveti, donmeTorku, ragdollSuresi);
                    Debug.Log($"Client {oyuncuClientId} için ragdoll aktif edildi!");
                }
            }
        }
    }
    
    private System.Collections.IEnumerator YanlisBlokKirilmaCoroutine(ulong oyuncuClientId)
    {
        
        // Hemen kırılma sesi çal
        if (audioSource != null && kirilmaSesi != null)
        {
            audioSource.PlayOneShot(kirilmaSesi);
        }
        
        // Mobil optimizasyonlu kırılma efekti göster
        MobilOptimizeEfektGoster();
        
        // Hızlıca aşağı düşme efekti
        Vector3 baslangicPozisyon = transform.position;
        Vector3 hedefPozisyon = baslangicPozisyon + Vector3.down * dusmeMesafesi;
        
        float gecenSure = 0f;
        float dusmeSuresi = dusmeMesafesi / dusmeHizi;
        
        while (gecenSure < dusmeSuresi)
        {
            float t = gecenSure / dusmeSuresi;
            transform.position = Vector3.Lerp(baslangicPozisyon, hedefPozisyon, t);
            
            gecenSure += Time.deltaTime;
            yield return null;
        }
        
        // Collider'ı devre dışı bırak
        if (blokCollider != null)
        {
            blokCollider.enabled = false;
        }
        
        // Bloğu görünmez yap
        if (blokRenderer != null)
        {
            blokRenderer.enabled = false;
        }
        
        Debug.Log("❌ Yanlış blok! Oyuncu düştü.");
        
        // 5 saniye sonra bloğu tekrar aktif et
        yield return new WaitForSeconds(5f);
        BlokuSifirla();
    }
    
    private void BlokuSifirla()
    {
        kullanildi = false;
        
        // Bloğu orijinal pozisyonuna geri getir
        transform.position = baslangicPozisyonu;
        
        if (blokRenderer != null)
        {
            blokRenderer.enabled = true;
        }
        
        if (blokCollider != null)
        {
            blokCollider.enabled = true;
        }
    }
    
    /// <summary>
    /// Bu bloğu çift grubuna ekler
    /// ID 0,1 -> Çift 0 | ID 2,3 -> Çift 1 | ID 4,5 -> Çift 2
    /// </summary>
    private void CiftGrubunaEkle()
    {
        int ciftNumarasi = blokID / 2; // 0,1->0 | 2,3->1 | 4,5->2
        
        if (!ciftBloklar.ContainsKey(ciftNumarasi))
        {
            ciftBloklar[ciftNumarasi] = new List<DogruYanlisBlok>();
        }
        
        ciftBloklar[ciftNumarasi].Add(this);
        
        Debug.Log($"Blok ID {blokID} -> Çift {ciftNumarasi} grubuna eklendi");
    }
    
    /// <summary>
    /// Çiftteki blokların birini doğru, diğerini yanlış yapar
    /// </summary>
    private void CiftDogruYanlisDagit()
    {
        int ciftNumarasi = blokID / 2;
        
        // Bu çift için daha önce dağılım yapıldıysa çık
        if (ciftDagitildi.ContainsKey(ciftNumarasi) && ciftDagitildi[ciftNumarasi])
            return;
        
        // Çiftteki her iki blok da yüklendi mi kontrol et
        if (!ciftBloklar.ContainsKey(ciftNumarasi) || ciftBloklar[ciftNumarasi].Count < 2)
            return;
        
        List<DogruYanlisBlok> ciftBlokListesi = ciftBloklar[ciftNumarasi];
        
        // Rastgele birini doğru, diğerini yanlış yap
        int dogruIndex = Random.Range(0, 2);
        
        for (int i = 0; i < ciftBlokListesi.Count; i++)
        {
            ciftBlokListesi[i].dogruBlok = (i == dogruIndex);
            Debug.Log($"Çift {ciftNumarasi} - Blok ID {ciftBlokListesi[i].blokID}: {(ciftBlokListesi[i].dogruBlok ? "DOĞRU" : "YANLIŞ")}");
        }
        
        // Bu çift için dağılım tamamlandı olarak işaretle
        ciftDagitildi[ciftNumarasi] = true;
        
        // Tüm blokların materyallerini güncelle
        foreach (var blok in ciftBlokListesi)
        {
            blok.MateryaliGuncelle();
        }
    }
    
    /// <summary>
    /// Bloğun materyalini doğru/yanlış durumuna göre günceller
    /// </summary>
    private void MateryaliGuncelle()
    {
        if (blokRenderer != null)
        {
            blokRenderer.material = dogruBlok ? dogruMaterial : yanlisMaterial;
        }
    }
    
    /// <summary>
    /// MOBİL OPTİMİZE - Efekt gösterme sistemi
    /// Object pooling ve maksimum efekt sayısı kontrolü ile
    /// </summary>
    private void MobilOptimizeEfektGoster()
    {
        if (kirilmaEfekti == null) return;
        
        // Mobil optimizasyon kapalıysa normal şekilde çalıştır
        if (!mobilOptimizasyon)
        {
            GameObject efekt = Instantiate(kirilmaEfekti, transform.position, transform.rotation);
            Destroy(efekt, 3f);
            return;
        }
        
        // Maksimum efekt sayısını kontrol et
        if (aktifEfektSayisi >= maxEfektSayisi)
        {
            Debug.Log("Maksimum efekt sayısına ulaşıldı, efekt atlandı (mobil optimizasyon)");
            return;
        }
        
        GameObject yeniEfekt = null;
        
        // Object pooling - Havuzdan efekt al
        if (efektHavuzu.Count > 0)
        {
            yeniEfekt = efektHavuzu.Dequeue();
            yeniEfekt.transform.position = transform.position;
            yeniEfekt.transform.rotation = transform.rotation;
            yeniEfekt.SetActive(true);
        }
        else
        {
            // Havuzda yoksa yeni oluştur
            yeniEfekt = Instantiate(kirilmaEfekti, transform.position, transform.rotation);
        }
        
        aktifEfektSayisi++;
        
        // Efekti belirli süre sonra havuza geri koy
        StartCoroutine(EfektiHavuzaGeriKoy(yeniEfekt));
    }
    
    /// <summary>
    /// MOBİL OPTİMİZE - Efekti havuza geri koyma
    /// </summary>
    private System.Collections.IEnumerator EfektiHavuzaGeriKoy(GameObject efekt)
    {
        yield return new WaitForSeconds(efektYokEtmeSuresi);
        
        if (efekt != null)
        {
            efekt.SetActive(false);
            
            // Havuz doluysa destroy et, değilse havuza koy
            if (efektHavuzu.Count < 5)
            {
                efektHavuzu.Enqueue(efekt);
            }
            else
            {
                Destroy(efekt);
            }
            
            aktifEfektSayisi--;
        }
    }
    
    /// <summary>
    /// MOBİL OPTİMİZE - Düşük kaliteli cihazlar için ayarları uygula
    /// </summary>
    [ContextMenu("Düşük Kalite Ayarları Uygula")]
    public void DusukKaliteAyarlariUygula()
    {
        mobilOptimizasyon = true;
        maxEfektSayisi = 2; // Daha az efekt
        efektYokEtmeSuresi = 1.5f; // Daha erken yok et
        dusmeHizi = 15f; // Daha hızlı düşme (daha az frame)
        
        Debug.Log("Düşük kalite ayarları uygulandı (mobil optimizasyon)");
    }
    
    /// <summary>
    /// MOBİL OPTİMİZE - Yüksek kaliteli cihazlar için ayarları uygula
    /// </summary>
    [ContextMenu("Yüksek Kalite Ayarları Uygula")]
    public void YuksekKaliteAyarlariUygula()
    {
        mobilOptimizasyon = false;
        maxEfektSayisi = 5; // Daha fazla efekt
        efektYokEtmeSuresi = 3f; // Normal süre
        dusmeHizi = 10f; // Normal hız
        
        Debug.Log("Yüksek kalite ayarları uygulandı");
    }
    
    /// <summary>
    /// Editor'da blok tipini değiştirmek için
    /// </summary>
    [ContextMenu("Doğru Blok Yap")]
    public void DogruBlokYap()
    {
        dogruBlok = true;
        MateryaliGuncelle();
    }
    
    [ContextMenu("Yanlış Blok Yap")]
    public void YanlisBlokYap()
    {
        dogruBlok = false;
        MateryaliGuncelle();
    }
    
    /// <summary>
    /// Blok ID'sini otomatik ayarla (pozisyona göre)
    /// </summary>
    [ContextMenu("Blok ID'sini Otomatik Ayarla")]
    public void BlokIDOtomatikAyarla()
    {
        // X pozisyonuna göre ID hesapla (örnek)
        blokID = Mathf.RoundToInt(transform.position.x);
        Debug.Log($"Blok ID otomatik ayarlandı: {blokID}");
    }
    
    private void OnDestroy()
    {
        // Blok yok edilirken listeden çıkar
        int ciftNumarasi = blokID / 2;
        if (ciftBloklar.ContainsKey(ciftNumarasi))
        {
            ciftBloklar[ciftNumarasi].Remove(this);
            
            // Liste boşsa sözlükten çıkar
            if (ciftBloklar[ciftNumarasi].Count == 0)
            {
                ciftBloklar.Remove(ciftNumarasi);
                ciftDagitildi.Remove(ciftNumarasi);
            }
        }
    }
}