using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Draw Call sayısını acil durumlarda azaltmak için optimize edici sınıf.
/// SetPass call sayısını düşürmek için agresif optimizasyonlar uygular.
/// </summary>
public class DrawCallOptimizer : MonoBehaviour
{
    // Singleton instance
    public static DrawCallOptimizer Instance { get; private set; }

    [Header("Mesafe Bazlı Optimizasyonlar")]
    [Tooltip("Bu mesafeden sonraki nesneler devre dışı bırakılır (metre)")]
    public float cullingDistance = 50f;

    [Tooltip("Küçük nesneler için daha agresif culling mesafesi (metre)")]
    public float smallObjectCullingDistance = 30f;

    [Tooltip("Bir nesnenin 'küçük' sayılması için maksimum boyut (metre küp)")]
    public float smallObjectThreshold = 0.5f;

    [Header("Batching Ayarları")]
    [Tooltip("Static batching için uygun nesneleri otomatik işaretle")]
    public bool forceStaticBatching = true;

    [Tooltip("GPU Instancing'i zorla etkinleştir")]
    public bool forceGPUInstancing = true;

    [Header("Işık Optimizasyonları")]
    [Tooltip("Maksimum aktif ışık sayısı")]
    [Range(1, 8)]
    public int maxActiveLights = 2;

    [Tooltip("Işık mesafelerini azaltma çarpanı")]
    [Range(0.1f, 1.0f)]
    public float lightRangeMultiplier = 0.7f;

    [Header("Materyal Optimizasyonları")]
    [Tooltip("Benzer materyalleri birleştir")]
    public bool combineSimilarMaterials = true;

    [Tooltip("Shader karmaşıklığını düşür")]
    public bool simplifyShaders = true;

    // Orijinal materyal referanslarını saklamak için
    private Dictionary<Renderer, Material[]> originalMaterials = new Dictionary<Renderer, Material[]>();
    
    // Benzer materyalleri gruplamak için
    private Dictionary<int, Material> materialGroups = new Dictionary<int, Material>();

    private void Awake()
    {
        // Singleton pattern
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(gameObject);

        // Hemen optimizasyonları uygula
        ApplyEmergencyOptimizations();
    }

    /// <summary>
    /// Tüm acil optimizasyonları uygula
    /// </summary>
    public void ApplyEmergencyOptimizations()
    {
        Debug.Log("[DrawCallOptimizer] Acil draw call optimizasyonları başlatılıyor...");
        
        // Ana kamerayı bul
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            Debug.LogWarning("[DrawCallOptimizer] Ana kamera bulunamadı! Mesafe bazlı optimizasyonlar atlanıyor.");
            return;
        }

        // Tüm renderer'ları bul
        Renderer[] allRenderers = FindObjectsByType<Renderer>(FindObjectsSortMode.None);
        Debug.Log($"[DrawCallOptimizer] Toplam {allRenderers.Length} renderer bulundu.");

        // Işık optimizasyonlarını uygula
        OptimizeLights();

        // Renderer optimizasyonlarını uygula
        OptimizeRenderers(allRenderers, mainCamera);

        // Materyal optimizasyonlarını uygula (eğer etkinse)
        if (combineSimilarMaterials)
        {
            GroupSimilarMaterials(allRenderers);
        }

        // Static batching optimizasyonlarını uygula
        if (forceStaticBatching)
        {
            ForceStaticBatching(allRenderers);
        }

        Debug.Log("[DrawCallOptimizer] Acil optimizasyonlar tamamlandı!");
    }

    /// <summary>
    /// Işık sayısını ve mesafesini optimize eder
    /// </summary>
    private void OptimizeLights()
    {
        // Tüm ışıkları bul
        Light[] allLights = FindObjectsByType<Light>(FindObjectsSortMode.None);
        
        // Işıkları önem sırasına göre sırala (intensity ve range'e göre)
        var sortedLights = allLights
            .Where(l => l.enabled && l.type != LightType.Directional) // Directional ışıkları hariç tut
            .OrderByDescending(l => l.intensity * l.range)
            .ToArray();

        Debug.Log($"[DrawCallOptimizer] {sortedLights.Length} ışık optimize ediliyor...");

        // Maksimum ışık sayısını aşan ışıkları kapat
        for (int i = 0; i < sortedLights.Length; i++)
        {
            if (i >= maxActiveLights)
            {
                sortedLights[i].enabled = false;
                continue;
            }
            
            // Kalan ışıkların mesafesini azalt
            if (sortedLights[i].type == LightType.Point || sortedLights[i].type == LightType.Spot)
            {
                sortedLights[i].range *= lightRangeMultiplier;
            }
        }

        Debug.Log($"[DrawCallOptimizer] Işık optimizasyonu tamamlandı. Aktif ışık sayısı: {Mathf.Min(maxActiveLights, sortedLights.Length)}");
    }

    /// <summary>
    /// Renderer'ları mesafeye ve boyuta göre optimize eder
    /// </summary>
    private void OptimizeRenderers(Renderer[] renderers, Camera mainCamera)
    {
        int disabledCount = 0;
        int smallObjectsDisabled = 0;

        foreach (Renderer renderer in renderers)
        {
            if (renderer == null || !renderer.enabled) continue;

            // Nesnenin kameraya olan mesafesini hesapla
            float distance = Vector3.Distance(mainCamera.transform.position, renderer.bounds.center);
            
            // Nesnenin boyutunu hesapla (hacim)
            float objectVolume = renderer.bounds.size.x * renderer.bounds.size.y * renderer.bounds.size.z;
            bool isSmallObject = objectVolume < smallObjectThreshold;

            // Mesafeye göre culling
            if ((isSmallObject && distance > smallObjectCullingDistance) || 
                distance > cullingDistance)
            {
                renderer.enabled = false;
                disabledCount++;
                
                if (isSmallObject)
                {
                    smallObjectsDisabled++;
                }
                
                continue;
            }

            // GPU Instancing'i zorla etkinleştir (eğer etkinse)
            if (forceGPUInstancing)
            {
                EnableGPUInstancing(renderer);
            }
        }

        Debug.Log($"[DrawCallOptimizer] Renderer optimizasyonu tamamlandı. " +
                  $"Devre dışı bırakılan: {disabledCount} ({smallObjectsDisabled} küçük nesne)");
    }

    /// <summary>
    /// GPU Instancing'i etkinleştirir
    /// </summary>
    private void EnableGPUInstancing(Renderer renderer)
    {
        if (renderer == null) return;
        
        Material[] materials = renderer.sharedMaterials;
        if (materials == null || materials.Length == 0) return;

        for (int i = 0; i < materials.Length; i++)
        {
            if (materials[i] != null && materials[i].enableInstancing == false)
            {
                materials[i].enableInstancing = true;
            }
        }
    }

    /// <summary>
    /// Benzer materyalleri gruplar ve birleştirir
    /// </summary>
    private void GroupSimilarMaterials(Renderer[] renderers)
    {
        // Önce tüm orijinal materyalleri kaydet
        foreach (Renderer renderer in renderers)
        {
            if (renderer == null) continue;
            
            // Orijinal materyalleri kaydet
            originalMaterials[renderer] = renderer.sharedMaterials;
        }

        // Benzer materyalleri grupla
        foreach (Renderer renderer in renderers)
        {
            if (renderer == null) continue;
            
            Material[] currentMaterials = renderer.sharedMaterials;
            if (currentMaterials == null || currentMaterials.Length == 0) continue;

            Material[] newMaterials = new Material[currentMaterials.Length];
            
            for (int i = 0; i < currentMaterials.Length; i++)
            {
                Material currentMat = currentMaterials[i];
                if (currentMat == null) continue;

                // Materyal özelliklerinden bir hash oluştur
                int materialHash = GetMaterialHash(currentMat);

                // Bu hash için zaten bir materyal var mı kontrol et
                if (!materialGroups.TryGetValue(materialHash, out Material sharedMat))
                {
                    // Yok ise, bu materyali gruba ekle
                    materialGroups[materialHash] = currentMat;
                    sharedMat = currentMat;
                    
                    // Shader'ı basitleştir (eğer etkinse)
                    if (simplifyShaders)
                    {
                        SimplifyShader(sharedMat);
                    }
                }

                // Renderer'a paylaşılan materyali ata
                newMaterials[i] = sharedMat;
            }

            // Yeni materyalleri renderer'a ata
            renderer.sharedMaterials = newMaterials;
        }

        Debug.Log($"[DrawCallOptimizer] Materyal gruplama tamamlandı. Toplam {materialGroups.Count} benzersiz materyal grubu oluşturuldu.");
    }

    /// <summary>
    /// Materyal için bir hash değeri oluşturur
    /// </summary>
    private int GetMaterialHash(Material material)
    {
        if (material == null) return 0;
        
        int hash = 17;
        
        // Shader hash'i
        hash = hash * 23 + (material.shader != null ? material.shader.GetHashCode() : 0);
        
        // Ana texture hash'i
        hash = hash * 23 + (material.mainTexture != null ? material.mainTexture.GetHashCode() : 0);
        
        // Ana renk hash'i
        if (material.HasProperty("_Color"))
        {
            Color color = material.GetColor("_Color");
            hash = hash * 23 + color.GetHashCode();
        }
        
        return hash;
    }

    /// <summary>
    /// Shader'ı basitleştirir
    /// </summary>
    private void SimplifyShader(Material material)
    {
        if (material == null) return;
        
        // Bump map'i kapat
        if (material.HasProperty("_BumpScale"))
        {
            material.SetFloat("_BumpScale", 0);
        }
        
        // Specular highlight'ı azalt
        if (material.HasProperty("_SpecularHighlights"))
        {
            material.SetFloat("_SpecularHighlights", 0);
        }
        
        // Glossiness'ı azalt
        if (material.HasProperty("_Glossiness"))
        {
            material.SetFloat("_Glossiness", 0.1f);
        }
        
        // Metallic'i azalt
        if (material.HasProperty("_Metallic"))
        {
            material.SetFloat("_Metallic", 0.1f);
        }
    }

    /// <summary>
    /// Static batching'i zorla uygular
    /// </summary>
    private void ForceStaticBatching(Renderer[] renderers)
    {
        int markedStaticCount = 0;
        
        foreach (Renderer renderer in renderers)
        {
            if (renderer == null) continue;
            
            GameObject obj = renderer.gameObject;
            
            // Hareket eden nesnelere static batching uygulanmaz
            if (obj.GetComponent<Rigidbody>() != null || 
                obj.GetComponent<Animator>() != null ||
                obj.GetComponent<Cloth>() != null)
            {
                continue;
            }
            
            // Nesne zaten statik değilse işaretle
            if (!obj.isStatic)
            {
                obj.isStatic = true;   // Runtime'da tek yol budur
                markedStaticCount++;
            }
        }
        
        Debug.Log($"[DrawCallOptimizer] Static batching optimizasyonu tamamlandı. {markedStaticCount} nesne static batching için işaretlendi.");
    }

    /// <summary>
    /// Optimizasyonları geri al ve orijinal duruma döndür
    /// </summary>
    public void RevertOptimizations()
    {
        // Orijinal materyalleri geri yükle
        foreach (var pair in originalMaterials)
        {
            if (pair.Key != null)
            {
                pair.Key.sharedMaterials = pair.Value;
            }
        }
        
        Debug.Log("[DrawCallOptimizer] Optimizasyonlar geri alındı.");
    }

    private void OnDestroy()
    {
        // Singleton instance'ı temizle
        if (Instance == this)
        {
            Instance = null;
        }
    }

    /// <summary>
    /// Editor‐only GameObjectUtility ve StaticEditorFlags kaldırıldı; 
    /// runtime ortamında statik işaretleme için gameObject.isStatic yeterlidir.
}
