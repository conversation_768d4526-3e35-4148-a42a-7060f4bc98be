using UnityEngine;

public class Billboard : MonoBehaviour
{
    [Header("Billboard Settings")]
    public bool lockY = true; // Y ekseni kilitli mi?
    public bool invertForward = false; // Ters yöne bakma
    
    private Camera mainCamera;
    private Transform cameraTransform;
    
    void Start()
    {
        // Ana kamerayı bul
        FindCamera();
    }
    
    void LateUpdate()
    {
        // Kamera yoksa veya değiştiyse tekrar bul
        if (mainCamera == null || cameraTransform == null)
        {
            FindCamera();
        }
        
        if (cameraTransform != null)
        {
            // Kameraya doğru bak
            Vector3 directionToCamera = cameraTransform.position - transform.position;
            
            // Y eksenini kilitle (sadece yatay düzlemde dön)
            if (lockY)
            {
                directionToCamera.y = 0;
            }
            
            // Ters yöne bakma seçeneği
            if (invertForward)
            {
                directionToCamera = -directionToCamera;
            }
            
            // Eğer yön vektörü çok küçükse (kamera çok yakınsa) rotasyonu değiştirme
            if (directionToCamera.sqrMagnitude > 0.01f)
            {
                // Kameraya doğru bak
                transform.rotation = Quaternion.LookRotation(directionToCamera);
            }
        }
    }
    
    void FindCamera()
    {
        // Önce InstantHybridCameraController'ı bul
        var hybridCamera = FindFirstObjectByType<InstantHybridCameraController>();
        if (hybridCamera != null)
        {
            mainCamera = hybridCamera.GetComponent<Camera>();
            if (mainCamera != null)
            {
                cameraTransform = mainCamera.transform;
                return;
            }
        }
        
        // Eğer bulunamazsa ana kamerayı bul
        mainCamera = Camera.main;
        if (mainCamera != null)
        {
            cameraTransform = mainCamera.transform;
            return;
        }
        
        // Son çare: "MainCamera" tag'li objeyi bul
        GameObject cameraObject = GameObject.FindGameObjectWithTag("MainCamera");
        if (cameraObject != null)
        {
            mainCamera = cameraObject.GetComponent<Camera>();
            if (mainCamera != null)
            {
                cameraTransform = mainCamera.transform;
            }
        }
    }
}
