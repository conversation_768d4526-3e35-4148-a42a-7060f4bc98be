using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using UnityEngine.InputSystem;

/// <summary>
/// Ayarlar sahnesini yöneten script
/// Kamera hassasiyeti ve diğer ayarları içerir
/// </summary>
public class AyarlarSahnesi : MonoBehaviour
{
    [Header("Sahne Kontrolü")]
    [Tooltip("Ana menüye dönüş butonu")]
    public Button anaMenuButonu;
    
    [Tooltip("Geri dönüş butonu")]
    public Button geriButonu;
    
    [Tooltip("Dönülecek sahne adı (boş bırakılırsa önceki sahneye döner)")]
    public string dönüsSahneAdi = "";
    
    [Header("Kamera Hassasiyeti")]
    [Tooltip("Kamera hassasiyet yöneticisi referansı")]
    public KameraHassasiyetYoneticisi kameraHassasiyetYoneticisi;
    
    [<PERSON><PERSON>("Diğer Ayarlar")]
    [Tooltip("Ayarları sıfırlama butonu")]
    public Button ayarlariSifirlaButonu;

    // ESC tuşu kontrolü için
    private bool escTusuBasildiMi = false;

    void Start()
    {
        // Cursor'u göster (ayarlar sahnesi için)
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;

        // ESC tuşu kontrolünü başlat
        escTusuBasildiMi = false;

        // Buton event'lerini bağla
        ButonEventleriniAyarla();
    }



    /// <summary>
    /// Buton event'lerini ayarlar
    /// </summary>
    private void ButonEventleriniAyarla()
    {
        if (anaMenuButonu != null)
        {
            anaMenuButonu.onClick.AddListener(AnaMenuyeDon);
        }

        if (geriButonu != null)
        {
            geriButonu.onClick.AddListener(OncekiSahneyeDon);
        }
        
        if (ayarlariSifirlaButonu != null)
        {
            ayarlariSifirlaButonu.onClick.AddListener(TumAyarlariSifirla);
        }
    }

    /// <summary>
    /// Ana menüye döner
    /// </summary>
    public void AnaMenuyeDon()
    {
        // Ayarları kaydet
        AyarlariKaydet();
        
        // Ana menü sahnesine geç
        SceneManager.LoadScene("MainMenu"); // Sahne adını projenize göre ayarlayın
    }

    /// <summary>
    /// Önceki sahneye döner
    /// </summary>
    public void OncekiSahneyeDon()
    {
        // Ayarları kaydet
        AyarlariKaydet();
        
        if (!string.IsNullOrEmpty(dönüsSahneAdi))
        {
            // Belirtilen sahneye git
            SceneManager.LoadScene(dönüsSahneAdi);
        }
        else
        {
            // Önceki sahneye git (basit yöntem)
            int mevcutSahneIndex = SceneManager.GetActiveScene().buildIndex;
            int oncekiSahneIndex = mevcutSahneIndex - 1;
            
            if (oncekiSahneIndex >= 0)
            {
                SceneManager.LoadScene(oncekiSahneIndex);
            }
            else
            {
                // İlk sahneyse ana menüye git
                SceneManager.LoadScene(0);
            }
        }
    }

    /// <summary>
    /// Ayarları kaydet
    /// </summary>
    private void AyarlariKaydet()
    {
        // PlayerPrefs otomatik olarak kaydediliyor, ama emin olmak için
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Tüm ayarları varsayılan değerlere sıfırlar
    /// </summary>
    public void TumAyarlariSifirla()
    {
        if (kameraHassasiyetYoneticisi != null)
        {
            kameraHassasiyetYoneticisi.HassasiyetSifirla();
        }
    }

    /// <summary>
    /// ESC tuşu kontrolü
    /// </summary>
    void Update()
    {
        // New Input System ile ESC tuşu kontrolü
        if (Keyboard.current != null && Keyboard.current.escapeKey.wasPressedThisFrame)
        {
            if (!escTusuBasildiMi)
            {
                escTusuBasildiMi = true;
                OncekiSahneyeDon();
            }
        }

        // ESC tuşu bırakıldığında flag'i sıfırla
        if (Keyboard.current != null && Keyboard.current.escapeKey.wasReleasedThisFrame)
        {
            escTusuBasildiMi = false;
        }
    }

    /// <summary>
    /// Component destroy edildiğinde event listener'ları temizle
    /// </summary>
    private void OnDestroy()
    {
        // UI Button event'lerini temizle
        if (anaMenuButonu != null)
        {
            anaMenuButonu.onClick.RemoveListener(AnaMenuyeDon);
        }

        if (geriButonu != null)
        {
            geriButonu.onClick.RemoveListener(OncekiSahneyeDon);
        }

        if (ayarlariSifirlaButonu != null)
        {
            ayarlariSifirlaButonu.onClick.RemoveListener(TumAyarlariSifirla);
        }

        // ESC tuşu flag'ini temizle
        escTusuBasildiMi = false;
    }
}
