using UnityEngine;

public class PositionLock : MonoBehaviour
{
    [Tooltip("Kilidi uygulanacak hedef objenin Transform'u.")]
    public Transform targetTransform;

    void Update()
    {
        // Eğer hedefTransform null değilse ve obje aktifse
        if (targetTransform != null && gameObject.activeInHierarchy)
        {
            // Kendi objemizin pozisyonunu hedef objenin pozisyonuna eşitle
            transform.position = targetTransform.position;
        }
    }
}