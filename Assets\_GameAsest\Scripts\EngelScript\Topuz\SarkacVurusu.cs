using UnityEngine;

// --- MAYIN KODUNDAN İLHAM ALINMIŞ YENİ SARKACVURUSU.CS ---
public class SarkacVurusu : MonoBehaviour
{
    [Header("Darbe Ayarları")]
    [Tooltip("Topuzun karaktere ne kadar güçlü vuracağı.")]
    public float vurmaGucu = 100f;

    [Tooltip("Ragdoll'un minimum ne kadar süre aktif kalacağı.")]
    public float ragdollSuresi = 2f;

    private void OnTriggerEnter(Collider other)
    {
        // İLHAM 1: Sadece "Player" etiketli objelerle ilgileniyoruz.
        if (!other.CompareTag("Player"))
        {
            // Debug.Log(other.name + " objesi bir oyuncu değil, işlem yapılmadı."); // İstersen bu satırı açarak diğer objelerin de girdiğini görebilirsin.
            return;
        }

        // Giren objeden veya onun üst objelerinden (parent) RagdollManager scriptini bulmaya çalışır.
        RagdollManager ragdollManager = other.GetComponentInParent<RagdollManager>();

        // İLHAM 2: RagdollManager'ın bulunup bulunmadığını kontrol eden daha detaylı bir yapı.
        if (ragdollManager != null)
        {
            // Ragdoll zaten aktif mi diye kontrol et
            if (ragdollManager.IsRagdollActive)
            {
                return;
            }

            // İtme yönünü ve kuvvetini hesapla
            Vector3 itmeYonu = (other.transform.position - transform.position).normalized;
            itmeYonu.y = 0.4f; // Karaktere hafif bir yukarı doğru ivme de verir
            Vector3 kuvvet = itmeYonu * vurmaGucu;

            // Ragdoll'u tetikle!
            ragdollManager.DarbelereKarsiRagdollAktifEt(kuvvet, Vector3.zero, ragdollSuresi);
        }
        else
        {
            // Bu mesaj, karakterinin Tag'i Player olsa bile üzerinde RagdollManager olmadığını gösterir.
            Debug.LogError("'" + other.name + "' objesinin Tag'i Player olmasına rağmen üzerinde RagdollManager script'i bulunamadı!");
        }
    }
}