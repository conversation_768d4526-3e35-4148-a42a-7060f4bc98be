using UnityEngine;
using Unity.Netcode;
using System.Collections.Generic;

public class MovingPlatform : NetworkBehaviour
{
    [Header("Platform Ayarları")]
    [SerializeField] private float moveSpeed = 2f;
    [SerializeField] private float waitTime = 1f;

    [Header("Hareket Noktaları")]
    [SerializeField] private Transform[] waypoints;
    [SerializeField] private bool backAndForth = true;

    [<PERSON><PERSON>("Görselleştirme")]
    [SerializeField] private bool showGizmos = true;

    [Header("Network Ayarları")]
    [SerializeField] private bool smoothClientMovement = true;
    [SerializeField] private float clientSmoothSpeed = 10f;

    // --- Network Variables ---
    private NetworkVariable<Vector3> networkPosition = new NetworkVariable<Vector3>(
        Vector3.zero, 
        NetworkVariableReadPermission.Everyone, 
        NetworkVariableWritePermission.Server);
    
    private NetworkVariable<int> networkWaypointIndex = new NetworkVariable<int>(
        0, 
        NetworkVariableReadPermission.Everyone, 
        NetworkVariableWritePermission.Server);
    
    private NetworkVariable<bool> networkMovingForward = new NetworkVariable<bool>(
        true, 
        NetworkVariableReadPermission.Everyone, 
        NetworkVariableWritePermission.Server);

    // --- Private Değişkenler ---
    private Vector3 lastPosition;
    private int currentWaypointIndex = 0;
    private float waitTimer;
    private bool movingForward = true;
    private Vector3 targetPosition;
    private Vector3 clientTargetPosition;

    // Sadece server'da tutulacak oyuncu listesi
    private readonly HashSet<CharacterController> playersOnPlatform = new HashSet<CharacterController>();
    
    // Client-side player tracking
    private readonly HashSet<CharacterController> clientPlayersOnPlatform = new HashSet<CharacterController>();

    // --- Unity & Network Fonksiyonları ---

    public override void OnNetworkSpawn()
    {
        // Network variable değişikliklerini dinle
        networkPosition.OnValueChanged += OnNetworkPositionChanged;
        networkWaypointIndex.OnValueChanged += OnNetworkWaypointChanged;
        networkMovingForward.OnValueChanged += OnNetworkDirectionChanged;

        if (IsServer)
        {
            // Server başlangıç pozisyonunu ayarlar
            if (waypoints.Length > 0)
            {
                transform.position = waypoints[0].position;
                networkPosition.Value = transform.position;
            }
            lastPosition = transform.position;
        }
        else
        {
            // Client için başlangıç pozisyonunu ayarla
            if (waypoints.Length > 0)
            {
                currentWaypointIndex = networkWaypointIndex.Value;
                movingForward = networkMovingForward.Value;
                transform.position = networkPosition.Value;
                clientTargetPosition = transform.position;
            }
        }
    }

    public override void OnNetworkDespawn()
    {
        // Event listener'ları temizle
        if (networkPosition != null)
            networkPosition.OnValueChanged -= OnNetworkPositionChanged;
        if (networkWaypointIndex != null)
            networkWaypointIndex.OnValueChanged -= OnNetworkWaypointChanged;
        if (networkMovingForward != null)
            networkMovingForward.OnValueChanged -= OnNetworkDirectionChanged;
    }

    // Network variable değişiklik event'leri
    private void OnNetworkPositionChanged(Vector3 previousValue, Vector3 newValue)
    {
        if (!IsServer)
        {
            clientTargetPosition = newValue;
        }
    }

    private void OnNetworkWaypointChanged(int previousValue, int newValue)
    {
        if (!IsServer)
        {
            currentWaypointIndex = newValue;
        }
    }

    private void OnNetworkDirectionChanged(bool previousValue, bool newValue)
    {
        if (!IsServer)
        {
            movingForward = newValue;
        }
    }

    private void FixedUpdate()
    {
        if (IsServer)
        {
            // Server: Platformu hareket ettir ve oyuncuları taşı
            HandleServerPlatformMovement();
            MovePlayersOnServer();
        }
        else
        {
            // Client: Smooth interpolation ile platformu hareket ettir
            HandleClientPlatformMovement();
            MovePlayersOnClient();
        }
    }

    private void HandleServerPlatformMovement()
    {
        if (waypoints.Length < 2) return;

        targetPosition = waypoints[currentWaypointIndex].position;
        float distance = Vector3.Distance(transform.position, targetPosition);

        if (distance < 0.1f)
        {
            waitTimer += Time.fixedDeltaTime;
            if (waitTimer >= waitTime)
            {
                waitTimer = 0f;
                UpdateWaypointIndex();
            }
        }
        else
        {
            transform.position = Vector3.MoveTowards(transform.position, targetPosition, moveSpeed * Time.fixedDeltaTime);
        }

        // Network pozisyonunu güncelle
        networkPosition.Value = transform.position;
    }

    private void HandleClientPlatformMovement()
    {
        if (waypoints.Length < 2) return;

        // Client'ta smooth interpolation
        if (smoothClientMovement)
        {
            transform.position = Vector3.Lerp(transform.position, clientTargetPosition, clientSmoothSpeed * Time.fixedDeltaTime);
        }
        else
        {
            transform.position = clientTargetPosition;
        }
    }

    private void MovePlayersOnServer()
    {
        Vector3 movementDelta = transform.position - lastPosition;

        // Oyuncuyu platforma "yapıştırmak" için aşağı yönlü küçük bir kuvvet.
        Vector3 stickyForce = Vector3.down * 2f;

        foreach (CharacterController player in playersOnPlatform)
        {
            if (player != null)
            {
                // Platformun hareketini ve yapıştırma kuvvetini aynı anda uygula.
                player.Move(movementDelta + (stickyForce * Time.fixedDeltaTime));
            }
        }

        lastPosition = transform.position;
    }

    private void MovePlayersOnClient()
    {
        Vector3 movementDelta = transform.position - lastPosition;

        // Client'ta da oyuncuları hareket ettir (local player için)
        Vector3 stickyForce = Vector3.down * 2f;

        foreach (CharacterController player in clientPlayersOnPlatform)
        {
            if (player != null)
            {
                // Sadece local player'ı hareket ettir
                NetworkObject playerNetworkObject = player.GetComponent<NetworkObject>();
                if (playerNetworkObject != null && playerNetworkObject.IsOwner)
                {
                    player.Move(movementDelta + (stickyForce * Time.fixedDeltaTime));
                }
            }
        }

        lastPosition = transform.position;
    }

    private void UpdateWaypointIndex()
    {
        if (backAndForth)
        {
            if (movingForward)
            {
                if (++currentWaypointIndex >= waypoints.Length)
                {
                    currentWaypointIndex = waypoints.Length - 2;
                    movingForward = false;
                }
            }
            else
            {
                if (--currentWaypointIndex < 0)
                {
                    currentWaypointIndex = 1;
                    movingForward = true;
                }
            }
        }
        else // Döngü
        {
            currentWaypointIndex = (currentWaypointIndex + 1) % waypoints.Length;
        }

        // Network değişkenlerini güncelle
        networkWaypointIndex.Value = currentWaypointIndex;
        networkMovingForward.Value = movingForward;
    }

    // --- Trigger Mantığı ---

    private void OnTriggerEnter(Collider other)
    {
        CharacterController playerController = other.GetComponentInParent<CharacterController>();
        if (playerController == null) return;

        if (IsServer)
        {
            // Server: Tüm oyuncuları takip et
            if (playersOnPlatform.Add(playerController))
            {
                Debug.Log($"[MovingPlatform] Oyuncu platforma girdi: {playerController.name}");
                
                // Client'lara bildir
                NotifyPlayerEnteredClientRpc(playerController.GetComponent<NetworkObject>().NetworkObjectId);
            }
        }
        else
        {
            // Client: Sadece local player'ı takip et
            NetworkObject playerNetworkObject = playerController.GetComponent<NetworkObject>();
            if (playerNetworkObject != null && playerNetworkObject.IsOwner)
            {
                clientPlayersOnPlatform.Add(playerController);
                Debug.Log($"[MovingPlatform] Local oyuncu platforma girdi: {playerController.name}");
            }
        }
    }

    private void OnTriggerExit(Collider other)
    {
        CharacterController playerController = other.GetComponentInParent<CharacterController>();
        if (playerController == null) return;

        if (IsServer)
        {
            // Server: Oyuncuyu listeden çıkar
            if (playersOnPlatform.Remove(playerController))
            {
                Debug.Log($"[MovingPlatform] Oyuncu platformdan ayrıldı: {playerController.name}");
                
                // Client'lara bildir
                NotifyPlayerExitedClientRpc(playerController.GetComponent<NetworkObject>().NetworkObjectId);
            }
        }
        else
        {
            // Client: Local player'ı listeden çıkar
            NetworkObject playerNetworkObject = playerController.GetComponent<NetworkObject>();
            if (playerNetworkObject != null && playerNetworkObject.IsOwner)
            {
                clientPlayersOnPlatform.Remove(playerController);
                Debug.Log($"[MovingPlatform] Local oyuncu platformdan ayrıldı: {playerController.name}");
            }
        }
    }

    // --- Network RPC'ler ---

    [ClientRpc]
    private void NotifyPlayerEnteredClientRpc(ulong playerNetworkId)
    {
        if (IsServer) return; // Server zaten kendi listesini tutuyor

        // Client'ta player'ı bul ve listeye ekle
        if (NetworkManager.Singleton.SpawnManager.SpawnedObjects.TryGetValue(playerNetworkId, out NetworkObject playerNetworkObject))
        {
            CharacterController playerController = playerNetworkObject.GetComponent<CharacterController>();
            if (playerController != null && playerNetworkObject.IsOwner)
            {
                clientPlayersOnPlatform.Add(playerController);
            }
        }
    }

    [ClientRpc]
    private void NotifyPlayerExitedClientRpc(ulong playerNetworkId)
    {
        if (IsServer) return; // Server zaten kendi listesini tutuyor

        // Client'ta player'ı bul ve listeden çıkar
        if (NetworkManager.Singleton.SpawnManager.SpawnedObjects.TryGetValue(playerNetworkId, out NetworkObject playerNetworkObject))
        {
            CharacterController playerController = playerNetworkObject.GetComponent<CharacterController>();
            if (playerController != null && playerNetworkObject.IsOwner)
            {
                clientPlayersOnPlatform.Remove(playerController);
            }
        }
    }

    // --- Diğer Fonksiyonlar ---

    private void OnDrawGizmosSelected()
    {
        if (!showGizmos || waypoints == null || waypoints.Length < 2) return;

        for (int i = 0; i < waypoints.Length; i++)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(waypoints[i].position, 0.3f);

            if (i < waypoints.Length - 1)
                Gizmos.DrawLine(waypoints[i].position, waypoints[i + 1].position);
        }

        if (!backAndForth && waypoints.Length > 1)
            Gizmos.DrawLine(waypoints[waypoints.Length - 1].position, waypoints[0].position);
    }

    private void CreateDefaultWaypoints()
    {
        waypoints = new Transform[2];
        GameObject p1 = new GameObject("Waypoint 1");
        GameObject p2 = new GameObject("Waypoint 2");
        p1.transform.SetParent(transform.parent);
        p2.transform.SetParent(transform.parent);
        p1.transform.position = transform.position;
        p2.transform.position = transform.position + Vector3.right * 5f;
        waypoints[0] = p1.transform;
        waypoints[1] = p2.transform;
    }
}