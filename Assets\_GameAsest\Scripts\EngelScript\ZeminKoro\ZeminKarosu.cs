using UnityEngine;
using System.Collections;
using Unity.Netcode;
using System.Collections.Generic;

public class ZeminKarosu : NetworkBehaviour
{
    public enum ZeminTipi { Saglam, Yikilan }


    [Tooltip("Tuzak karolarının rengi gösterildikten ne kadar saniye sonra yok olacağı.")]
    public float yikilmaGecikmesi = 0.5f;

    [Header("Düşme Animasyonu")]
    [Tooltip("Karonun aşağı doğru düşme animasyonunun ne kadar süreceği.")]
    public float dusmeSuresi = 0.5f;


    private Renderer karoRenderer;
    private static MaterialPropertyBlock sharedPropertyBlock; // BATCH OPTİMİZASYONU: Shared property block
    private readonly NetworkVariable<ZeminTipi> zeminTipi = new NetworkVariable<ZeminTipi>();
    private readonly NetworkVariable<int> temaIndex = new NetworkVariable<int>();
    private readonly NetworkVariable<bool> isRevealed = new NetworkVariable<bool>(false);

    // --- YENİ ---
    // ZeminOlusturucu'dan tema ve debug bilgisini almak için referanslar
    private ZeminOlusturucu zeminOlusturucu;
    private ZeminTemasi mevcutTema;

    // --- Hangi oyuncular bu karoya bastı? ---
    private HashSet<ulong> basanOyuncular = new HashSet<ulong>();

    // --- FLAG ---
    private bool animasyonBasladi = false;
    private bool dusmeAnimasyonuBasladi = false; // Düşme animasyonu tekrarını engellemek için

    private void Awake()
    {
        karoRenderer = GetComponent<Renderer>();

        // BATCH OPTİMİZASYONU: Shared property block'u lazy initialization ile oluştur
        if (sharedPropertyBlock == null)
        {
            sharedPropertyBlock = new MaterialPropertyBlock();
        }
    }


    public override void OnNetworkSpawn()
    {
        // Değişken dinleyicilerini (listener) ayarla
        isRevealed.OnValueChanged += OnStateChanged;

        // ZeminOlusturucu'yu bul ve debug modunu dinle
        zeminOlusturucu = ZeminOlusturucu.Instance;
        if (zeminOlusturucu != null)
        {
            zeminOlusturucu.isDebugModeActive.OnValueChanged += OnStateChanged;
        }

        // --- EN KRİTİK DEĞİŞİKLİK ---
        // temaIndex'in değerinin değişmesini DİNLE. Değiştiği an TemaAyarla() metodunu çalıştır.
        temaIndex.OnValueChanged += (oncekiDeger, yeniDeger) => TemaAyarla();

        // Başlangıçta bir kez çalıştırarak mevcut durumu yakala
        TemaAyarla();
    }

    // ZeminKarosu.cs içine eklenecek YENİ METOT
    private void TemaAyarla()
    {
        // ZeminOlusturucu hala geçerli mi diye kontrol et
        if (zeminOlusturucu == null)
        {
            zeminOlusturucu = ZeminOlusturucu.Instance;
            if (zeminOlusturucu == null)
            {
                return;
            }
        }

        // Artık temaIndex'in değerinin geldiğinden eminiz.
        if (temaIndex.Value < zeminOlusturucu.kullanilabilirTemalar.Count)
        {
            mevcutTema = zeminOlusturucu.kullanilabilirTemalar[temaIndex.Value];

            // Başlangıç materyalini ve görselini ayarla
            if (mevcutTema != null && mevcutTema.anaMateryal != null)
            {
                karoRenderer.sharedMaterial = mevcutTema.anaMateryal;
            }
            UpdateVisuals();
        }
    }


    // Bu metod sadece sunucu tarafında, karo spawn edilmeden önce çağrılır.
    public void Kurulum(ZeminTipi tip, int temaIdx)
    {
        if (!IsServer) return; // Güvenlik için kontrol
        zeminTipi.Value = tip;
        temaIndex.Value = temaIdx;
    }
    

    // Hem isRevealed hem de isDebugModeActive değiştiğinde bu metot çağrılacak
    private void OnStateChanged(bool previousValue, bool newValue)
    {
        UpdateVisuals();
    }


    // --- BİRLEŞTİRİLMİŞ GÖRSEL GÜNCELLEME METODU (YENİ HALİ) ---
    private void UpdateVisuals()
    {
        if (mevcutTema == null || karoRenderer == null || zeminOlusturucu == null)
        {
            return;
        }

        Color hedefRenk; // Artık materyal değil, renk hedefliyoruz.
        bool isDebug = zeminOlusturucu.isDebugModeActive.Value;

        if (isDebug)
        {
            hedefRenk = (zeminTipi.Value == ZeminTipi.Saglam) ? mevcutTema.guvenliRenk : mevcutTema.tuzakRenk;
        }
        else if (isRevealed.Value)
        {
            hedefRenk = (zeminTipi.Value == ZeminTipi.Saglam) ? mevcutTema.guvenliRenk : mevcutTema.tuzakRenk;
        }
        else
        {
            hedefRenk = mevcutTema.gizliRenk;
        }

        // --- BATCH OPTİMİZASYONU: SHARED MATERIALPROPERTYBLOCK KULLANIMI ---
        // Shared property block kullanarak batch'leri azaltıyoruz
        sharedPropertyBlock.SetColor("_BaseColor", hedefRenk);
        karoRenderer.SetPropertyBlock(sharedPropertyBlock);
    }

    private void OnTriggerEnter(Collider other)
    {
        // Bu fonksiyon artık ÇOK DAHA BASİT.
        // Sadece oyuncunun kendi ekranında (IsOwner) çalışacak ve sunucuya komut gönderecek.
        if (other.CompareTag("Player"))
        {
            var netObj = other.GetComponent<NetworkObject>();
            if (netObj != null && netObj.IsOwner)
            {
                // Oyuncu karoya dokunduğu an, düşünmeden sunucuya haber veriyor.
                KaroAcildiServerRpc();
            }
        }
    }

    [ServerRpc(RequireOwnership = false)]
    public void KaroAcildiServerRpc(ServerRpcParams rpcParams = default)
    {
        // --- TÜM SUNUCU MANTIĞI ARTIK BURADA! ---
        // RPC'yi gönderen istemcinin ID'sini alıyoruz.
        ulong basanOyuncuId = rpcParams.Receive.SenderClientId;

        // Eğer bu oyuncu zaten karoya basmış olarak listeleniyorsa, tekrar işlem yapma. (Spam engelleme)
        if(basanOyuncular.Contains(basanOyuncuId)) return;
        
        // İşlem yapacağımız için oyuncuyu listeye ekle.
        basanOyuncular.Add(basanOyuncuId);
        isRevealed.Value = true; // Karoyu açılmış olarak işaretle.

        // Sunucu, komutu gönderen oyuncunun objesini bulur.
        var oyuncuNetworkObject = NetworkManager.Singleton.ConnectedClients[basanOyuncuId].PlayerObject;
        if (oyuncuNetworkObject == null) return;

        var sunucudakiOyuncu = oyuncuNetworkObject.GetComponent<OyuncuKontrol>();
        if (sunucudakiOyuncu == null) return;

        // --- Karonun tipine göre aksiyon al ---
        if (zeminTipi.Value == ZeminTipi.Saglam)
        {
            GameManager.Instance.CoinEkleServerRpc(10, new ServerRpcParams { Receive = new ServerRpcReceiveParams { SenderClientId = basanOyuncuId } });
        }
        else if (zeminTipi.Value == ZeminTipi.Yikilan)
        {
            GameManager.Instance.CoinEkleServerRpc(-5, new ServerRpcParams { Receive = new ServerRpcReceiveParams { SenderClientId = basanOyuncuId } });
            
            // Ragdoll komutunu SADECE ilgili oyuncuya gönder.
            sunucudakiOyuncu.RagdolluAktifEtClientRpc(2f, new ClientRpcParams { Send = new ClientRpcSendParams { TargetClientIds = new ulong[] { basanOyuncuId } } });
            
            // Yıkılma rutinini SADECE bir kez başlat.
            if (!dusmeAnimasyonuBasladi)
            {
                dusmeAnimasyonuBasladi = true;

                // ÇÖZÜM 2: Profesyonel yol - Server'da direkt başlat
                if (IsServer)
                {
                    StartCoroutine(YikilmaRutiniSunucu());
                }
                else
                {
                    // Client'taysa server'a haber ver
                    YikilmaIstegiServerRpc();
                }
            }
        }
        
        // Açılma animasyonunu tüm client'larda tetikle (bu sizin mevcut kodunuzda vardı, koruyoruz).
        KaroAcildiClientRpc();
    }

    /// <summary>
    /// ÇÖZÜM 2: Client'tan server'a "beni yok et" isteği
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    private void YikilmaIstegiServerRpc()
    {
        if (!IsServer) return; // Ekstra güvenlik

        // Yıkılma rutinini başlat (eğer daha önce başlamamışsa)
        if (!dusmeAnimasyonuBasladi)
        {
            dusmeAnimasyonuBasladi = true;
            StartCoroutine(YikilmaRutiniSunucu());
        }
    }

    // Sunucu tarafında çalışacak olan, GECİKMESİ KALDIRILMIŞ yıkılma rutini
    private IEnumerator YikilmaRutiniSunucu()
    {
        // ⚠️ KRİTİK GÜVENLİK KONTROLÜ ⚠️
        if (!IsServer)
        {
            yield break;
        }

        double serverTime = Unity.Netcode.NetworkManager.Singleton.ServerTime.Time;
        DusmeyeBaslaClientRpc(serverTime);
        yield return new WaitForSeconds(dusmeSuresi);

        var networkObject = GetComponent<NetworkObject>();
        if (networkObject != null && networkObject.IsSpawned)
        {
            networkObject.Despawn(true);
        }
    }

    // --- YENİ METOTLAR ---

    // Sunucudan tüm istemcilere gönderilecek olan komut.
    [ClientRpc]
    public void DusmeyeBaslaClientRpc(double baslangicZamani)
    {
        if (!animasyonBasladi) // Sadece bir kez başlat
        {
            StartCoroutine(DusmeAnimasyonu((float)baslangicZamani));
        }
    }

    // Her istemcide lokal olarak çalışacak olan asıl animasyon metodu.
    private IEnumerator DusmeAnimasyonu(float baslangicZamani)
    {
        if (animasyonBasladi) yield break;
        animasyonBasladi = true;
        var col = GetComponent<Collider>();
        if (col != null) col.enabled = false;

        // Düşme animasyonu başladığında rengi kırmızı yap
        if (karoRenderer != null && sharedPropertyBlock != null)
        {
            karoRenderer.GetPropertyBlock(sharedPropertyBlock);
            sharedPropertyBlock.SetColor("_BaseColor", Color.red);
            karoRenderer.SetPropertyBlock(sharedPropertyBlock);
        }

        Vector3 baslangicPozisyonu = transform.position;
        Vector3 hedefPozisyon = baslangicPozisyonu - new Vector3(0, 15, 0); // Daha belirgin düşüş

        // Anlık düşme - smooth interpolation yok
        float dusmeHizi = 10f; // Saniye başına düşme hızı (daha yavaş, görünür olsun)

        while (transform.position.y > hedefPozisyon.y)
        {
            // Sabit hızla aşağı düş
            Vector3 yeniPozisyon = transform.position;
            yeniPozisyon.y -= dusmeHizi * Time.deltaTime;

            // Hedef pozisyonun altına geçmeyi engelle
            if (yeniPozisyon.y < hedefPozisyon.y)
                yeniPozisyon.y = hedefPozisyon.y;

            transform.position = yeniPozisyon;
            yield return null;
        }
    }

    [ClientRpc]
    public void KaroAcildiClientRpc()
    {
        // isRevealed.Value = true; // KALDIRILDI, sadece local animasyon başlat
        StartCoroutine(LocalRevealAnim());
    }

    // --- LOCAL AÇILMA ANİMASYONU (örnek, renderer rengi değiştir) ---
    private IEnumerator LocalRevealAnim()
    {
        if (karoRenderer != null)
        {
            Color orijinalRenk = karoRenderer.material.color;
            Color hedefRenk = Color.yellow; // Örnek: açılırken sarı
            float t = 0;
            while (t < 1f)
            {
                karoRenderer.material.color = Color.Lerp(orijinalRenk, hedefRenk, t);
                t += Time.deltaTime * 8f;
                yield return null;
            }
            karoRenderer.material.color = hedefRenk;
        }
    }

    public override void OnNetworkDespawn()
    {
        // Abonelikleri güvenli bir şekilde sonlandır
        if (isRevealed != null) isRevealed.OnValueChanged -= OnStateChanged;
        if (zeminOlusturucu != null && zeminOlusturucu.isDebugModeActive != null)
        {
            zeminOlusturucu.isDebugModeActive.OnValueChanged -= OnStateChanged;
        }
    }

    /// <summary>
    /// ÇÖZÜM 2: Genel amaçlı yok olma isteği - Client'tan çağrılabilir
    /// </summary>
    public void YokOlmaIstegiGonder()
    {
        // Eğer zaten server isek direkt yok et, değilsek server'a istek yolla
        if (IsServer)
        {
            var networkObject = GetComponent<NetworkObject>();
            if (networkObject != null && networkObject.IsSpawned)
            {
                networkObject.Despawn(true);
            }
        }
        else
        {
            YokEtBeniServerRpc();
        }
    }

    /// <summary>
    /// ÇÖZÜM 2: Client'tan gelen genel yok etme isteği
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    private void YokEtBeniServerRpc()
    {
        if (!IsServer) return;

        var networkObject = GetComponent<NetworkObject>();
        if (networkObject != null && networkObject.IsSpawned)
        {
            networkObject.Despawn(true);
        }
    }
}