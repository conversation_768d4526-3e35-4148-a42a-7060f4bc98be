// <PERSON><PERSON><PERSON><PERSON> kütüphaneler
using Unity.Netcode;
using UnityEngine;
using UnityEngine.InputSystem;
using TMPro;
using System.Collections;
using Unity.Netcode.Components;
using UnityEngine.SceneManagement;

// MonoBehaviour yerine NetworkBehaviour'dan miras alıyoruz.
public class OyuncuKontrol : NetworkBehaviour, IEtkilesimeGirebilir
{
    // --- Network Değişkenleri ---
    private NetworkVariable<bool> isGroundedNet = new NetworkVariable<bool>(true, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);
    private NetworkVariable<bool> isMovingNet = new NetworkVariable<bool>(false, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);
    public NetworkVariable<float> playerSpeed = new NetworkVariable<float>(5.0f, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);

    // OyuncuKontrol.cs içinde, sınıfın en üstüne ekleyin
    // Ulaşılan son checkpoint'in ID'sini tutar. -1, hen<PERSON>z hiçbir checkpoint'e ulaşılmadığı anlamına gelir.
    // Sadece sunucu tarafından yazılabilir, herkes tarafından okunabilir.
    public NetworkVariable<int> lastCheckpointID = new NetworkVariable<int>(-1, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);

    public NetworkVariable<int> puan = new NetworkVariable<int>(0, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);

    // Oyuncu ismini ağ üzerinden senkronize etmek için NetworkVariable
    public NetworkVariable<Unity.Collections.FixedString64Bytes> playerName = new NetworkVariable<Unity.Collections.FixedString64Bytes>(
        "Player", NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);

    [Header("Dependencies")]
    public Transform orientation;
    public Transform playerBody;
    public PlayerAnimator playerAnimator;
    [Tooltip("Kameranın takip edeceği bağımsız hedef. Hiyerarşide tek başına durmalı.")]
    public Transform cameraFollowTarget;

    [Header("Mobile Joystick")]
    private FloatingJoystick mobileJoystick;

    [Header("UI Components")]
    public TextMeshProUGUI playerNameText;

    // Ragdoll sırasında UI gizleme için
    private bool isRagdollActive = false;


    [Header("Movement Settings")]
    public float rotationSpeed = 15f;

    [Header("Movement Feel Settings")]
    public float acceleration = 10f;
    public float deceleration = 20f;
    private Vector3 _currentVelocity = Vector3.zero;

    [Header("Jump Settings")]
    public float jumpHeight = 1.5f;
    public float jumpCooldown = 1f;
    private bool canJump = true;

    [Header("Physics Settings")]
    public float gravityValue = -9.81f;

    // --- Private Değişkenler ---
    public static OyuncuKontrol LocalInstance { get; set; }
    private RagdollManager ragdollManager;
    private CharacterController controller;
    private Vector3 playerVelocity;
    private float _orijinalHiz;
    public NetworkVariable<Vector3> hariciHareketVektoruNet = new NetworkVariable<Vector3>(Vector3.zero, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Server);

    private PlayerControls playerControls;
    private Vector3 _moveDirection; // Hareket yönünü saklamak için

    // OPTİMİZASYON: Sadece durum değiştiğinde RPC göndermek için
    private bool _lastGroundedState = true;
    private bool _lastMovingState = false;

    // OPTİMİZASYON: GetComponentsInChildren çağrılarını azaltmak için
    private Renderer[] _allRenderers;
    private Collider[] _allColliders;

    // NetworkVariable olarak tanımla ki client-server arasında senkronize olsun
    public NetworkVariable<bool> elendi = new NetworkVariable<bool>(
        false,
        NetworkVariableReadPermission.Everyone,
        NetworkVariableWritePermission.Server);
    
    // Awake, obje ilk oluşturulduğunda çalışır.
    private void Awake()
    {
        controller = GetComponent<CharacterController>();
        ragdollManager = GetComponent<RagdollManager>();

        // Bir kere al ve referanslarını sakla
        if (playerBody != null)
        {
            _allRenderers = playerBody.GetComponentsInChildren<Renderer>(true); // true: inaktifleri de al
            _allColliders = playerBody.GetComponentsInChildren<Collider>(true); // true: inaktifleri de al
        }
    }

    // Start, obje aktif hale geldiğinde çalışır.
    private void Start()
    {
        // Güvenlik için component'lerin açık olduğundan emin ol
        if (controller != null && !controller.enabled)
        {
            controller.enabled = true;
#if UNITY_EDITOR
            Debug.Log($"🔧 Start'ta CharacterController açıldı: {gameObject.name}");
#endif
        }

        // Input sisteminin açık olduğundan emin ol (sadece owner için)
        if (IsOwner && playerControls != null && !playerControls.Oyuncu.enabled)
        {
            playerControls.Oyuncu.Enable();
#if UNITY_EDITOR
            Debug.Log($"🔧 Start'ta Input sistemi açıldı: {gameObject.name}");
#endif
        }

        // Renderer'ların açık olduğundan emin ol
        if (playerBody != null)
        {
            foreach (var renderer in playerBody.GetComponentsInChildren<Renderer>())
            {
                if (!renderer.enabled)
                {
                    renderer.enabled = true;
#if UNITY_EDITOR
                    Debug.Log($"🔧 Start'ta Renderer açıldı: {renderer.name}");
#endif
                }
            }
        }
    }

    // OnNetworkSpawn, obje AĞDA ilk yaratıldığında çalışır.
public override void OnNetworkSpawn()
    {
        // Oyuncu ismi değiştiğinde UI'ı güncelleyecek bir callback ekle.
        playerName.OnValueChanged += OnPlayerNameChanged;

        // Sahne değişikliği dinleyicisini ekle
        SceneManager.sceneLoaded += OnSceneLoaded;

        // Sadece minigame sahneslerinde spawn olurken resetleme yap
        string currentSceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        string[] miniGameSahneleri = { "RenkOyunu", "fakeblock_Scene" };

        if (System.Array.Exists(miniGameSahneleri, sahne => sahne == currentSceneName))
        {
            // Minigame sahnesinde spawn olurken oyuncuyu resetle
#if UNITY_EDITOR
            Debug.Log($"🎮 Minigame sahnesinde oyuncu {playerName.Value} spawn oluyor, durumu resetleniyor");
#endif
            // Oyuncuyu resetle
            ResetOyuncu();
        }
        else
        {
#if UNITY_EDITOR
            Debug.Log($"📋 {currentSceneName} sahnesinde spawn olan oyuncu resetlenmeyecek: {playerName.Value}");
#endif
        }

        if (IsOwner)
        {
            // YENİ: Oyuncu sahip olduğunda, sunucuya kendi ismini bildirir.
            SetPlayerNameServerRpc(PlayerData.PlayerName);

            LocalInstance = this;
            // --- TEMEL INPUT SİSTEMİNİ AYARLA ---
            playerControls = new PlayerControls();
            playerControls.Oyuncu.Enable();
            playerControls.Oyuncu.Jump.performed += _ => Jump();

            // --- KAMERAYI AYARLA ---
            StartCoroutine(SetupCameraWithDelay());

            // --- YENİ EKLENEN JOYSTICK BULMA KODU ---
            StartCoroutine(SetupJoystickWithDelay());

            puan.OnValueChanged += (oldVal, newVal) => {
                if (GameManager.Instance != null && GameManager.Instance.uiManager != null)
                    GameManager.Instance.uiManager.CoinGuncelle(newVal);
                else if (ArayuzYoneticisi.Instance != null)
                    ArayuzYoneticisi.Instance.PuanGuncelle(newVal);
            };
        }

        // UI'ı başlangıçta bir kez güncelle
        UpdatePlayerNameUI(playerName.Value);

        // Ragdoll event'lerini dinle
        if (ragdollManager != null)
        {
            ragdollManager.OnRagdollAktifOldu += OnRagdollActivated;
            ragdollManager.OnRagdollKapandi += OnRagdollDeactivated;
        }

        if (IsServer)
        {
            _orijinalHiz = playerSpeed.Value;
        }
    }

    private void OnPlayerNameChanged(Unity.Collections.FixedString64Bytes previousValue, Unity.Collections.FixedString64Bytes newValue)
    {
        UpdatePlayerNameUI(newValue);
    }

    private void UpdatePlayerNameUI(Unity.Collections.FixedString64Bytes newName)
    {
        if (playerNameText != null)
        {
            playerNameText.text = newName.ToString();
        }
    }

    /// <summary>
    /// Ragdoll aktif olduğunda çağrılır - UI'ı gizler
    /// </summary>
    private void OnRagdollActivated()
    {
        if (playerNameText != null && !isRagdollActive)
        {
            // UI'ı gizle
            playerNameText.gameObject.SetActive(false);
            isRagdollActive = true;
#if UNITY_EDITOR
            Debug.Log($"🎭 Ragdoll aktif: UI gizlendi - {playerName.Value}");
#endif
        }
    }

    /// <summary>
    /// Ragdoll kapandığında çağrılır - UI'ı tekrar gösterir
    /// </summary>
    private void OnRagdollDeactivated()
    {
        if (playerNameText != null && isRagdollActive)
        {
            // UI'ı tekrar göster
            playerNameText.gameObject.SetActive(true);
            isRagdollActive = false;
#if UNITY_EDITOR
            Debug.Log($"🎭 Ragdoll kapandı: UI tekrar gösterildi - {playerName.Value}");
#endif
        }
    }

    // YENİ EKLENECEK RPC
    [ServerRpc]
    private void SetPlayerNameServerRpc(string name)
    {
        playerName.Value = name;
        // YENİ: Lobi listesindeki ismi de güncelle
        if (Lobby3DManager.Instance != null)
        {
            Lobby3DManager.Instance.UpdatePlayerNameServerRpc(OwnerClientId, name);
        }
    }


    // Obje ağdan kaldırıldığında çalışır.
    public override void OnNetworkDespawn()
    {
        // Callback aboneliğini kaldır
        playerName.OnValueChanged -= OnPlayerNameChanged;

        // Sahne değişikliği dinleyicisini kaldır
        SceneManager.sceneLoaded -= OnSceneLoaded;

        // Ragdoll event'lerini temizle
        if (ragdollManager != null)
        {
            ragdollManager.OnRagdollAktifOldu -= OnRagdollActivated;
            ragdollManager.OnRagdollKapandi -= OnRagdollDeactivated;
        }

        if (IsOwner && playerControls != null)
        {
            playerControls.Oyuncu.Jump.performed -= _ => Jump();
            playerControls.Oyuncu.Disable();
        }
    }

    /// <summary>
    /// Sahne değiştiğinde çağrılır
    /// </summary>
    private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
    {
#if UNITY_EDITOR
        Debug.Log($"🌍 Sahne değişti: {scene.name}");
#endif
        // SAHNE DEĞİŞTİĞİNDE JOYSTICK'İ TEKRAR BUL
        if (IsOwner)
        {
            mobileJoystick = FindFirstObjectByType<FloatingJoystick>();
#if UNITY_EDITOR
            Debug.Log($"Joystick sahne değişiminde tekrar bulundu: {(mobileJoystick != null ? mobileJoystick.name : "null")}");
#endif
        }

        // Sadece minigame sahneslerinde oyuncu resetlenmeli
        string[] miniGameSahneleri = { "RenkOyunu", "fakeblock_Scene" };

        if (System.Array.Exists(miniGameSahneleri, sahne => sahne == scene.name))
        {
#if UNITY_EDITOR
            Debug.Log($"🎮 Minigame sahnesinde oyuncu resetleniyor: {playerName.Value}");
#endif
            // Kısa bir delay ile resetle (sahne yüklenmesi tamamlansın diye)
            StartCoroutine(DelayedSceneReset());
        }
        else
        {
#if UNITY_EDITOR
            Debug.Log($"📋 {scene.name} sahnesinde oyuncu resetlenmeyecek: {playerName.Value}");
#endif
        }
    }

    /// <summary>
    /// Sahne değişikliği sonrası gecikmeli resetleme
    /// </summary>
    private System.Collections.IEnumerator DelayedSceneReset()
    {
        yield return new WaitForSeconds(0.2f);
        ResetOyuncu();
        
        // Eğer RenkOyunu sahnesindeyse, kamera setup'ını da yap
        if (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "RenkOyunu" && IsOwner)
        {
            StartCoroutine(SetupCameraWithDelay());
        }
    }

    /// <summary>
    /// Oyuncuyu resetleyen ortak metod
    /// </summary>
    private void ResetOyuncu()
    {
        // Elendi durumunu resetle (sadece server'da)
        if (IsServer)
        {
            elendi.Value = false;
        }

        // CharacterController'ı aç
        var controller = GetComponent<CharacterController>();
        if (controller != null)
        {
            controller.enabled = true;
#if UNITY_EDITOR
            Debug.Log($"✅ CharacterController açıldı: {controller.enabled}");
#endif
        }

        // Renderer'ları ve Collider'ları aç
        if (playerBody != null)
        {
            foreach (var renderer in _allRenderers) // Yeni array oluşturmuyoruz
            {
                if (renderer != null) renderer.enabled = true;
            }
            foreach (var collider in _allColliders) // Yeni array oluşturmuyoruz
            {
                if (collider != null) collider.enabled = true;
            }
#if UNITY_EDITOR
            Debug.Log($"✅ Renderer'lar ve Collider'lar açıldı");
#endif
        }

        // Input sistemini aç (sadece owner için)
        if (IsOwner && playerControls != null)
        {
            playerControls.Oyuncu.Enable();
#if UNITY_EDITOR
            Debug.Log($"🎮 Input sistemi açıldı: {playerName.Value}");
#endif
        }

        // Spectate referansları kaldırıldı - sonradan eklenecek

        // İsmi tekrar göster
        if (playerNameText != null)
        {
            playerNameText.gameObject.SetActive(true);
#if UNITY_EDITOR
            Debug.Log($"📝 Oyuncu ismi tekrar gösterildi: {playerName.Value}");
#endif
        }

        // Hızları sıfırla
        _currentVelocity = Vector3.zero;
        playerVelocity = Vector3.zero;
        hariciHareketVektoruNet.Value = Vector3.zero; // NetworkVariable'ı sıfırla

#if UNITY_EDITOR
        Debug.Log($"🔄 Oyuncu {playerName.Value} tamamen resetlendi");
#endif
    }

    /// <summary>
    /// Client-side resetleme - RenkOyunuManager tarafından çağrılır
    /// Network sync sorunlarını önlemek için client'larda da resetleme yapar
    /// </summary>
    public void ClientSideReset()
    {
#if UNITY_EDITOR
        Debug.Log($"🔄 Client-side reset başlıyor: {playerName.Value} (IsOwner: {IsOwner})");
#endif
        // CharacterController'ı aç
        var controller = GetComponent<CharacterController>();
        if (controller != null && !controller.enabled)
        {
            controller.enabled = true;
#if UNITY_EDITOR
            Debug.Log($"✅ Client: CharacterController açıldı: {playerName.Value}");
#endif
        }

        // Renderer'ları ve Collider'ları aç
        if (playerBody != null)
        {
            foreach (var renderer in _allRenderers) // Yeni array oluşturmuyoruz
            {
                if (renderer != null && !renderer.enabled)
                {
                    renderer.enabled = true;
                }
            }
            foreach (var collider in _allColliders) // Yeni array oluşturmuyoruz
            {
                if (collider != null && !collider.enabled)
                {
                    collider.enabled = true;
                }
            }
#if UNITY_EDITOR
            Debug.Log($"✅ Client: Renderer'lar ve Collider'lar açıldı: {playerName.Value}");
#endif
        }

        // Input sistemini aç (sadece owner için)
        if (IsOwner && playerControls != null)
        {
            if (!playerControls.Oyuncu.enabled)
            {
                playerControls.Oyuncu.Enable();
#if UNITY_EDITOR
                Debug.Log($"🎮 Client: Input sistemi açıldı: {playerName.Value}");
#endif
            }
        }

        // Spectate referansları kaldırıldı - sonradan eklenecek

        // İsmi tekrar göster
        if (playerNameText != null && !playerNameText.gameObject.activeInHierarchy)
        {
            playerNameText.gameObject.SetActive(true);
#if UNITY_EDITOR
            Debug.Log($"📝 Client: Oyuncu ismi tekrar gösterildi: {playerName.Value}");
#endif
        }

        // Hızları sıfırla
        _currentVelocity = Vector3.zero;
        playerVelocity = Vector3.zero;
        hariciHareketVektoruNet.Value = Vector3.zero; // NetworkVariable'ı sıfırla

#if UNITY_EDITOR
        Debug.Log($"🔄 Client-side reset tamamlandı: {playerName.Value}");
#endif
    }

    // Her frame'de çalışır.
    void Update()
    {
        // Input okuma buradan kaldırıldı, sadece animasyon kalıyor.
        HandleAnimation();
    }

    // Kamera takip kodu burada, titremeyi önler
    private void LateUpdate()
    {
        if (IsOwner && cameraFollowTarget != null && orientation != null)
        {
            cameraFollowTarget.position = orientation.position;
        }


    }

    // Fiziksel hareketler için FixedUpdate kullanılır.
    private void FixedUpdate()
    {
        if (IsOwner)
        {
            HandleClientPhysics();
        }
    }

    // --- CLIENT TARAFI MANTIĞI ---
    // HandleClientInputAndRotation fonksiyonu kaldırıldı, mantığı HandleClientPhysics'e taşındı.

    private void HandleClientPhysics()
    {
        // Ragdoll veya controller kapalıysa hiçbir şey yapma
        if ((ragdollManager != null && ragdollManager.IsRagdollActive) || !controller.enabled)
        {
            _moveDirection = Vector3.zero; // Hareketin durduğundan emin ol
            return;
        }

        // --- INPUT OKUMA VE ROTASYON ---
        Vector2 keyboardMoveInput = playerControls.Oyuncu.Move.ReadValue<Vector2>();
        Vector2 joystickMoveInput = (mobileJoystick != null && mobileJoystick.gameObject.activeInHierarchy) ? mobileJoystick.Direction : Vector2.zero;
        
        // OPTİMİZASYON: Joystick inputunu öncelikli yapıyoruz, daha net hale getirildi.
        Vector2 currentMoveInput = keyboardMoveInput; 
        if (mobileJoystick != null && mobileJoystick.gameObject.activeInHierarchy && joystickMoveInput.sqrMagnitude > 0.01f)
        {
            currentMoveInput = joystickMoveInput; 
        }

        Vector3 targetMoveDirection = Vector3.zero;
        if (currentMoveInput.sqrMagnitude > 0.01f)
        {
            targetMoveDirection = (orientation.forward * currentMoveInput.y + orientation.right * currentMoveInput.x).normalized;
            Quaternion targetRotation = Quaternion.LookRotation(targetMoveDirection, Vector3.up);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.fixedDeltaTime);
        }

        // --- YENİ İVMELENME MANTIĞI ---
        Vector3 targetVelocity = targetMoveDirection * playerSpeed.Value;
        float smoothTime = (targetMoveDirection.sqrMagnitude > 0.01f) ? (1f / acceleration) : (1f / deceleration);
        _currentVelocity = Vector3.Lerp(_currentVelocity, targetVelocity, 1 - Mathf.Exp(-acceleration * Time.fixedDeltaTime));

        // --- FİZİK HESAPLAMALARI ---
        bool isGrounded = controller.isGrounded;
        if (isGrounded && playerVelocity.y < 0)
        {
            playerVelocity.y = -2f;
        }

        playerVelocity.y += gravityValue * Time.fixedDeltaTime;

        // Final hıza yumuşatılmış hareketi ve yerçekimini ekle
        Vector3 finalVelocity = _currentVelocity;
        finalVelocity.y = playerVelocity.y;

        Vector3 nihaiHareket = finalVelocity + hariciHareketVektoruNet.Value; // NetworkVariable'ın Value'sini kullan
        controller.Move(nihaiHareket * Time.fixedDeltaTime);

        // Animasyon durumu sunucuya bildirilir
        bool isMoving = _currentVelocity.sqrMagnitude > 0.1f; // Eşik değerini biraz artırdık
        if (isMoving != _lastMovingState || isGrounded != _lastGroundedState)
        {
            UpdateAnimationStateServerRpc(isMoving, isGrounded);
            _lastMovingState = isMoving;
            _lastGroundedState = isGrounded;
        }
    }

    public void Jump()
    {
        if (!canJump || !controller.isGrounded) return;
        canJump = false;
        playerVelocity.y = Mathf.Sqrt(jumpHeight * -2.0f * gravityValue);
        PlayJumpAnimationServerRpc();
        Invoke(nameof(ResetJump), jumpCooldown);
    }

    private void ResetJump()
    {
        canJump = true;
    }

    // --- ORTAK ---
    private void HandleAnimation()
    {
        if (playerAnimator == null) return;

        // Yerel oyuncu için animasyonu anında güncelle
        if (IsOwner)
        {
            playerAnimator.UpdateAnimationState(_lastMovingState, false, _lastGroundedState);
        }
        // Diğer oyuncular için ağdan gelen değerlerle güncelle
        else
        {
            playerAnimator.UpdateAnimationState(isMovingNet.Value, false, isGroundedNet.Value);
        }
    }

    // =====================================================================
    // --- RPC'ler (Remote Procedure Calls) ---
    // =====================================================================

    [ServerRpc]
    private void UpdateAnimationStateServerRpc(bool isMoving, bool isGrounded)
    {
        isMovingNet.Value = isMoving;
        isGroundedNet.Value = isGrounded;
    }

    [ServerRpc]
    private void PlayJumpAnimationServerRpc()
    {
        PlayJumpAnimationClientRpc();
    }

    [ClientRpc]
    private void PlayJumpAnimationClientRpc()
    {
        if (playerAnimator != null)
        {
            playerAnimator.TriggerJump();
        }
    }


    // Bu değişkeni sınıfın en üstüne, diğer değişkenlerin yanına ekle.
    private NetworkTransform networkTransform;

    // Bu satırı Awake() metodunun içine ekle.
    // networkTransform = GetComponent<NetworkTransform>();

    // OPERASYON ADIM 1: ZIRHLI TELEPORT SİSTEMİ
    // Bu metodu server çağıracak ve o da aşağıdaki RPC'yi tetikleyecek.
    public void Teleport(Vector3 position, Quaternion rotation)
    {
        // Bu komutun sadece server tarafından tetiklendiğinden emin olalım.
        if (!IsServer) return;

        // Sadece bu objenin sahibi olan client'a özel bir RPC (Uzaktan Komut) gönderiyoruz.
        ClientRpcParams clientRpcParams = new ClientRpcParams
        {
            Send = new ClientRpcSendParams
            {
                TargetClientIds = new ulong[] { OwnerClientId }
            }
        };

        // Asıl ışınlanma işini yapacak olan ClientRpc'yi çağır.
        TeleportClientRpc(position, rotation, clientRpcParams);
    }

    // Bu metot asıl ışınlanma işini client'ın bilgisayarında yapar.
    [ClientRpc]
    private void TeleportClientRpc(Vector3 position, Quaternion rotation, ClientRpcParams rpcParams = default)
    {
#if UNITY_EDITOR && DEBUG_OYUNCUKONTROL
        Debug.Log($"[CLIENT {OwnerClientId}] 💡 TELEPORT EMRİ ALINDI! Hedef: {position}");
#endif
        // CharacterController veya Rigidbody gibi fizik component'leri pozisyon atamasıyla çakışabilir.
        // Bu yüzden ışınlamadan önce onları geçici olarak devre dışı bırakmak en GÜVENLİ yoldur.
        var characterController = GetComponent<CharacterController>();
        if (characterController != null)
        {
            characterController.enabled = false;
        }
        // Pozisyonu ve açıyı SERVER'IN SÖYLEDİĞİ YERE AYARLA!
        transform.position = position;
        transform.rotation = rotation;
        // Devre dışı bıraktığımız component'leri geri açıyoruz.
        if (characterController != null)
        {
            characterController.enabled = true;
        }
        // Düşme hızını sıfırla.
        playerVelocity = Vector3.zero;
        // Debug.Log($"[CLIENT {OwnerClientId}] ✅ IŞINLANMA BAŞARILI! Yeni pozisyon: {transform.position}");
    }

    // =====================================================================
    // --- Arayüz Fonksiyonları (Güvenlik için Sunucu Tarafından Çağrılmalı) ---
    // =====================================================================
    public void ZıplatmaYap(float ziplamaHizi)
    {
        if (!IsServer) return;
    }

    public void HiziDegistir(float hizCarpanı)
    {
        if (!IsServer) return;
        playerSpeed.Value = _orijinalHiz * hizCarpanı;
    }

    public void HiziSifirla()
    {
        if (!IsServer) return;
        playerSpeed.Value = _orijinalHiz;
    }

    public void HariciHareketiAyarla(Vector3 hareketVektoru)
    {
        if (!IsServer) return;
        hariciHareketVektoruNet.Value = hareketVektoru; // NetworkVariable'a ata
    }

    public void HariciHareketiSifirla()
    {
        if (!IsServer) return;
        hariciHareketVektoruNet.Value = Vector3.zero; // NetworkVariable'ı sıfırla
    }

    public Transform GetTransform()
    {
        return transform;
    }

    public void ZiplamayaZorla(float ziplamaGucu)
    {
        if (!IsServer) return;
    }
    /// <summary>
    /// Oyuncuyu eler. Sadece sunucuda çağrılmalı.
    /// </summary>
    public void ElemeYap()
    {
        if (!IsServer) return;
        
        // Zaten elenmişse tekrar eleme
        if (elendi.Value) return;

        elendi.Value = true;
        
        // RenkOyunuManager'a elendiğini haber ver
        if (RenkOyunuManager.Instance != null)
        {
            RenkOyunuManager.Instance.OyuncuEleninceHaberVerServerRpc();
        }

        ElemeClientRpc();
    }

    /// <summary>
    /// Oyuncuyu normal haline döndürür (eleme sonrası resetleme için)
    /// Server tarafından çağrılır ve tüm client'lara gönderilir
    /// </summary>
    public void OyuncuyuResetla()
    {
        if (!IsServer) return;

        // Server'da resetle
        ResetOyuncu();

        // Tüm client'lara da resetleme komutunu gönder
        OyuncuyuResetlaClientRpc();
    }

    [ClientRpc]
    private void OyuncuyuResetlaClientRpc()
    {
        ResetOyuncu();
    }

    // Bu RPC, eleme efektini (ragdoll vb.) oyuncunun kendi ekranında tetikler.
    [ClientRpc]
    private void ElemeClientRpc()
    {
        // Debug.Log("ELENDİN!");
        GetComponent<CharacterController>().enabled = false; // Hareketi durdur

        // --- GÖRÜNÜRLÜĞÜ VE FİZİĞİ KAPAT ---
        if (playerBody != null)
        {
            foreach (var renderer in playerBody.GetComponentsInChildren<Renderer>())
                renderer.enabled = false;
            foreach (var collider in playerBody.GetComponentsInChildren<Collider>())
                collider.enabled = false;
        }

        // --- İSMİ GİZLE ---
        if (playerNameText != null)
        {
            playerNameText.gameObject.SetActive(false);
            Debug.Log($"👻 Oyuncu ismi gizlendi: {playerName.Value}");
        }

        // --- SPECTATE MODU BAŞLAT (sonradan eklenecek) ---
        // Spectate referansları kaldırıldı

        // GameObject'i kapatmak yerine sadece input'ları devre dışı bırak
        // Bu sayede spectate sistemi çalışmaya devam edebilir
        if (IsOwner)
        {
            // Input sistemini kapat
            if (playerControls != null)
            {
                playerControls.Oyuncu.Disable();
            }
        }
    }

    [ClientRpc]
    public void RagdolluAktifEtClientRpc(float ragdollSuresi, ClientRpcParams clientRpcParams = default)
    {
        if (ragdollManager != null)
        {
            ragdollManager.SadeceRagdolluAktifEt(ragdollSuresi);
        }
    }

    // --- YENİ EKLENEN FONKSİYON ---
    // ZeminKarosu'ndan çağrılacak ve anında oyuncu kontrolünü kesecek.
    public void KontrolleriAnindaDevreDisiBirak()
    {
        // Bu fonksiyonun sadece oyuncunun kendi ekranında (owner) çalışmasını sağlıyoruz.
        if (!IsOwner) return;

        // CharacterController, karakterin fiziksel hareketini ve çarpışmalarını yönetir.
        if (controller != null)
        {
            controller.enabled = false;
        }

        // PlayerControls, klavye/gamepad girdilerini dinler.
        if (playerControls != null)
        {
            playerControls.Oyuncu.Disable();
        }

        // Hareketi tamamen durdurmak için mevcut hızı sıfırla.
        _currentVelocity = Vector3.zero;
        playerVelocity = Vector3.zero;
        UpdateAnimationStateServerRpc(false, isGroundedNet.Value); // Animasyonu durma haline getir.
    }

    // --- SETUP COROUTINE'LERİ ---
    private System.Collections.IEnumerator SetupCameraWithDelay()
    {
#if UNITY_EDITOR && DEBUG_OYUNCUKONTROL
        Debug.Log($"OyuncuKontrol: SetupCameraWithDelay başladı - {name} (IsOwner: {IsOwner})");
#endif
        // Kameranın spawn olmasını bekle
        yield return new WaitForSeconds(0.5f);
        int attempts = 0;
        while (attempts < 20) // Maksimum 20 deneme (2 saniye)
        {
            if (InstantHybridCameraController.instance != null && cameraFollowTarget != null)
            {
                InstantHybridCameraController.instance.target = cameraFollowTarget;
                // Debug.Log($"OyuncuKontrol: Kamera hedefi ayarlandı - {name} → {cameraFollowTarget.name}");
                // Kamerayı oyuncunun pozisyonuna yakın bir yere yerleştir
                Vector3 playerPos = transform.position;
                InstantHybridCameraController.instance.transform.position = playerPos + new Vector3(0, 5, -7);
                InstantHybridCameraController.instance.transform.LookAt(playerPos + Vector3.up * 2);
                // Debug.Log($"OyuncuKontrol: Kamera pozisyonu ayarlandı - Pos: {InstantHybridCameraController.instance.transform.position}");
                break;
            }
            else
            {
                attempts++;
                // if (attempts % 5 == 0)
                //     Debug.LogWarning($"OyuncuKontrol: Kamera setup denemesi {attempts}/20 - Camera: {InstantHybridCameraController.instance != null}, Target: {cameraFollowTarget != null}");
                yield return new WaitForSeconds(0.1f);
            }
        }
        if (InstantHybridCameraController.instance == null)
        {
            // Debug.LogWarning($"OyuncuKontrol: Kamera bulunamadı! RenkOyunuSetupManager'a başvuruluyor - {name}");
            // RenkOyunuSetupManager'ı bulmaya çalış
            RenkOyunuSetupManager setupManager = FindFirstObjectByType<RenkOyunuSetupManager>();
            if (setupManager != null)
            {
                // Debug.Log($"OyuncuKontrol: RenkOyunuSetupManager bulundu, setup tetikleniyor - {name}");
                setupManager.SetupPlayerOnSpawn(this);
            }
            else
            {
                // Debug.LogWarning($"OyuncuKontrol: RenkOyunuSetupManager bulunamadı! Sahne: {UnityEngine.SceneManagement.SceneManager.GetActiveScene().name} - {name}");
                // Alternatif: Kamerayı manuel bul ve ayarla
                StartCoroutine(ManualCameraSetup());
            }
        }
        else if (attempts >= 20)
        {
            Debug.LogError($"OyuncuKontrol: Kamera setup başarısız! 20 deneme sonrası vazgeçildi - {name}");
        }
    }

    /// <summary>
    /// Manuel kamera setup - RenkOyunuSetupManager bulunamazsa
    /// </summary>
    private System.Collections.IEnumerator ManualCameraSetup()
    {
        // Debug.Log($"OyuncuKontrol: Manuel kamera setup başlatılıyor - {name}");
        // Biraz daha bekle, belki kamera spawn olur
        yield return new WaitForSeconds(1f);
        // Kamerayı tekrar ara
        InstantHybridCameraController camera = FindFirstObjectByType<InstantHybridCameraController>();
        if (camera != null && cameraFollowTarget != null)
        {
            camera.target = cameraFollowTarget;
            // Debug.Log($"OyuncuKontrol: Manuel kamera setup başarılı - {name}");
        }
        else
        {
            Debug.LogWarning($"OyuncuKontrol: Manuel kamera setup başarısız - Camera: {camera != null}, Target: {cameraFollowTarget != null} - {name}");
        }
    }

    private System.Collections.IEnumerator SetupJoystickWithDelay()
    {
        // Joystick'in spawn olmasını bekle
        yield return new WaitForSeconds(0.1f);
        // Sadece local oyuncu joystick referansını bulsun
        if (IsOwner)
        {
            mobileJoystick = FindFirstObjectByType<FloatingJoystick>();
        }
        // Geliştirme dışında logları kaldır
#if UNITY_EDITOR && DEBUG_JOYSTICK
        if (mobileJoystick != null)
        {
            Debug.Log("Mobil joystick yerel oyuncu için bulundu ve atandı.");
        }
        else
        {
            Debug.LogWarning("Sahnede aktif bir FloatingJoystick bulunamadı. Mobil kontrol devre dışı.");
        }
#endif
    }

    // --- KONTROLLERİ GERİ AÇAN FONKSİYON ---
    public void KontrolleriGeriAc()
    {
        if (!IsOwner) return;

        if (controller != null)
        {
            controller.enabled = true;
        }
        if (playerControls != null)
        {
            playerControls.Oyuncu.Enable();
        }
    }
}
