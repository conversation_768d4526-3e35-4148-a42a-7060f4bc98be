using System.Linq;
using UnityEngine;
using Unity.Netcode;
using System.Collections.Generic;

/// <summary>
/// 3D Lobi için oyuncu verilerini tutan yapı.
/// </summary>
public struct PlayerData3D : INetworkSerializable, System.IEquatable<PlayerData3D>
{
    public ulong ClientId;
    public bool IsReady; // Oyuncunun hazır olup olmadığını belirtir.
    public Unity.Collections.FixedString64Bytes PlayerName; // Oyuncunun ismi

    public void NetworkSerialize<T>(BufferSerializer<T> serializer) where T : IReaderWriter
    {
        serializer.SerializeValue(ref ClientId);
        serializer.SerializeValue(ref IsReady);
        serializer.SerializeValue(ref PlayerName);
    }

    public bool Equals(PlayerData3D other)
    {
        return ClientId == other.ClientId && IsReady == other.IsReady && PlayerName.Equals(other.PlayerName);
    }
}

/// <summary>
/// 3D Lobi'nin ana yöneticisi. Oyuncu bağlantılarını yönetir ve oyunun başlatılmasını kontrol eder.
/// </summary>
public class Lobby3DManager : NetworkBehaviour
{
    [Header("Lobby Settings")]
    [Tooltip("Lobiye katılabilecek maksimum oyuncu sayısı")]
    public int maxPlayers = 8;
    [Tooltip("Oyunun başlayabilmesi için gereken minimum oyuncu sayısı")]
    public int minPlayers = 2;

    [Header("Singleton")]
    public static Lobby3DManager Instance { get; private set; }

    [Header("Player Tracking")]
    // Lobiye bağlı tüm oyuncuların senkronize listesi.
    private NetworkList<PlayerData3D> connectedPlayers;

    // Player spawning NetworkManager tarafından otomatik yapılacak


    // Host'un client ID'sini takip etmek için
    private NetworkVariable<ulong> hostClientId = new NetworkVariable<ulong>();

    // Oyuncu listesi değiştiğinde UI veya diğer sistemleri bilgilendirmek için event.
    public event System.Action OnPlayerListChanged;

    // Oyuncu isimlerini geçici olarak saklamak için dictionary
    private Dictionary<ulong, string> tempNameDict = new Dictionary<ulong, string>();

    private void Awake()
    {
        // Singleton deseni
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return; // Eğer bu nesne kopya ise, devam etme
        }
        Instance = this;
        // DontDestroyOnLoad KALDIRILIYOR - Lobby sadece lobby sahnesinde kalmalı

        // NetworkList'i burada değil, OnNetworkSpawn'da başlat
        if (connectedPlayers == null)
        {
            connectedPlayers = new NetworkList<PlayerData3D>();
        }

    }

    public override void OnNetworkSpawn()
    {

        // Bu mantık sadece sunucuda/host'ta çalışmalıdır.
        if (IsServer)
        {
            // NetworkList'i temizle ve yeniden başlat
            connectedPlayers.Clear();
            tempNameDict.Clear();

            // Host'u lobi başladığında oyuncu listesine ekle.
            string hostPlayerName = PlayerData.PlayerName;
            
            // Host için de tempNameDict'e kaydet (tutarlılık için)
            tempNameDict[NetworkManager.Singleton.LocalClientId] = hostPlayerName;
            
            var hostPlayerData = new PlayerData3D
            {
                ClientId = NetworkManager.Singleton.LocalClientId,
                PlayerName = hostPlayerName,
                IsReady = false
            };

            connectedPlayers.Add(hostPlayerData);
            // SpawnPlayerObject(hostPlayerData); // KALDIRILDI - NetworkManager halletsin

            // Host'un client ID'sini ayarla
            hostClientId.Value = NetworkManager.Singleton.LocalClientId;

            // Bağlantı olaylarına abone ol.
            NetworkManager.Singleton.OnClientConnectedCallback += HandleClientConnected;
            NetworkManager.Singleton.OnClientDisconnectCallback += HandleClientDisconnected;

            // Connection approval sistemini aktif hale getir
            // Önce mevcut callback'leri temizle
            NetworkManager.Singleton.ConnectionApprovalCallback = null;

            // Sonra kendi callback'imizi ayarla
            NetworkManager.Singleton.NetworkConfig.ConnectionApproval = true;
            NetworkManager.Singleton.ConnectionApprovalCallback = HandleApproval;

            // NetworkManager'ın otomatik player spawn'ını AÇ - artık NetworkManager spawn edecek
            NetworkManager.Singleton.NetworkConfig.AutoSpawnPlayerPrefabClientSide = true;
            
            // ÖNEMLİ: NetworkManager'ın default player prefab'ını geçici olarak saklayalım
            var originalPlayerPrefab = NetworkManager.Singleton.NetworkConfig.PlayerPrefab;
            
            // Sahne değişiminde geri yüklemek için kaydet (güvenlik için)
            if (originalPlayerPrefab != null)
            {
                PlayerPrefs.SetString("OriginalPlayerPrefabName", originalPlayerPrefab.name);
            }

            // NetworkManager otomatik spawn yapacak

        }

        // UI'i güncellemek için tüm client'larda liste değişikliklerine abone ol.
        connectedPlayers.OnListChanged += (NetworkListEvent<PlayerData3D> changeEvent) =>
        {
            OnPlayerListChanged?.Invoke();
        };
    }

    


    public override void OnNetworkDespawn()
    {

        // Nesne yok edildiğinde veya despawn olduğunda event aboneliklerini temizle.
        if (IsServer && NetworkManager.Singleton != null)
        {
            NetworkManager.Singleton.OnClientConnectedCallback -= HandleClientConnected;
            NetworkManager.Singleton.OnClientDisconnectCallback -= HandleClientDisconnected;
            NetworkManager.Singleton.ConnectionApprovalCallback -= HandleApproval;
        }

        // Sunucudaysak oyuncu listesini temizle
        if (IsServer)
        {
            if (connectedPlayers != null)
            {
                connectedPlayers.Clear(); // Oyuncu listesini temizle
            }
            tempNameDict.Clear();
        }

        // Instance'ı temizle
        if (Instance == this)
        {
            Instance = null;
        }
    }

    private void HandleClientConnected(ulong clientId)
    {
        // Host kendisini tekrar eklememeli - çünkü OnNetworkSpawn'da zaten eklendi
        if (clientId == NetworkManager.Singleton.LocalClientId)
        {
            return;
        }

        // NetworkManager otomatik spawn yapacak

        // Oyuncu zaten listede mi kontrol et
        foreach (var player in connectedPlayers)
        {
            if (player.ClientId == clientId)
            {
                return;
            }
        }

        // Sunucuda yeni bir client bağlandığında çağrılır.
        if (connectedPlayers.Count >= maxPlayers)
        {
            // Oyuncuyu sunucudan at.
            NetworkManager.Singleton.DisconnectClient(clientId);
            return;
        }

        // Önce geçici isim ver, sonra client'dan gerçek ismi iste
        string finalPlayerName = $"Oyuncu {connectedPlayers.Count + 1}";

        var newPlayerData = new PlayerData3D
        {
            ClientId = clientId,
            PlayerName = finalPlayerName,
            IsReady = false
        };

        connectedPlayers.Add(newPlayerData);
        
        // Client'dan gerçek ismini iste
        RequestPlayerNameClientRpc(new ClientRpcParams 
        { 
            Send = new ClientRpcSendParams 
            { 
                TargetClientIds = new ulong[] { clientId } 
            } 
        });

        // SpawnPlayerObject(newPlayerData); // KALDIRILDI - NetworkManager halletsin
    }

    private void HandleClientDisconnected(ulong clientId)
    {
        // Sunucuda bir client ayrıldığında çağrılır.
        // NetworkManager otomatik olarak player object'ini kaldıracak
        for (int i = 0; i < connectedPlayers.Count; i++)
        {
            if (connectedPlayers[i].ClientId == clientId)
            {
                connectedPlayers.RemoveAt(i);
                break;
            }
        }
    }
    // Spawn sistemi kaldırıldı - NetworkManager otomatik spawn yapacak

    /// <summary>
    /// Oyuncu listesini UI gibi diğer scriptlere vermek için kullanılır.
    /// </summary>
    public NetworkList<PlayerData3D> GetPlayerList()
    {
        return connectedPlayers;
    }
    
    /// <summary>
    /// Host'un client ID'sini döndürür.
    /// </summary>
    public ulong GetHostClientId()
    {
        return hostClientId.Value;
    }

    /// <summary>
    /// Client'dan ismini iste
    /// </summary>
    [ClientRpc]
    private void RequestPlayerNameClientRpc(ClientRpcParams clientRpcParams = default)
    {
        // Client kendi ismini gönder
        if (NetworkManager.Singleton != null)
        {
            string myName = PlayerData.PlayerName;
            SendPlayerNameToServerServerRpc(NetworkManager.Singleton.LocalClientId, myName);
        }
    }
    
    /// <summary>
    /// Client'dan gelen isim güncellemesi
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    private void SendPlayerNameToServerServerRpc(ulong clientId, string playerName, ServerRpcParams serverRpcParams = default)
    {
        // Oyuncu listesinde güncelle
        for (int i = 0; i < connectedPlayers.Count; i++)
        {
            if (connectedPlayers[i].ClientId == clientId)
            {
                PlayerData3D playerData = connectedPlayers[i];
                playerData.PlayerName = playerName;
                connectedPlayers[i] = playerData;
                break;
            }
        }
    }

    /// <summary>
    /// Oyuncu ismini günceller (SADECE RUNTIME İSİM DEĞİŞİKLİĞİ İÇİN - artık başlangıçta kullanılmıyor)
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
public void UpdatePlayerNameServerRpc(ulong clientId, string newName, ServerRpcParams serverRpcParams = default)
    {
        if (!IsServer) return;
        
        // Sadece kendisi değiştirebilir
        var senderId = serverRpcParams.Receive.SenderClientId;
        if (clientId != senderId) return;
        
        for (int i = 0; i < connectedPlayers.Count; i++)
        {
            if (connectedPlayers[i].ClientId == clientId)
            {
                PlayerData3D playerData = connectedPlayers[i];
                playerData.PlayerName = newName;
                connectedPlayers[i] = playerData;
                break;
            }
        }
    }
    
    /// <summary>
    /// Oyuncunun hazır durumunu değiştirir.
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    public void ToggleReadyStatusServerRpc(ulong clientId)
    {
        if (!IsServer) return;

        for (int i = 0; i < connectedPlayers.Count; i++)
        {
            if (connectedPlayers[i].ClientId == clientId)
            {
                PlayerData3D playerData = connectedPlayers[i];
                playerData.IsReady = !playerData.IsReady;
                connectedPlayers[i] = playerData;
                break;
            }
        }
        CheckAllPlayersReady();
    }

    /// <summary>
    /// Tüm oyuncuların hazır olup olmadığını kontrol eder ve event tetikler.
    /// </summary>
    private void CheckAllPlayersReady()
    {
        if (!IsServer) return;

        bool allClientsReady = true;
        int clientCount = 0;
        
        foreach (var player in connectedPlayers)
        {
            // Host değilse (client ise) kontrol et
            if (player.ClientId != NetworkManager.Singleton.LocalClientId)
            {
                clientCount++;
                if (!player.IsReady)
                {
                    allClientsReady = false;
                    break;
                }
            }
        }
        
    }

    public void ResetLobbyState()
    {
        if (IsServer)
        {
            if (connectedPlayers != null)
            {
                connectedPlayers.Clear();
            }
            tempNameDict.Clear();
        }
    }

    /// <summary>
    /// Oyunu başlatmak için ServerRpc. Sadece host tarafından çağrılmalı.
    /// Artık direkt oyun başlatmak yerine voting ekranını açar.
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    public void StartGameServerRpc(ulong clientId)
    {
        Debug.Log($"🎮 [Lobby3DManager] StartGameServerRpc çağrıldı. ClientId: {clientId}");
        
        // Oyunu başlatan kişinin host olup olmadığını ve yeterli oyuncu olup olmadığını kontrol et.
        if (IsServer && clientId == NetworkManager.Singleton.LocalClientId)
        {
            Debug.Log($"🎮 [Lobby3DManager] Host kontrolü geçti. Oyuncu sayısı: {connectedPlayers.Count}, Min: {minPlayers}");
            
            if (connectedPlayers.Count < minPlayers)
            {
                Debug.LogWarning($"⚠️ [Lobby3DManager] Yetersiz oyuncu! Mevcut: {connectedPlayers.Count}, Gerekli: {minPlayers}");
                return;
            }

            // MinigameSelection sahnesine geç (voting sistemi orada)
            Debug.Log("🎯 [Lobby3DManager] MinigameSelection sahnesine yönlendiriliyor...");

            if (NetworkManager.Singleton != null && NetworkManager.Singleton.SceneManager != null)
            {
                // Önce MinigameSelection sahnesini dene
                try
                {
                    NetworkManager.Singleton.SceneManager.LoadScene("MinigameSelection", UnityEngine.SceneManagement.LoadSceneMode.Single);
                    Debug.Log("✅ [Lobby3DManager] MinigameSelection sahnesine geçiş başlatıldı");
                }
                catch
                {
                    // MinigameSelection sahnesi yoksa fallback
                    Debug.LogWarning("⚠️ [Lobby3DManager] MinigameSelection sahnesi bulunamadı!");
                    if (UniversalSceneManager.Instance != null)
                    {
                        Debug.Log("🔄 [Lobby3DManager] Fallback: RenkOyunu'na geçiliyor...");
                        UniversalSceneManager.Instance.ManuelSahneGecisi("RenkOyunu");
                    }
                }
            }
            else
            {
                Debug.LogError("❌ [Lobby3DManager] NetworkManager veya SceneManager bulunamadı!");
            }
        }
        else
        {
            Debug.LogWarning($"[Lobby3DManager] Oyunu sadece host başlatabilir. Deneyen: {clientId}, Host: {NetworkManager.Singleton.LocalClientId}");
        }
    }



    private void HandleApproval(NetworkManager.ConnectionApprovalRequest request, NetworkManager.ConnectionApprovalResponse response)
    {
        try
        {
            // Lobby dolu mu kontrol et
            if (connectedPlayers.Count >= maxPlayers)
            {
                response.Approved = false;
                response.Reason = "Lobby dolu!";
                return;
            }

            string playerName = "";
            if (request.Payload != null && request.Payload.Length > 0)
            {
                playerName = System.Text.Encoding.UTF8.GetString(request.Payload);
            }
            else
            {
                playerName = $"Oyuncu {tempNameDict.Count + 1}";
            }

            response.Approved = true;
            response.CreatePlayerObject = true; // NetworkManager spawn etsin
            tempNameDict[request.ClientNetworkId] = playerName; // cache the name for later use
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[Lobby3DManager] ❌ HandleApproval hatası: {ex.Message}");
            response.Approved = false;
            response.Reason = "Sunucu hatası!";
        }
    }
}
