Shader "Custom/Mobile/LavaUltraOptimized"
{
    Properties
    {
        _MainTex ("Lava Atlas (RGB=Main, A=Noise)", 2D) = "white" {}
        _LavaColor ("Lava Color", Color) = (1, 0.3, 0, 1)
        _HotSpotColor ("Hot Spot Color", Color) = (1, 1, 0, 1)
        _FlowSpeed ("Flow Speed", Range(0, 2)) = 0.5
        _Intensity ("Intensity", Range(0, 3)) = 1.5
        
        // Soft Edge Properties
        _EdgeSoftness ("Edge Softness", Range(0, 1)) = 0.1
        _EdgePower ("Edge Power", Range(0.5, 8)) = 3.0
        _EdgeColor ("Edge Color", Color) = (1.2, 0.4, 0.1, 1)
        
        // Instancing support for batching
        [PerRendererData] _VariationOffset ("Variation Offset", Vector) = (0,0,0,0)
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderType"="Opaque" 
            "Queue"="Geometry"
            "DisableBatching"="False"
        }
        
        LOD 100
        
        Pass
        {
            Tags { "LightMode" = "UniversalForward" }
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0
            
            // Ultra batch optimization - FIXED
            #pragma multi_compile_instancing
            #pragma instancing_options assumeuniformscaling nomatrices nolightprobe nolightmap
            #pragma multi_compile _ UNITY_SINGLE_PASS_STEREO
            #pragma skip_variants LIGHTMAP_ON DYNAMICLIGHTMAP_ON LIGHTMAP_SHADOW_MIXING SHADOWS_SHADOWMASK
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float2 uv : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };
            
            struct Varyings
            {
                float4 positionHCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 normalWS : TEXCOORD1;
                float3 viewDirWS : TEXCOORD2;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };
            
            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            
            CBUFFER_START(UnityPerMaterial)
                float4 _MainTex_ST;
                half4 _LavaColor;
                half4 _HotSpotColor;
                half4 _EdgeColor;
                half _FlowSpeed;
                half _Intensity;
                half _EdgeSoftness;
                half _EdgePower;
            CBUFFER_END
            
            // Per-instance data for variation
            UNITY_INSTANCING_BUFFER_START(Props)
                UNITY_DEFINE_INSTANCED_PROP(float4, _VariationOffset)
            UNITY_INSTANCING_BUFFER_END(Props)
            
            Varyings vert(Attributes input)
            {
                Varyings output;
                
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);
                
                // Ultra-fast vertex transform
                output.positionHCS = TransformObjectToHClip(input.positionOS.xyz);
                output.uv = TRANSFORM_TEX(input.uv, _MainTex);
                
                // Calculate world space normal and view direction for soft edges
                output.normalWS = TransformObjectToWorldNormal(input.normalOS);
                float3 positionWS = TransformObjectToWorld(input.positionOS.xyz);
                output.viewDirWS = GetWorldSpaceViewDir(positionWS);
                
                return output;
            }
            
            half4 frag(Varyings input) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(input);
                
                // Get per-instance variation
                float4 variation = UNITY_ACCESS_INSTANCED_PROP(Props, _VariationOffset);
                
                // Ultra-optimized single texture sample with atlas
                float2 animUV = input.uv + _Time.y * _FlowSpeed * 0.1 + variation.xy * 0.1;
                half4 texSample = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, animUV);
                
                // Use alpha channel as noise (atlas technique)
                half noise = texSample.a;
                
                // Simple flow pattern
                half flow = sin(noise * 6.28 + _Time.y * _FlowSpeed + variation.z) * 0.5 + 0.5;
                
                // Ultra-fast color mixing
                half3 lavaColor = lerp(_LavaColor.rgb, _HotSpotColor.rgb, flow);
                lavaColor *= texSample.rgb * _Intensity;
                
                // Soft Edge Effect (Fresnel-based)
                float3 normalWS = normalize(input.normalWS);
                float3 viewDirWS = normalize(input.viewDirWS);
                half fresnel = 1.0 - saturate(dot(normalWS, viewDirWS));
                fresnel = pow(fresnel, _EdgePower);
                
                // Apply soft edge blending
                half3 edgeEffect = lerp(lavaColor, _EdgeColor.rgb, fresnel * _EdgeSoftness);
                
                return half4(edgeEffect, 1);
            }
            ENDHLSL
        }
    }
    
    Fallback Off
}