using UnityEngine;
using Unity.Netcode;

/// <summary>
/// UniversalSceneManager'ı otomatik olarak kuran bootstrap scripti.
/// Bu script'i sadece MainMenu sahnesine ekleyin, tüm sahnelerde çalışacak.
/// </summary>
public class AutoSceneManagerBootstrap : MonoBehaviour
{
    [Header("Otomatik Kurulum Ayarları")]
    [Tooltip("UniversalSceneManager'ı otomatik olarak kur")]
    public bool otomatikKurulum = true;
    
    [Header("Sahne Ayarları")]
    [Tooltip("Oynanacak mini oyun sahneleri")]
    public string[] miniOyunSahneleri = {
        "RenkOyunu",
        "fakeblock_Scene"
    };
    
    [Tooltip("Ana menü sahne adı")]
    public string anaMenuSahnesi = "MainMenu";
    
    [Tooltip("Sahne geçiş bekleme süresi")]
    public float sahneGecisiBeklemeSuresi = 3f;

    [Header("Debug")]
    [Tooltip("Debug mesajlarını göster")]
    public bool showDebugLogs = true;

    private void Start()
    {
        if (otomatikKurulum)
        {
            KurulumYap();
        }
    }

    public void KurulumYap()
    {
        // Zaten var mı kontrol et
        if (UniversalSceneManager.Instance != null)
        {
            if (showDebugLogs)
                Debug.Log("✅ AutoSceneManagerBootstrap: UniversalSceneManager zaten mevcut.");
            return;
        }

        if (showDebugLogs)
            Debug.Log("🚀 AutoSceneManagerBootstrap: UniversalSceneManager kuruluyor...");

        // UniversalSceneManager oluştur
        OlusturUniversalSceneManager();
    }

    private void OlusturUniversalSceneManager()
    {
        // Yeni GameObject oluştur
        GameObject managerObj = new GameObject("UniversalSceneManager");
        
        // UniversalSceneManager component'ini ekle
        UniversalSceneManager manager = managerObj.AddComponent<UniversalSceneManager>();
        
        // Ayarları yap
        manager.miniOyunSahneleri = miniOyunSahneleri;
        manager.anaMenuSahnesi = anaMenuSahnesi;
        manager.sahneGecisiBeklemeSuresi = sahneGecisiBeklemeSuresi;
        
        // NetworkObject ekle (eğer NetworkManager varsa)
        if (NetworkManager.Singleton != null)
        {
            NetworkObject networkObject = managerObj.AddComponent<NetworkObject>();
            networkObject.DontDestroyWithOwner = true;
            
            // Server'da spawn et
            if (NetworkManager.Singleton.IsServer)
            {
                networkObject.Spawn(true);
                if (showDebugLogs)
                    Debug.Log("✅ AutoSceneManagerBootstrap: UniversalSceneManager NetworkObject olarak spawn edildi.");
            }
        }
        else
        {
            if (showDebugLogs)
                Debug.Log("ℹ️ AutoSceneManagerBootstrap: NetworkManager yok, offline modda çalışıyor.");
        }
        
        if (showDebugLogs)
            Debug.Log("✅ AutoSceneManagerBootstrap: UniversalSceneManager başarıyla oluşturuldu!");
    }

    /// <summary>
    /// Oyun başlatıldığında oynanan sahneleri sıfırla
    /// </summary>
    public void YeniOyunBaslat()
    {
        if (UniversalSceneManager.Instance != null)
        {
            UniversalSceneManager.Instance.DebugOynananSahneleriSifirla();
            if (showDebugLogs)
                Debug.Log("🔄 AutoSceneManagerBootstrap: Yeni oyun için oynanan sahneler sıfırlandı.");
        }
    }

    [ContextMenu("Manuel Kurulum")]
    public void ManuelKurulum()
    {
        KurulumYap();
    }

    [ContextMenu("Yeni Oyun Başlat")]
    public void ManuelYeniOyunBaslat()
    {
        YeniOyunBaslat();
    }

    [ContextMenu("Durum Kontrolü")]
    public void DurumKontrolu()
    {
        Debug.Log("=== AUTO SCENE MANAGER BOOTSTRAP DURUM KONTROLÜ ===");

        if (UniversalSceneManager.Instance != null)
        {
            Debug.Log("✅ UniversalSceneManager: Mevcut");
        }
        else
        {
            Debug.LogWarning("❌ UniversalSceneManager: Bulunamadı!");
        }
        
        Debug.Log("=== KONTROL TAMAMLANDI ===");
    }

    private void OnDestroy()
    {
        if (showDebugLogs)
            Debug.Log("🗑️ AutoSceneManagerBootstrap: Destroy ediliyor...");
    }
}
