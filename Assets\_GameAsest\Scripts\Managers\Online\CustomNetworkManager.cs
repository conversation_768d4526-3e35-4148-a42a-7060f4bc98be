using System.Collections.Generic;
using Unity.Netcode;
using UnityEngine;

public class CustomNetworkManager : NetworkBehaviour
{
    public static CustomNetworkManager Singleton { get; private set; }

    // KENDİ BAŞLANGIÇ NOKTASI LİSTESİNİ GERİ GETİRDİK.
    [Header("Başlangıç Noktaları")]
    [Tooltip("Oyuncuların oyuna İLK GİRDİĞİNDE doğacağı noktaları buraya sürükleyin.")]
    [SerializeField] private Transform[] initialSpawnPoints;
    private int nextSpawnPointIndex = 0;

    // Public metod: Spawn noktalarını dışarıdan ayarlamak için
    public void SetSpawnPoints(Transform[] spawnPoints)
    {
        initialSpawnPoints = spawnPoints;
        nextSpawnPointIndex = 0; // Index'i sıfırla
    }

    // Public property: Spawn noktalarını okumak için
    public Transform[] GetSpawnPoints()
    {
        return initialSpawnPoints;
    }

    private void Awake()
    {
        if (Singleton != null && Singleton != this)
        {
            Destroy(gameObject);
            return;
        }
        Singleton = this;
    }

    public override void OnNetworkSpawn()
    {
        if (IsServer)
        {
            // LOBBY SAHNESI DEĞİLSE ConnectionApproval'ı aktif et
            string currentSceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
            if (currentSceneName != "3DLobby" && currentSceneName != "Lobby")
            {
                // Oyun sahnesinde AutoSpawn'ı tekrar aç
                NetworkManager.Singleton.NetworkConfig.AutoSpawnPlayerPrefabClientSide = true;
                
                NetworkManager.Singleton.NetworkConfig.ConnectionApproval = true;
                NetworkManager.Singleton.ConnectionApprovalCallback = ConnectionApproval; // += değil = kullan

                // Sahne değişikliği sonrası oyuncuları kontrol et
                StartCoroutine(CheckAndSpawnPlayersAfterSceneLoad());
            }
            else
            {
                // Lobby sahnesinde hiçbir şey yapma, Lobby3DManager halletsin
                // ConnectionApproval'ı kapat
                NetworkManager.Singleton.NetworkConfig.ConnectionApproval = false;
                NetworkManager.Singleton.ConnectionApprovalCallback = null;
                return;
            }
        }
    }

    private System.Collections.IEnumerator CheckAndSpawnPlayersAfterSceneLoad()
    {
        yield return new WaitForSeconds(2f);

        string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        if (currentScene != "Lobby")
        {
            // Oyuncu objesi olmayan client'ları kontrol et
            foreach (var clientId in NetworkManager.Singleton.ConnectedClientsIds)
            {
                if (!NetworkManager.Singleton.ConnectedClients[clientId].PlayerObject)
                {
                    // Oyuncu spawn et
                    SpawnPlayerForClient(clientId);
                }
            }
        }
    }

    private void SpawnPlayerForClient(ulong clientId)
    {
        // Zaten spawn edilmiş mi kontrol et
        if (NetworkManager.Singleton.ConnectedClients.ContainsKey(clientId) &&
            NetworkManager.Singleton.ConnectedClients[clientId].PlayerObject != null)
        {
            return;
        }

        if (NetworkManager.Singleton.NetworkConfig.PlayerPrefab != null)
        {
            // Spawn pozisyonu belirle
            Vector3 spawnPos = GetSpawnPosition();

            // Oyuncu objesi oluştur
            GameObject playerInstance = Instantiate(NetworkManager.Singleton.NetworkConfig.PlayerPrefab, spawnPos, Quaternion.identity);
            NetworkObject networkObject = playerInstance.GetComponent<NetworkObject>();

            if (networkObject != null)
            {
                networkObject.SpawnAsPlayerObject(clientId);
            }
            else
            {
                Destroy(playerInstance);
            }
        }
    }

    private Vector3 GetSpawnPosition()
    {
        if (initialSpawnPoints != null && initialSpawnPoints.Length > 0 && initialSpawnPoints[0] != null)
        {
            Transform spawnPoint = initialSpawnPoints[nextSpawnPointIndex];
            nextSpawnPointIndex = (nextSpawnPointIndex + 1) % initialSpawnPoints.Length;
            return spawnPoint.position;
        }

        return new Vector3(0, 2, 0);
    }

    public override void OnNetworkDespawn()
    {
        if (IsServer && NetworkManager.Singleton != null)
        {
            NetworkManager.Singleton.ConnectionApprovalCallback -= ConnectionApproval;
        }
    }

private void ConnectionApproval(NetworkManager.ConnectionApprovalRequest request, NetworkManager.ConnectionApprovalResponse response)
    {
        string currentSceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;

        // Lobby sahnesinde özel kontroller - Şimdilik devre dışı (yeni lobi sistemi gelecek)
        /*
        if (currentSceneName == "Lobby")
        {
            // SimpleLobbyManager'dan lobby durumunu kontrol et
            SimpleLobbyManager lobbyManager = FindObjectOfType<SimpleLobbyManager>();
            if (lobbyManager != null)
            {
                // Lobby dolu mu kontrolü
                if (lobbyManager.oyuncuListesi.Count >= lobbyManager.maksimumOyuncuSayisi)
                {
                    response.Approved = false;
                    response.Reason = "Lobi dolu!";
                    Debug.Log($"CustomNetworkManager: Bağlantı reddedildi - Lobi dolu");
                    return;
                }

                // Oyun başladı mı kontrolü
                if (lobbyManager.IsGameStarting())
                {
                    response.Approved = false;
                    response.Reason = "Oyun zaten başladı!";
                    Debug.Log($"CustomNetworkManager: Bağlantı reddedildi - Oyun başladı");
                    return;
                }
            }
        }
        */

        if (NetworkManager.Singleton.NetworkConfig.PlayerPrefab == null)
        {
            response.Approved = false;
            response.Reason = "Sunucuda oyuncu prefab'ı ayarlanmamış.";
            return;
        }

        response.Approved = true;
        response.CreatePlayerObject = false; // Lobby3DManager kendi spawn sistemini kullanıyor

        // ARTIK SPAWNMANAGER'A BAĞLI DEĞİLİZ, KENDİ LİSTEMİZİ KULLANIYORUZ.
        if (initialSpawnPoints != null && initialSpawnPoints.Length > 0 && initialSpawnPoints[0] != null)
        {
            Transform spawnPoint = initialSpawnPoints[nextSpawnPointIndex];
            response.Position = spawnPoint.position;
            response.Rotation = spawnPoint.rotation;

            nextSpawnPointIndex = (nextSpawnPointIndex + 1) % initialSpawnPoints.Length;
        }
        else
        {
            response.Position = new Vector3(0, 2, 0); // Platformun üstünde
            response.Rotation = Quaternion.identity;
        }
    }
}