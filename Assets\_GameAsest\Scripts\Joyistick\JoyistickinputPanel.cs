using UnityEngine;
using UnityEngine.EventSystems; // Bu namespace'i eklemeyi unutmayın!

public class CameraRotationWithUIGuard : MonoBehaviour
{
    [Header("Kamera Dönüş Ayarları")]
    public float rotationSpeed = 200f; // Kamera dönüş hızı
    public bool invertY = false;      // Y ekseninde ters döndürme

    [Header("Dikey Açı Limitleri")]
    public float minVerticalAngle = -90f; // Minimum dikey açı (aşağı bakma)
    public float maxVerticalAngle = 90f;  // Maksimum dikey açı (yukarı bakma)

    private float rotationX = 0f; // X eksenindeki (dikey) mevcut dönüş
    private float rotationY = 0f; // Y eksenindeki (yatay) mevcut dönüş

    void Start()
    {
        // Başlangıçta kameranın mevcut dönüşünü al
        Vector3 currentRotation = transform.eulerAngles;
        rotationY = currentRotation.y;
        rotationX = currentRotation.x;

        // eulerAngles 0-360 aras<PERSON> değerler döndürebilir, -90/90 aralığına çevirelim
        if (rotationX > 180) rotationX -= 360;
    }

    void Update()
    {
        // ------------- KORUYUCU KISIM BAŞLANGICI -------------
        // Sahnenizde bir EventSystem objesi olduğundan emin olun!
        if (EventSystem.current == null)
        {
            Debug.LogWarning("Sahnenizde bir EventSystem bulunamadı. UI etkileşimleri doğru çalışmayabilir.");
            return; // EventSystem yoksa devam etme
        }

        // Fare ile UI üzerine gelme kontrolü
        if (EventSystem.current.IsPointerOverGameObject())
        {
            // Fare bir UI elemanının üzerinde, kamera döndürmeyi durdur.
            return;
        }

        // Dokunmatik ekran ile UI üzerine gelme kontrolü (mobil cihazlar için)
        // Her bir dokunuşu kontrol ediyoruz
        for (int i = 0; i < Input.touchCount; i++)
        {
            // Input.GetTouch(i).fingerId: Hangi parmağın dokunduğunu belirten ID.
            // IsPointerOverGameObject() metodu bu ID'yi alabilir.
            if (EventSystem.current.IsPointerOverGameObject(Input.GetTouch(i).fingerId))
            {
                // Bir parmak bir UI elemanının üzerinde, kamera döndürmeyi durdur.
                return;
            }
        }
        // ------------- KORUYUCU KISIM SONU -------------

        // Sadece fare sol tuşuna basıldığında veya dokunmatik ekran algılandığında döndür
        bool inputActive = false;

        // Fare girişi
        if (Input.GetMouseButton(0)) // Sol fare tuşu basılı mı?
        {
            inputActive = true;
            // Fare hareketine göre Y (yatay) dönüşü güncelle
            rotationY += Input.GetAxis("Mouse X") * rotationSpeed * Time.deltaTime;
            
            // Fare hareketine göre X (dikey) dönüşü güncelle
            float mouseYDelta = Input.GetAxis("Mouse Y") * rotationSpeed * Time.deltaTime;
            rotationX += invertY ? mouseYDelta : -mouseYDelta; // Y eksenini ters çevir
        }
        // Dokunmatik ekran girişi (mobil)
        else if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Moved)
        {
            inputActive = true;
            Touch touch = Input.GetTouch(0); // İlk dokunuşu al (genellikle kamera için tek dokunuş kullanılır)

            // Dokunma hareketine göre Y (yatay) dönüşü güncelle
            rotationY += touch.deltaPosition.x * rotationSpeed * Time.deltaTime * 0.1f; // Dokunma hassasiyetini ayarla
            
            // Dokunma hareketine göre X (dikey) dönüşü güncelle
            float touchYDelta = touch.deltaPosition.y * rotationSpeed * Time.deltaTime * 0.1f;
            rotationX += invertY ? touchYDelta : -touchYDelta;
        }

        if (inputActive)
        {
            // Dikey dönüşü limitler içinde tut
            rotationX = Mathf.Clamp(rotationX, minVerticalAngle, maxVerticalAngle);

            // Kameranın yeni dönüşünü uygula
            transform.rotation = Quaternion.Euler(rotationX, rotationY, 0);
        }
    }
}