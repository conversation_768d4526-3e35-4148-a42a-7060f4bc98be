using UnityEngine;
using UnityEngine.InputSystem; // YENİ SİSTEM İÇİN BU SATIR ŞART!
using UnityEngine.EventSystems; // EventSystem kontrolü için bu da gerekli

/// <summary>
/// YENİ INPUT SİSTEMİ ile çalışan PC (fare/gamepad) ve Mobil (dokunmatik) için tasarlanmış hibrit bir kamera kontrolcüsüdür.
/// </summary>
public class InstantHybridCameraController : MonoBehaviour
{
    // Bu script'e diğer script'lerden kolayca erişmek için statik bir referans oluşturuyoruz.
    public static InstantHybridCameraController instance;
    // --- Inspector <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hepsi aynı kalıyor ---
    [Header("Temel Ayarlar")]
    public Transform target;
    [Header("Mesafe ve Yükseklik Ayarları")]
    public float distance = 7.0f;
    public float height = 5.0f;
    public float lookAtOffset = 1.0f;
    [Header("Kontrol Hassasiyeti")]
    public float sensitivity = 50f; // DEĞİŞTİ: Artık tek bir genel hassasiyet var
    private const string HASSASIYET_KAYIT_ANAHTARI = "KameraHassasiyeti";
    [Header("Dikey Açı Sınırları (Pitch)")]
    public float minVerticalAngle = -20f;
    public float maxVerticalAngle = 75f;
    [Header("Otomatik Sıfırlama")]
    public bool enableAutoReset = true;
    public float autoResetDelay = 4.0f;
    public float defaultPitch = 15.0f;
    [Header("Çarpışma Kontrolü (Collision)")]
    public LayerMask collisionLayers = 1;
    public float collisionCushion = 0.2f;

    // --- YENİ DEĞİŞKENLER ---
    private PlayerControls playerControls;
    private InputAction lookAction;
    private InputAction lockCursorAction;

    private float _yaw = 0.0f;
    private float _pitch = 0.0f;
    private float _lastInputTime;

    private void Awake()
    {
        // Singleton pattern: Bu script'in tek bir kopyası olduğundan emin ol.
        if (instance == null)
        {
            instance = this;
        }
        else
        {
            // Eğer sahnede zaten bir tane varsa, bu yenisini yok et.
            Destroy(gameObject);
            return;
        }

        // YENİ: Kontrol şemasını ve eylemleri hazırlıyoruz
        playerControls = new PlayerControls();
        lookAction = playerControls.Oyuncu.Look;
        lockCursorAction = playerControls.Oyuncu.LockCursor; // LockCursor eylemini atıyoruz
    }

    private void OnEnable()
    {
        playerControls.Oyuncu.Enable();

        // YENİ: İmleç kilitleme eylemlerini dinliyoruz
        lockCursorAction.started += _ => LockCursor();
        lockCursorAction.canceled += _ => UnlockCursor();
    }

    private void OnDisable()
    {
        playerControls.Oyuncu.Disable();

        // YENİ: Eylem dinleyicilerini kaldırıyoruz
        lockCursorAction.started -= _ => LockCursor();
        lockCursorAction.canceled -= _ => UnlockCursor();
    }

    void Start()
    {
        if (target == null)
        {
            // Local player'ı bul ve target'ı ayarla
            StartCoroutine(FindLocalPlayerTarget());
            return;
        }

        _yaw = target.eulerAngles.y;
        _pitch = defaultPitch;
        _lastInputTime = Time.time;
        UnlockCursor(); // Başlangıçta imleç serbest olsun

        // Kaydedilmiş hassasiyet değerini yükle
        KaydedilmisHassasiyetiYukle();
    }

    private System.Collections.IEnumerator FindLocalPlayerTarget()
    {
        int attempts = 0;
        while (attempts < 30 && target == null) // 3 saniye bekle
        {
            // OyuncuKontrol.LocalInstance'ı kontrol et
            if (OyuncuKontrol.LocalInstance != null && OyuncuKontrol.LocalInstance.cameraFollowTarget != null)
            {
                target = OyuncuKontrol.LocalInstance.cameraFollowTarget;

                _yaw = target.eulerAngles.y;
                _pitch = defaultPitch;
                _lastInputTime = Time.time;

                // Kaydedilmiş hassasiyet değerini yükle
                KaydedilmisHassasiyetiYukle();
                break;
            }

            // Tüm oyuncular arasında ara
            OyuncuKontrol[] allPlayers = Object.FindObjectsByType<OyuncuKontrol>(FindObjectsSortMode.None);
            foreach (var player in allPlayers)
            {
                if (player.IsOwner && player.cameraFollowTarget != null)
                {
                    target = player.cameraFollowTarget;

                    _yaw = target.eulerAngles.y;
                    _pitch = defaultPitch;
                    _lastInputTime = Time.time;

                    // Kaydedilmiş hassasiyet değerini yükle
                    KaydedilmisHassasiyetiYukle();
                    break;
                }
            }

            if (target != null) break;

            attempts++;
            yield return new WaitForSeconds(0.1f);
        }

        if (target == null)
        {
            // Varsayılan kamera pozisyonu ayarla
            transform.position = new Vector3(0, 10, -10);
            transform.LookAt(Vector3.zero);

            // Tekrar deneme coroutine'ini başlat
            StartCoroutine(RetryFindTarget());
        }
    }

    private System.Collections.IEnumerator RetryFindTarget()
    {
        // 5 saniye bekle ve tekrar dene
        yield return new WaitForSeconds(5f);

        // Local player'ı tekrar ara
        if (OyuncuKontrol.LocalInstance != null && OyuncuKontrol.LocalInstance.cameraFollowTarget != null)
        {
            target = OyuncuKontrol.LocalInstance.cameraFollowTarget;

            _yaw = target.eulerAngles.y;
            _pitch = defaultPitch;
            _lastInputTime = Time.time;

            // Kaydedilmiş hassasiyet değerini yükle
            KaydedilmisHassasiyetiYukle();
        }
        else
        {
            // Hala bulunamadıysa tekrar dene
            StartCoroutine(RetryFindTarget());
        }
    }

    // KALDIRILDI: Update() metodu artık imleç kontrolü için gerekli değil,
    // çünkü bu işi OnEnable/OnDisable içindeki eylemlerle hallediyoruz.


    void LateUpdate()
    {
        if (target == null)
        {
            // Target yoksa varsayılan kamera davranışı
            // Debug.LogWarning("InstantHybridCameraController: Target null, LateUpdate atlandı");
            return;
        }

        HandlePcInputAndRotation();
        CalculatePositionAndCollision();
    }

    // YENİ VE DIŞARIDAN ERİŞİLEBİLİR METOT
    // Bu metot, dokunmatik panelden gelen veriyi işleyecek.
    public void RotateCameraFromTouch(Vector2 touchDelta)
    {
        // Dokunma hassasiyetini burada ayarlayabilirsiniz, 0.1f iyi bir başlangıç.
        float touchSensitivityFactor = 0.1f;
        Vector2 lookInput = touchDelta * touchSensitivityFactor;

        _lastInputTime = Time.time;
        _yaw += lookInput.x * sensitivity * Time.deltaTime;
        _pitch -= lookInput.y * sensitivity * Time.deltaTime;

        // Pitch'i sınırla
        _pitch = Mathf.Clamp(_pitch, minVerticalAngle, maxVerticalAngle);
    }

    private void HandlePcInputAndRotation()
    {
        // ⚠️ GÜVENLİK: Target null kontrolü ⚠️
        if (target == null)
        {
            return;
        }

        // SADECE PC/GAMEPAD GİRDİSİNİ OKU
        Vector2 lookInput = lookAction.ReadValue<Vector2>();

        // DOKUNMATİK KONTROL BLOĞU BURADAN TAMAMEN KALDIRILDI!

        bool isInteracting = lookInput.magnitude > 0.01f;

        if (isInteracting)
        {
            _lastInputTime = Time.time;
            _yaw += lookInput.x * sensitivity * Time.deltaTime;
            _pitch -= lookInput.y * sensitivity * Time.deltaTime;
        }
        else if (enableAutoReset && Time.time - _lastInputTime > autoResetDelay)
        {
            _yaw = target.eulerAngles.y;
            _pitch = defaultPitch;
        }

        _pitch = Mathf.Clamp(_pitch, minVerticalAngle, maxVerticalAngle);
    }

    // Bu fonksiyon hiç değişmedi, olduğu gibi kalıyor.
    private void CalculatePositionAndCollision()
    {
        // ⚠️ GÜVENLİK: Target null kontrolü ⚠️
        if (target == null)
        {
            return;
        }

        Quaternion desiredRotation = Quaternion.Euler(_pitch, _yaw, 0);
        Vector3 lookAtPoint = target.position + Vector3.up * lookAtOffset;
        Vector3 desiredPosition = lookAtPoint - (desiredRotation * Vector3.forward * distance);

        RaycastHit hit;
        if (Physics.Linecast(lookAtPoint, desiredPosition, out hit, collisionLayers))
        {
            desiredPosition = hit.point + hit.normal * collisionCushion;
        }

        transform.position = desiredPosition;
        transform.rotation = desiredRotation;
    }
    
    // YENİ: İmleç yönetimi için yardımcı fonksiyonlar
    private void LockCursor()
    {
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    private void UnlockCursor()
    {
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
    }

    // YENİ: Hassasiyet ayarlama metodları
    /// <summary>
    /// Kamera hassasiyetini ayarlar ve kaydeder
    /// </summary>
    /// <param name="yeniHassasiyet">Yeni hassasiyet değeri</param>
    public void HassasiyetAyarla(float yeniHassasiyet)
    {
        sensitivity = yeniHassasiyet;
        PlayerPrefs.SetFloat(HASSASIYET_KAYIT_ANAHTARI, yeniHassasiyet);
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Kaydedilmiş hassasiyet değerini yükler
    /// </summary>
    private void KaydedilmisHassasiyetiYukle()
    {
        if (PlayerPrefs.HasKey(HASSASIYET_KAYIT_ANAHTARI))
        {
            sensitivity = PlayerPrefs.GetFloat(HASSASIYET_KAYIT_ANAHTARI, sensitivity);
        }
    }

    /// <summary>
    /// Mevcut hassasiyet değerini döndürür
    /// </summary>
    /// <returns>Mevcut hassasiyet değeri</returns>
    public float MevcutHassasiyetiAl()
    {
        return sensitivity;
    }
}